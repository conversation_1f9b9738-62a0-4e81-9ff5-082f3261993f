# Edit Post System - Manual Testing Guide

## Overview
This guide provides comprehensive manual testing procedures for the new unified edit post system.

## Prerequisites
- User must be logged in
- User must have created posts of different types (general, blog, event, marketplace)
- Access to the dashboard/content management section

## Test Scenarios

### 1. General Post Editing

**Test Steps:**
1. Navigate to Dashboard → My Content → Posts tab
2. Find a general post and click the "Edit" button (three dots menu)
3. Verify the EditPostDialog opens with correct title "Edit Post"
4. Verify form fields are pre-populated with existing data:
   - Content field shows original content
   - Tags are displayed correctly
   - Visibility setting is preserved
5. Make changes to the content
6. Click "Update Post" button
7. Verify success notification appears
8. Verify dialog closes
9. Verify changes are reflected in the post list

**Expected Results:**
- ✅ Dialog opens correctly
- ✅ Data is pre-populated
- ✅ Changes are saved successfully
- ✅ UI updates reflect changes

### 2. Blog Post Editing

**Test Steps:**
1. Navigate to Dashboard → My Content → Posts tab
2. Find a blog post and click "Edit"
3. Verify the EditPostDialog opens with title "Edit Blog Article"
4. Verify blog-specific fields are pre-populated:
   - Title
   - Excerpt
   - Content (rich text)
   - Category
   - Tags
   - Read time
   - Draft status
5. Modify the title and excerpt
6. Change the category
7. Click "Update Article" button
8. Verify the post is updated correctly

**Expected Results:**
- ✅ Blog form loads with correct data
- ✅ All blog-specific fields work
- ✅ Updates save correctly
- ✅ Database reflects changes

### 3. Event Post Editing

**Test Steps:**
1. Navigate to Dashboard → My Content → Posts tab
2. Find an event post and click "Edit"
3. Verify the EditPostDialog opens with title "Edit Event"
4. Verify event-specific fields are pre-populated:
   - Event title
   - Event type
   - Event format
   - Date and time
   - Location
   - Theme
   - Registration URL
   - Description
5. Modify the event date and location
6. Update the description
7. Click "Update Event" button
8. Verify the event details are updated

**Expected Results:**
- ✅ Event form loads with parsed JSON data
- ✅ Date/time fields work correctly
- ✅ Location and other fields update
- ✅ JSON content is properly formatted

### 4. Marketplace Post Editing

**Test Steps:**
1. Navigate to Dashboard → My Content → Marketplace tab
2. Find a marketplace listing and click "Edit"
3. Verify the EditPostDialog opens with title "Edit Marketplace Listing"
4. Verify marketplace-specific fields are pre-populated:
   - Listing title
   - Description
   - Listing type
   - Category
   - Price and price type
   - Condition (if applicable)
   - Location
   - Contact info
   - Images
5. Modify the price and description
6. Change the condition
7. Click "Update Listing" button
8. Verify the listing is updated

**Expected Results:**
- ✅ Marketplace form loads with parsed JSON data
- ✅ Price and condition fields work
- ✅ Images are preserved
- ✅ JSON content structure is maintained

### 5. Error Handling Tests

**Test Steps:**
1. **Authentication Error:**
   - Log out while edit dialog is open
   - Verify appropriate error message

2. **Permission Error:**
   - Try to edit another user's post (if possible)
   - Verify permission denied error

3. **Network Error:**
   - Disconnect internet during update
   - Verify error handling and retry options

4. **Validation Error:**
   - Clear required fields and try to save
   - Verify validation messages appear

**Expected Results:**
- ✅ Errors are handled gracefully
- ✅ User-friendly error messages
- ✅ No data loss on errors
- ✅ Proper validation feedback

### 6. Cross-Browser Testing

**Test in:**
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

**Verify:**
- Dialog opens correctly
- Form fields work properly
- Styling is consistent
- Functionality works across browsers

### 7. Mobile Responsiveness

**Test Steps:**
1. Open the application on mobile device or use browser dev tools
2. Navigate to content management
3. Try editing posts of different types
4. Verify:
   - Dialog is properly sized for mobile
   - Form fields are accessible
   - Buttons are touch-friendly
   - Scrolling works correctly

### 8. Performance Testing

**Test Steps:**
1. Edit posts with large content (>10,000 characters)
2. Edit posts with multiple images
3. Edit posts with many tags
4. Verify:
   - Loading times are reasonable
   - No memory leaks
   - Smooth interactions

## Database Verification

After each test, verify in the database that:
1. **posts table** is updated correctly
2. **Field mappings** are correct:
   - `title` field
   - `content` field (JSON for events/marketplace)
   - `featured_image` field
   - `tags` array
   - `sub_type` field
   - Type-specific fields (e.g., `blog_category`, `event_type`)
3. **updated_at** timestamp is current
4. **No data corruption** occurred

## Regression Testing

Verify that existing functionality still works:
1. **Post creation** still works for all types
2. **Post viewing** displays correctly
3. **Post deletion** works
4. **Comments and likes** are preserved
5. **Search and filtering** work with updated posts

## Success Criteria

The edit post system is considered successful if:
- ✅ All post types can be edited correctly
- ✅ Data integrity is maintained
- ✅ User experience is smooth and intuitive
- ✅ Error handling is robust
- ✅ Performance is acceptable
- ✅ No regressions in existing functionality

## Known Issues / Limitations

Document any discovered issues:
- [ ] Issue 1: Description
- [ ] Issue 2: Description
- [ ] Issue 3: Description

## Test Results Summary

| Test Category | Status | Notes |
|---------------|--------|-------|
| General Posts | ⏳ Pending | |
| Blog Posts | ⏳ Pending | |
| Event Posts | ⏳ Pending | |
| Marketplace Posts | ⏳ Pending | |
| Error Handling | ⏳ Pending | |
| Cross-Browser | ⏳ Pending | |
| Mobile | ⏳ Pending | |
| Performance | ⏳ Pending | |
| Database | ⏳ Pending | |
| Regression | ⏳ Pending | |

**Overall Status:** ⏳ Testing in Progress

---

*Last Updated: [Current Date]*
*Tested By: [Tester Name]*
