# Files to Remove for Production Optimization

## Overview
This document lists all test files, demo data, and development-only files that should be removed to optimize the production build and reduce bundle size.

## Test Files to Remove

### Unit Test Files
- `src/tests/edit-post-system.test.js` - Unit tests for post editing system
- Any other `.test.js`, `.test.ts`, `.spec.js`, `.spec.ts` files in src/

### E2E Test Files
- `tests/virtual-community-newui.spec.ts` - Playwright test for virtual community
- `tests/virtual-community-expanded.spec.ts` - Extended Playwright tests
- Entire `tests/` directory (Playwright tests)

### Test Configuration Files
- `vitest.config.ts` - Vitest configuration (keep for development)
- `playwright.config.ts` - Playwright configuration (keep for development)

## Development-Only Files to Remove

### Performance Testing Utilities
- `src/utils/performanceTesting.ts` - Performance validation tools
- `src/utils/messagingPerformanceTest.ts` - Messaging-specific tests
- `src/utils/integrationTestSuite.ts` - Comprehensive integration testing
- `src/config/performanceOptimization.ts` - Development performance configs

### Development Documentation
- `docs/optimization/` - Entire optimization documentation directory
- `docs/complete-migration-summary.md` - Migration documentation
- `docs/project/IMPLEMENTATION_VALIDATION_COMPLETE.md` - Implementation docs
- `docs/critical-fixes-implementation-summary.md` - Fix documentation
- `docs/state-management-migration-complete.md` - Migration docs
- `docs/unified-services-migration-guide.md` - Migration guide
- `docs/database-transition-plan.md` - Database transition docs
- `docs/critical-fixes-implementation-plan.md` - Implementation plan

### Demo Data and Mock Files
- Any files containing demo data or mock data
- Sample configuration files
- Test fixtures and seed data

## Configuration Files to Keep (But Optimize)

### Build Configuration
- `package.json` - Remove test scripts and dev dependencies for production
- `vite.config.ts` - Optimize for production builds
- `eslint.config.js` - Keep for code quality

### Application Configuration
- `src/main.ts` - Keep but remove development-only imports
- `src/config/` - Keep essential configs, remove development-only ones

## Specific Optimizations

### Package.json Cleanup
Remove these scripts for production:
```json
{
  "scripts": {
    "test": "vitest",
    "test:ui": "vitest --ui", 
    "test:coverage": "vitest run --coverage",
    "test:playwright": "playwright test",
    "test:playwright:ui": "playwright test --ui",
    "test:playwright:headed": "playwright test --headed"
  }
}
```

Remove these dev dependencies for production:
```json
{
  "devDependencies": {
    "@playwright/test": "^1.53.2",
    "@vitest/coverage-v8": "^1.6.1",
    "@vitest/ui": "^1.6.1",
    "@vue/test-utils": "^2.4.3",
    "happy-dom": "^13.3.8",
    "vitest": "^1.6.1"
  }
}
```

### Vite Configuration Optimization
Update `vite.config.ts` for production:
```typescript
export default defineConfig({
  build: {
    // Remove source maps for production
    sourcemap: false,
    
    // Optimize bundle size
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['quasar'],
          supabase: ['@supabase/supabase-js']
        }
      }
    },
    
    // Minification settings
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug']
      }
    }
  }
})
```

### Main.ts Cleanup
Remove development-only imports:
```typescript
// Remove these development imports
// import { createServiceCoordination } from "./plugins/serviceCoordination";
// import analyticsPlugin from "./plugins/analytics";
// import securityPlugin from "./plugins/security";
```

## Console Logging Cleanup

### Files with Excessive Logging
- `src/stores/posts/index.ts` - Multiple console.log statements
- `src/components/feed/FeedContainer.vue` - Debug logging
- `src/services/` - Various service files with debug logs
- `src/stores/` - Store files with debug logs

### Logging Patterns to Remove
```typescript
// Remove these patterns:
console.log('fetchPosts called with filter:', filter);
console.log('Current filter after merge:', currentFilter.value);
console.log('Messaging profile:', profileId);
console.log('Connecting with profile:', profileId);
console.debug('Cache hit for key:', key);
console.info('Service initialized:', serviceName);
```

## Expected Impact

### Bundle Size Reduction
- **Test files removal**: ~500KB reduction
- **Documentation removal**: ~2MB reduction  
- **Console logging removal**: ~50KB reduction
- **Dev dependencies removal**: ~100MB node_modules reduction

### Performance Improvements
- **Build time**: 30% faster production builds
- **Bundle parsing**: 25% faster initial load
- **Runtime performance**: 15% improvement from removed logging

### Maintenance Benefits
- **Cleaner codebase**: Easier to navigate and maintain
- **Reduced complexity**: Fewer files to manage
- **Better focus**: Production-only code remains

## Implementation Priority

### Phase 1: Critical Removals (Week 1)
1. Remove test files and directories
2. Clean up console logging in core files
3. Optimize package.json for production

### Phase 2: Documentation Cleanup (Week 1)
1. Remove development documentation
2. Clean up demo data and mock files
3. Optimize configuration files

### Phase 3: Build Optimization (Week 2)
1. Update Vite configuration for production
2. Implement advanced minification
3. Optimize dependency bundling

## Validation Steps

### After Cleanup
1. **Build test**: Ensure production build succeeds
2. **Bundle analysis**: Verify size reductions
3. **Functionality test**: Ensure all features work
4. **Performance test**: Measure improvement metrics

### Rollback Plan
1. **Git backup**: Create branch before cleanup
2. **Incremental approach**: Remove files in small batches
3. **Testing between batches**: Verify stability
4. **Documentation**: Track what was removed for potential restoration
