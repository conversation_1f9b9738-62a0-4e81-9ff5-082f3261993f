#!/usr/bin/env node

/**
 * Console Log Removal <PERSON>
 * 
 * This script removes console.log, console.debug, and console.info statements
 * from the codebase while preserving console.error and console.warn statements.
 * 
 * Usage: node scripts/remove-console-logs.js
 */

const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  // Directories to process
  directories: [
    'src/stores',
    'src/components', 
    'src/services',
    'src/views',
    'src/composables',
    'src/utils'
  ],
  
  // File extensions to process
  extensions: ['.ts', '.js', '.vue'],
  
  // Console methods to remove
  removePatterns: [
    /console\.log\([^)]*\);?\s*\n?/g,
    /console\.debug\([^)]*\);?\s*\n?/g,
    /console\.info\([^)]*\);?\s*\n?/g
  ],
  
  // Console methods to keep (for error handling)
  keepPatterns: [
    'console.error',
    'console.warn'
  ],
  
  // Files to exclude
  excludeFiles: [
    'main.ts',
    'router.ts',
    'supabase.ts'
  ]
};

/**
 * Get all files recursively from a directory
 */
function getAllFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    
    if (fs.statSync(fullPath).isDirectory()) {
      arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
    } else {
      const ext = path.extname(file);
      if (CONFIG.extensions.includes(ext) && !CONFIG.excludeFiles.includes(file)) {
        arrayOfFiles.push(fullPath);
      }
    }
  });

  return arrayOfFiles;
}

/**
 * Remove console logs from file content
 */
function removeConsoleLogs(content) {
  let modifiedContent = content;
  let removedCount = 0;

  CONFIG.removePatterns.forEach(pattern => {
    const matches = modifiedContent.match(pattern);
    if (matches) {
      removedCount += matches.length;
      modifiedContent = modifiedContent.replace(pattern, '');
    }
  });

  // Clean up empty lines that might be left behind
  modifiedContent = modifiedContent.replace(/\n\s*\n\s*\n/g, '\n\n');

  return { content: modifiedContent, removedCount };
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    const originalContent = fs.readFileSync(filePath, 'utf8');
    const { content: modifiedContent, removedCount } = removeConsoleLogs(originalContent);

    if (removedCount > 0) {
      fs.writeFileSync(filePath, modifiedContent, 'utf8');
      console.log(`✅ ${filePath}: Removed ${removedCount} console statements`);
      return removedCount;
    } else {
      console.log(`⚪ ${filePath}: No console statements to remove`);
      return 0;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return 0;
  }
}

/**
 * Main execution function
 */
function main() {
  console.log('🧹 Starting console log removal...\n');

  let totalFiles = 0;
  let totalRemoved = 0;
  let processedFiles = 0;

  // Process each configured directory
  CONFIG.directories.forEach(dir => {
    const dirPath = path.join(process.cwd(), dir);
    
    if (!fs.existsSync(dirPath)) {
      console.log(`⚠️  Directory not found: ${dir}`);
      return;
    }

    console.log(`📁 Processing directory: ${dir}`);
    const files = getAllFiles(dirPath);
    totalFiles += files.length;

    files.forEach(file => {
      const removed = processFile(file);
      if (removed > 0) {
        processedFiles++;
        totalRemoved += removed;
      }
    });

    console.log('');
  });

  // Summary
  console.log('📊 Summary:');
  console.log(`   Total files scanned: ${totalFiles}`);
  console.log(`   Files modified: ${processedFiles}`);
  console.log(`   Console statements removed: ${totalRemoved}`);
  
  if (totalRemoved > 0) {
    console.log('\n✅ Console log removal completed successfully!');
    console.log('💡 Tip: Run your tests to ensure everything still works correctly.');
  } else {
    console.log('\n✨ No console logs found to remove.');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { removeConsoleLogs, processFile };
