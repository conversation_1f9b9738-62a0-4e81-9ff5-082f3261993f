<template>
  <q-card class="blog-list-card" flat bordered>
    <div class="blog-list-content row no-wrap">
      <!-- Image on the left -->
      <div class="blog-image-section col-auto">
        <q-img
          :src="articleImage"
          :ratio="4/3"
          class="blog-list-image"
          @error="handleImageError"
          no-spinner
          no-transition
        >
          <template v-slot:error>
            <div class="absolute-full flex flex-center bg-grey-3 text-grey-8">
              <q-icon name="broken_image" size="2em" />
            </div>
          </template>
        </q-img>
      </div>

      <!-- Content on the right -->
      <div class="blog-content-section col">
        <q-card-section class="q-pa-md">
          <!-- Categories -->
          <div class="blog-categories q-mb-sm">
            <q-badge
              color="purple"
              class="q-mr-sm"
              size="sm"
            >
              Blog
            </q-badge>
            <q-badge
              v-if="article.category"
              :color="getCategoryColor(article.category)"
              class="q-mr-sm"
              size="sm"
            >
              {{ article.category }}
            </q-badge>
          </div>

          <!-- Title -->
          <h3 class="blog-title text-h6 q-mb-sm text-weight-medium">
            {{ articleTitle }}
          </h3>

          <!-- Description/Excerpt -->
          <p class="blog-description text-body2 text-grey-7 q-mb-md">
            {{ articleDescription }}
          </p>

          <!-- Meta information -->
          <div class="blog-meta row items-center justify-between">
            <div class="meta-left row items-center">
              <!-- Author -->
              <div class="author-info row items-center q-mr-md">
                <q-avatar size="24px" class="q-mr-xs">
                  <img :src="authorAvatar" :alt="authorName" />
                </q-avatar>
                <span class="text-caption text-grey-7">{{ authorName }}</span>
              </div>

              <!-- Date -->
              <div class="date-info row items-center q-mr-md">
                <q-icon name="schedule" size="sm" class="q-mr-xs text-grey-5" />
                <span class="text-caption text-grey-7">{{ formattedDate }}</span>
              </div>

              <!-- Read time -->
              <div v-if="readTime" class="read-time row items-center">
                <q-icon name="timer" size="sm" class="q-mr-xs text-grey-5" />
                <span class="text-caption text-grey-7">{{ readTime }} min read</span>
              </div>
            </div>

            <!-- Action button -->
            <q-btn
              flat
              color="primary"
              label="Read More"
              size="sm"
              @click="handleRead"
              class="read-more-btn"
            />
          </div>

          <!-- Tags -->
          <div v-if="articleTags.length > 0" class="blog-tags q-mt-sm">
            <q-chip
              v-for="tag in articleTags.slice(0, 3)"
              :key="tag"
              size="sm"
              outline
              color="primary"
              class="q-mr-xs q-mb-xs"
            >
              #{{ tag }}
            </q-chip>
          </div>
        </q-card-section>
      </div>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

// Props
interface Props {
  article: any;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  read: [article: any];
}>();

// State
const imageError = ref(false);

// Computed properties
const articleTitle = computed(() => {
  return props.article.blogTitle || 
         props.article.title || 
         props.article.content?.substring(0, 100) + '...' || 
         'Untitled Article';
});

const articleDescription = computed(() => {
  const description = props.article.blogExcerpt || 
                     props.article.description || 
                     props.article.content?.substring(0, 200) + '...' || 
                     'No description available.';
  
  // Limit description length for list view
  return description.length > 150 ? description.substring(0, 150) + '...' : description;
});

const articleImage = computed(() => {
  if (imageError.value) {
    return `https://picsum.photos/300/225?random=${props.article.id}`;
  }
  
  return props.article.blogFeaturedImage || 
         props.article.featured_image || 
         props.article.image || 
         `https://picsum.photos/300/225?random=${props.article.id}`;
});

const articleTags = computed(() => {
  const tags = props.article.tags || [];
  return Array.isArray(tags) ? tags.filter((tag: string) => 
    typeof tag === 'string' && 
    tag.toLowerCase() !== 'blog' && 
    tag.toLowerCase() !== 'featured'
  ) : [];
});

const authorName = computed(() => {
  return props.article.author?.name || 
         props.article.author_name || 
         props.article.user?.name || 
         'Anonymous';
});

const authorAvatar = computed(() => {
  return props.article.author?.avatar || 
         props.article.author_avatar || 
         props.article.user?.avatar || 
         `https://ui-avatars.com/api/?name=${encodeURIComponent(authorName.value)}&background=random`;
});

const formattedDate = computed(() => {
  const date = props.article.created_at || props.article.published_at || props.article.date;
  if (!date) return 'Unknown date';
  
  try {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch {
    return 'Invalid date';
  }
});

const readTime = computed(() => {
  const content = props.article.blogFullContent || props.article.content || '';
  const wordsPerMinute = 200;
  const wordCount = content.split(/\s+/).length;
  return Math.max(1, Math.ceil(wordCount / wordsPerMinute));
});

// Methods
const getCategoryColor = (category: string): string => {
  const colors: Record<string, string> = {
    'technology': 'blue',
    'business': 'green',
    'innovation': 'purple',
    'startup': 'orange',
    'ai': 'indigo',
    'education': 'teal',
    'mentorship': 'pink',
    'career': 'amber',
    'default': 'grey'
  };
  
  return colors[category?.toLowerCase()] || colors.default;
};

const handleImageError = () => {
  imageError.value = true;
};

const handleRead = () => {
  emit('read', props.article);
};
</script>

<style scoped>
.blog-list-card {
  transition: all 0.2s ease;
  border-radius: 8px;
}

.blog-list-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.blog-list-content {
  min-height: 180px;
}

.blog-image-section {
  width: 200px;
  flex-shrink: 0;
}

.blog-list-image {
  width: 200px;
  height: 150px;
  border-radius: 8px 0 0 8px;
}

.blog-content-section {
  display: flex;
  flex-direction: column;
}

.blog-title {
  line-height: 1.3;
  margin: 0;
  color: #1a1a1a;
}

.blog-title:hover {
  color: #1976d2;
  cursor: pointer;
}

.blog-description {
  line-height: 1.5;
  flex-grow: 1;
}

.blog-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.blog-meta {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.meta-left {
  flex-grow: 1;
}

.author-info,
.date-info,
.read-time {
  white-space: nowrap;
}

.read-more-btn {
  flex-shrink: 0;
}

.blog-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .blog-list-content {
    flex-direction: column;
  }
  
  .blog-image-section {
    width: 100%;
  }
  
  .blog-list-image {
    width: 100%;
    height: 200px;
    border-radius: 8px 8px 0 0;
  }
  
  .blog-meta {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .meta-left {
    flex-direction: column;
    gap: 4px;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .blog-image-section {
    width: 100%;
  }
  
  .blog-list-image {
    height: 160px;
  }
  
  .author-info,
  .date-info,
  .read-time {
    font-size: 0.75rem;
  }
}
</style>
