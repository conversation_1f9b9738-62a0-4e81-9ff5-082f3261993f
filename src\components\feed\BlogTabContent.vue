<template>
  <div class="blog-tab-content">
    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <q-spinner-dots size="2rem" color="primary" />
      <p>Loading articles...</p>
    </div>

    <!-- Articles List -->
    <div v-else-if="articles.length > 0" class="articles-list">
      <div
        v-for="article in articles"
        :key="article.id"
        class="article-card"
      >
        <q-card flat bordered class="q-mb-md">
          <div class="article-image-container" v-if="article.featured_image">
            <q-img
              :src="article.featured_image"
              :alt="article.title"
              class="article-image"
              ratio="16/9"
            />
          </div>

          <q-card-section>
            <div class="article-meta">
              <q-chip
                v-for="tag in article.tags?.slice(0, 2)"
                :key="tag"
                size="sm"
                color="primary"
                text-color="white"
                :label="tag"
              />
              <span class="article-date">{{ formatDate(article.created_at) }}</span>
            </div>

            <h5 class="article-title">{{ article.title }}</h5>
            
            <p class="article-excerpt">{{ article.excerpt || article.content }}</p>

            <div class="article-author">
              <UserAvatar :user="article.author" :size="24" />
              <span class="author-name">{{ article.author?.full_name || 'Anonymous' }}</span>
            </div>
          </q-card-section>

          <q-card-actions>
            <q-btn
              flat
              color="primary"
              label="Read More"
              @click="readArticle(article.id)"
            />
            <q-spacer />
            <q-btn flat round icon="bookmark_border" />
            <q-btn flat round icon="share" />
          </q-card-actions>
        </q-card>
      </div>

      <!-- Load More Button -->
      <div v-if="hasMore" class="load-more-container">
        <q-btn
          unelevated
          color="primary"
          label="Load More"
          @click="$emit('load-more')"
          :loading="loading"
        />
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="empty-state">
      <q-icon name="article" size="4rem" color="grey-5" />
      <h4>No Articles Found</h4>
      <p>No blog articles match your current filters.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import { useRouter } from 'vue-router'
import UserAvatar from '../common/UserAvatar.vue'

// Props
interface Props {
  articles: any[]
  loading: boolean
  hasMore: boolean
}

defineProps<Props>()

// Emits
defineEmits<{
  'load-more': []
  'refresh': []
}>()

// Composables
const router = useRouter()

// Methods
function formatDate(dateString: string) {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  })
}

function readArticle(articleId: string) {
  router.push(`/virtual-community/article/${articleId}`)
}
</script>

<style scoped>
.blog-tab-content {
  padding: 16px;
}

.loading-container {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.articles-list {
  max-width: 800px;
  margin: 0 auto;
}

.article-card {
  margin-bottom: 24px;
}

.article-image-container {
  position: relative;
  overflow: hidden;
}

.article-image {
  border-radius: 8px 8px 0 0;
}

.article-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.article-date {
  font-size: 12px;
  color: #6b7280;
  margin-left: auto;
}

.article-title {
  margin: 0 0 12px 0;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  line-height: 1.3;
}

.article-excerpt {
  margin: 0 0 16px 0;
  color: #374151;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-author {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-name {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.load-more-container {
  text-align: center;
  padding: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
  
  h4 {
    margin: 16px 0 8px 0;
    color: #374151;
  }
  
  p {
    margin: 0 0 24px 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .blog-tab-content {
    padding: 8px;
  }
  
  .articles-list {
    max-width: 100%;
  }
  
  .article-title {
    font-size: 18px;
  }
}
</style>
