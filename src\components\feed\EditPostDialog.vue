<template>
  <q-dialog
    v-model="dialogOpen"
    persistent
    maximized
    :transition-show="'slide-up'"
    :transition-hide="'slide-down'"
  >
    <q-card class="edit-post-dialog">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">{{ dialogTitle }}</div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <q-separator class="q-my-md" />

      <q-card-section class="q-pt-none">
        <div v-if="!isAuthenticated" class="text-center q-pa-lg">
          <q-icon name="lock" size="64px" color="grey-5" class="q-mb-md" />
          <div class="text-h6 q-mb-md">Authentication Required</div>
          <div class="text-body2 text-grey-7 q-mb-lg">
            You need to be logged in to edit posts.
          </div>
        </div>

        <div v-else-if="loading" class="text-center q-pa-lg">
          <q-spinner-dots size="64px" color="primary" class="q-mb-md" />
          <div class="text-h6 q-mb-md">Loading Post...</div>
          <div class="text-body2 text-grey-7">
            Please wait while we fetch the post data.
          </div>
        </div>

        <div v-else-if="error" class="text-center q-pa-lg">
          <q-icon name="error" size="64px" color="negative" class="q-mb-md" />
          <div class="text-h6 q-mb-md">Error Loading Post</div>
          <div class="text-body2 text-grey-7 q-mb-lg">
            {{ error }}
          </div>
          <q-btn 
            label="Try Again" 
            color="primary" 
            @click="fetchPostData" 
            :loading="loading"
          />
        </div>

        <div v-else-if="postData">
          <!-- Post Type Display (Read-only) -->
          <div class="q-mb-md">
            <q-chip 
              :icon="getPostTypeIcon(currentPostType)" 
              :color="getPostTypeColor(currentPostType)"
              text-color="white"
              class="q-mb-sm"
            >
              {{ getPostTypeLabel(currentPostType) }}
            </q-chip>
            <div class="text-caption text-grey-6">
              Post type cannot be changed when editing
            </div>
          </div>

          <!-- Dynamic form based on post type -->
          <component
            :is="currentFormComponent"
            v-if="currentFormComponent"
            @submit="handleFormSubmit"
            @cancel="dialogOpen = false"
            :loading="submitting"
            :edit-mode="true"
            :initial-data="postData"
          />
        </div>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useQuasar } from 'quasar';
import { useAuthStore } from '@/stores/auth';
import { usePostsStore } from '@/stores/posts';
import { useNotificationStore } from '@/stores/notifications';

// Import form components
import GeneralPostForm from './forms/GeneralPostForm.vue';
import OpportunityPostForm from './forms/OpportunityPostForm.vue';
import BlogPostForm from './forms/BlogPostForm.vue';
import EventPostForm from './forms/EventPostForm.vue';
import MarketplacePostForm from './forms/MarketplacePostForm.vue';

const props = withDefaults(defineProps<{
  modelValue?: boolean;
  postId?: number | string;
}>(), {
  modelValue: false,
  postId: null
});

const emit = defineEmits(['update:modelValue', 'post-updated']);

// Stores
const authStore = useAuthStore();
const postsStore = usePostsStore();
const notificationStore = useNotificationStore();
const $q = useQuasar();

// State
const dialogOpen = ref(props.modelValue);
const loading = ref(false);
const submitting = ref(false);
const error = ref<string | null>(null);
const postData = ref<any>(null);
const currentPostType = ref<string>('general');

// Computed properties
const isAuthenticated = computed(() => authStore.isAuthenticated);

const dialogTitle = computed(() => {
  if (!isAuthenticated.value) return 'Authentication Required';
  if (loading.value) return 'Loading Post...';
  if (error.value) return 'Error Loading Post';
  
  switch (currentPostType.value) {
    case 'general':
      return 'Edit Post';
    case 'opportunity':
      return 'Edit Opportunity';
    case 'blog':
      return 'Edit Blog Article';
    case 'event':
      return 'Edit Event';
    case 'marketplace':
      return 'Edit Marketplace Listing';
    default:
      return 'Edit Post';
  }
});

const currentFormComponent = computed(() => {
  switch (currentPostType.value) {
    case 'general':
      return GeneralPostForm;
    case 'opportunity':
      return OpportunityPostForm;
    case 'blog':
      return BlogPostForm;
    case 'event':
      return EventPostForm;
    case 'marketplace':
      return MarketplacePostForm;
    default:
      return null;
  }
});

// Watch for dialog open/close
watch(() => props.modelValue, (newVal) => {
  dialogOpen.value = newVal;
  if (newVal && props.postId) {
    fetchPostData();
  }
});

watch(dialogOpen, (newVal) => {
  emit('update:modelValue', newVal);
  if (!newVal) {
    // Reset state when dialog closes
    postData.value = null;
    error.value = null;
    currentPostType.value = 'general';
  }
});

// Methods
async function fetchPostData() {
  if (!props.postId) {
    error.value = 'No post ID provided';
    return;
  }

  loading.value = true;
  error.value = null;

  try {
    // Fetch the post data
    const post = await postsStore.getPostById(props.postId);
    
    if (!post) {
      throw new Error('Post not found');
    }

    postData.value = post;
    
    // Determine post type from the post data
    currentPostType.value = determinePostType(post);
    
  } catch (err: any) {
    console.error('Error fetching post data:', err);
    error.value = err.message || 'Failed to load post data';
  } finally {
    loading.value = false;
  }
}

function determinePostType(post: any): string {
  // Use subType first, then postType, then fallback logic
  const subType = (post.subType || '').toLowerCase();
  const postType = (post.postType || '').toLowerCase();
  
  if (subType === 'marketplace' || postType === 'marketplace') return 'marketplace';
  if (subType === 'blog' || postType === 'blog' || postType === 'blog_article') return 'blog';
  if (subType === 'event' || postType === 'event') return 'event';
  if (subType === 'opportunity' || postType === 'opportunity') return 'opportunity';
  
  return 'general';
}

function getPostTypeIcon(type: string): string {
  switch (type) {
    case 'general': return 'post_add';
    case 'opportunity': return 'emoji_objects';
    case 'blog': return 'article';
    case 'event': return 'event';
    case 'marketplace': return 'storefront';
    default: return 'post_add';
  }
}

function getPostTypeColor(type: string): string {
  switch (type) {
    case 'general': return 'blue';
    case 'opportunity': return 'amber';
    case 'blog': return 'deep-orange';
    case 'event': return 'green';
    case 'marketplace': return 'purple';
    default: return 'blue';
  }
}

function getPostTypeLabel(type: string): string {
  switch (type) {
    case 'general': return 'General Post';
    case 'opportunity': return 'Opportunity';
    case 'blog': return 'Blog Article';
    case 'event': return 'Event';
    case 'marketplace': return 'Marketplace Listing';
    default: return 'Post';
  }
}

async function handleFormSubmit(formData: any) {
  if (!postData.value) return;

  submitting.value = true;

  try {
    console.log('Form data received:', formData);
    console.log('Original post data:', postData.value);

    // Prepare the update data with the post ID
    const updateData = {
      id: postData.value.id,
      ...formData
    };

    console.log('Prepared update data:', updateData);

    // Update the post using the posts store
    const success = await postsStore.updatePost(updateData);

    if (success) {
      // Emit the updated post event
      emit('post-updated', updateData);
      dialogOpen.value = false;
      notificationStore.success('Post updated successfully!');
    } else {
      throw new Error('Failed to update post - store returned false');
    }
  } catch (error: any) {
    console.error('Error in handleFormSubmit:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      formData,
      postData: postData.value
    });

    // Show more specific error messages
    let errorMessage = 'Failed to update post. Please try again.';
    if (error.message.includes('Database update failed')) {
      errorMessage = 'Database error: ' + error.message;
    } else if (error.message.includes('not authenticated')) {
      errorMessage = 'Please log in to update posts.';
    } else if (error.message.includes('permission')) {
      errorMessage = 'You do not have permission to edit this post.';
    } else if (error.message) {
      errorMessage = error.message;
    }

    notificationStore.error(errorMessage);
  } finally {
    submitting.value = false;
  }
}

// Method to open the dialog with a specific post ID
function openDialog(postId: number | string) {
  if (postId) {
    // Set the post ID and open the dialog
    emit('update:modelValue', true);
  }
}

// Expose the openDialog method for parent components
defineExpose({
  openDialog
});
</script>

<style scoped>
.edit-post-dialog {
  width: 100%;
  max-width: none;
}

@media (max-width: 600px) {
  .edit-post-dialog {
    margin: 0;
    border-radius: 0;
  }
}
</style>
