<template>
  <div class="events-tab-content">
    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <q-spinner-dots size="2rem" color="primary" />
      <p>Loading events...</p>
    </div>

    <!-- Events List -->
    <div v-else-if="events.length > 0" class="events-list">
      <div
        v-for="event in events"
        :key="event.id"
        class="event-card"
      >
        <q-card flat bordered class="q-mb-md">
          <q-card-section class="event-header">
            <div class="event-date-badge">
              <div class="date-day">{{ formatDay(event.event_date) }}</div>
              <div class="date-month">{{ formatMonth(event.event_date) }}</div>
            </div>
            <div class="event-info">
              <h6 class="event-title">{{ event.title }}</h6>
              <p class="event-time">{{ formatTime(event.event_date) }}</p>
              <p class="event-location">{{ event.location || 'Location TBA' }}</p>
            </div>
          </q-card-section>

          <q-card-section v-if="event.description">
            <p class="event-description">{{ event.description }}</p>
          </q-card-section>

          <q-card-section v-if="event.featured_image">
            <q-img
              :src="event.featured_image"
              :alt="event.title"
              class="event-image"
              ratio="16/9"
            />
          </q-card-section>

          <q-card-actions>
            <q-btn
              unelevated
              color="primary"
              label="View Details"
              @click="viewEvent(event.id)"
            />
            <q-btn
              flat
              color="primary"
              label="Register"
              @click="registerForEvent(event.id)"
            />
            <q-spacer />
            <q-btn flat round icon="share" />
          </q-card-actions>
        </q-card>
      </div>

      <!-- Load More Button -->
      <div v-if="hasMore" class="load-more-container">
        <q-btn
          unelevated
          color="primary"
          label="Load More"
          @click="$emit('load-more')"
          :loading="loading"
        />
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="empty-state">
      <q-icon name="event" size="4rem" color="grey-5" />
      <h4>No Events Found</h4>
      <p>No upcoming events match your current filters.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import { useRouter } from 'vue-router'

// Props
interface Props {
  events: any[]
  loading: boolean
  hasMore: boolean
}

defineProps<Props>()

// Emits
defineEmits<{
  'load-more': []
  'refresh': []
}>()

// Composables
const router = useRouter()

// Methods
function formatDay(dateString: string) {
  const date = new Date(dateString)
  return date.getDate().toString().padStart(2, '0')
}

function formatMonth(dateString: string) {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', { month: 'short' }).toUpperCase()
}

function formatTime(dateString: string) {
  const date = new Date(dateString)
  return date.toLocaleTimeString('en-US', { 
    hour: 'numeric', 
    minute: '2-digit',
    hour12: true 
  })
}

function viewEvent(eventId: string) {
  router.push(`/virtual-community/event/${eventId}`)
}

function registerForEvent(eventId: string) {
  console.log('Register for event:', eventId)
  // Implementation would go here
}
</script>

<style scoped>
.events-tab-content {
  padding: 16px 24px; /* Increase horizontal padding to prevent edge overflow */
}

.loading-container {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.events-list {
  max-width: 800px;
  margin: 0 auto;
}

.event-card {
  margin-bottom: 16px;
}

.event-header {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.event-date-badge {
  background: #6366f1;
  color: white;
  border-radius: 8px;
  padding: 8px;
  text-align: center;
  min-width: 60px;
  flex-shrink: 0;
}

.date-day {
  font-size: 20px;
  font-weight: 700;
  line-height: 1;
}

.date-month {
  font-size: 10px;
  font-weight: 600;
  letter-spacing: 0.5px;
  margin-top: 2px;
}

.event-info {
  flex: 1;
}

.event-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.event-time {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #6366f1;
  font-weight: 500;
}

.event-location {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

.event-description {
  margin: 0;
  color: #374151;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.event-image {
  border-radius: 8px;
  overflow: hidden;
}

.load-more-container {
  text-align: center;
  padding: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
  
  h4 {
    margin: 16px 0 8px 0;
    color: #374151;
  }
  
  p {
    margin: 0 0 24px 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .events-tab-content {
    padding: 8px 16px; /* Maintain horizontal padding on mobile */
  }
  
  .events-list {
    max-width: 100%;
  }
  
  .event-header {
    gap: 12px;
  }
  
  .event-date-badge {
    min-width: 50px;
    padding: 6px;
  }
  
  .date-day {
    font-size: 16px;
  }
}
</style>
