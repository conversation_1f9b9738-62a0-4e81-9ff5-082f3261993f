<template>
  <div class="featured-section">
    <!-- Section Header -->
    <div class="featured-header q-mb-md">
      <div class="text-h6 text-weight-bold">Featured</div>
      <p class="text-caption text-grey-7 q-mb-none">Discover what's trending in our community</p>
    </div>

    <!-- Blog Tab: Special layout (no horizontal scroll) -->
    <template v-if="tab === 'blog'">
      <div class="blog-featured-container">
        <div v-if="featuredBlogs.length > 0" class="blog-featured-content">
          <featured-card
            :item="featuredBlogs[0]"
            type="blog"
            @click="handleItemClick"
            class="blog-featured-main"
          />
        </div>
        <div v-else-if="loading" class="blog-featured-content">
          <featured-card-skeleton class="blog-featured-main" />
        </div>
        <div v-else class="blog-featured-empty">
          <div class="text-center q-pa-lg">
            <q-icon name="article" size="3em" color="grey-5" />
            <p class="text-grey-6 q-mt-md">No featured blog posts available</p>
          </div>
        </div>
      </div>
    </template>

    <!-- All Other Tabs: Horizontal Scrollable Container -->
    <template v-else>
      <div class="featured-scroll-container">
        <div class="featured-scroll-content" ref="scrollContainer">
          <!-- Feed Tab: Show one of each type -->
          <template v-if="tab === 'feed'">
            <div v-if="featuredBlog" class="featured-item">
              <featured-card
                :item="featuredBlog"
                type="blog"
                @click="handleItemClick"
              />
            </div>

            <div v-if="featuredEvent" class="featured-item">
              <featured-card
                :item="featuredEvent"
                type="event"
                @click="handleItemClick"
              />
            </div>

            <div v-if="featuredMarketplace" class="featured-item">
              <featured-card
                :item="featuredMarketplace"
                type="marketplace"
                @click="handleItemClick"
              />
            </div>

            <div v-if="featuredProfile" class="featured-item">
              <featured-card
                :item="featuredProfile"
                type="profile"
                @click="handleItemClick"
              />
            </div>
          </template>

          <!-- Events Tab: Show multiple featured events -->
          <template v-else-if="tab === 'events'">
            <div v-for="event in featuredEvents.slice(0, 6)" :key="event.id" class="featured-item">
              <featured-card
                :item="event"
                type="event"
                @click="handleItemClick"
              />
            </div>
          </template>

          <!-- Marketplace Tab: Show multiple featured marketplace items -->
          <template v-else-if="tab === 'marketplace'">
            <div v-for="item in featuredMarketplaceItems.slice(0, 6)" :key="item.id" class="featured-item">
              <featured-card
                :item="item"
                type="marketplace"
                @click="handleItemClick"
              />
            </div>
          </template>

          <!-- Profiles Tab: Show multiple featured profiles -->
          <template v-else-if="tab === 'profiles'">
            <div v-for="profile in featuredProfiles.slice(0, 6)" :key="profile.id" class="featured-item">
              <featured-card
                :item="profile"
                type="profile"
                @click="handleItemClick"
              />
            </div>
          </template>

          <!-- Loading placeholders -->
          <template v-if="loading">
            <div class="featured-item" v-for="n in 4" :key="`loading-${n}`">
              <featured-card-skeleton />
            </div>
          </template>

          <!-- Empty state when no featured content -->
          <div v-if="!loading && !hasFeaturedContent" class="featured-empty">
            <div class="text-center q-pa-lg">
              <q-icon name="star_border" size="3em" color="grey-5" />
              <p class="text-grey-6 q-mt-md">No featured content available for {{ tab }}</p>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import FeaturedCard from './FeaturedCard.vue';
import FeaturedCardSkeleton from './FeaturedCardSkeleton.vue';
import { usePostsStore } from '../../stores/posts';
import { useProfileStore } from '../../stores/profileStore';

// Props
interface Props {
  maxItems?: number;
  tab?: string;
}

const props = withDefaults(defineProps<Props>(), {
  maxItems: 5,
  tab: 'feed'
});

// Emits
const emit = defineEmits<{
  itemClick: [type: string, item: any];
}>();

// Composables
const router = useRouter();
const postsStore = usePostsStore();
const profileStore = useProfileStore();

// State
const loading = ref(true);
const scrollContainer = ref<HTMLElement>();

// Computed - Get latest featured posts of each type
const featuredBlog = computed(() => {
  const blogPosts = postsStore.posts
    .filter(post => {
      // Use same logic as posts store articles getter
      const hasBlogSubType = post.subType?.toLowerCase() === 'blog';
      const hasBlogFields = post.blogTitle || post.blogCategory || post.blogFullContent;
      const hasBlogTag = post.tags?.some((tag: string) =>
        typeof tag === 'string' && tag.toLowerCase() === 'blog'
      );

      const isBlog = hasBlogSubType || hasBlogFields || hasBlogTag;
      const isFeatured = post.tags?.includes('featured') || post.category?.toLowerCase() === 'featured';
      const isPublished = post.status === 'published';

      return isBlog && isFeatured && isPublished;
    })
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

  return blogPosts.length > 0 ? blogPosts[0] : null;
});

const featuredEvent = computed(() => {
  const eventPosts = postsStore.posts
    .filter(post => {
      // Use same logic as posts store events getter
      const hasEventSubType = post.subType?.toLowerCase() === 'event';
      const hasEventFields = post.eventTitle || post.eventLocation || post.eventStartDatetime;
      const hasEventTag = post.tags?.some((tag: string) =>
        typeof tag === 'string' && tag.toLowerCase() === 'event'
      );

      const isEvent = hasEventSubType || hasEventFields || hasEventTag;
      const isFeatured = post.tags?.includes('featured') || post.category?.toLowerCase() === 'featured';
      const isPublished = post.status === 'published';

      return isEvent && isFeatured && isPublished;
    })
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

  return eventPosts.length > 0 ? eventPosts[0] : null;
});

const featuredMarketplace = computed(() => {
  const marketplacePosts = postsStore.posts
    .filter(post => {
      // Use same logic as posts store marketplace getter
      const hasMarketplaceSubType = post.subType?.toLowerCase() === 'marketplace';
      const hasMarketplaceFields = post.price !== undefined || (post.location && post.price);
      const hasMarketplaceTag = post.tags?.some((tag: string) =>
        typeof tag === 'string' && (
          tag.toLowerCase() === 'marketplace' ||
          tag.toLowerCase() === 'market'
        )
      );

      const isMarketplace = hasMarketplaceSubType || hasMarketplaceFields || hasMarketplaceTag;
      const isFeatured = post.tags?.includes('featured') || post.category?.toLowerCase() === 'featured';
      const isPublished = post.status === 'published';

      return isMarketplace && isFeatured && isPublished;
    })
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

  return marketplacePosts.length > 0 ? marketplacePosts[0] : null;
});

const featuredProfile = computed(() => {
  const featuredProfiles = profileStore.publicProfiles
    ?.filter(profile => profile.tags?.includes('featured'))
    ?.sort((a, b) => new Date(b.created_at || b.updated_at || 0).getTime() - new Date(a.created_at || a.updated_at || 0).getTime());

  return featuredProfiles && featuredProfiles.length > 0 ? featuredProfiles[0] : null;
});

const featuredGroup = computed(() => {
  // Placeholder for when groups are implemented
  return null;
});

// Multiple featured items for specific tabs
const featuredBlogs = computed(() => {
  const blogPosts = postsStore.posts
    .filter(post => {
      const hasBlogSubType = post.subType?.toLowerCase() === 'blog';
      const hasBlogFields = post.blogTitle || post.blogCategory || post.blogFullContent;
      const hasBlogTag = post.tags?.some((tag: string) =>
        typeof tag === 'string' && tag.toLowerCase() === 'blog'
      );

      const isBlog = hasBlogSubType || hasBlogFields || hasBlogTag;
      const isFeatured = post.tags?.includes('featured') || post.category?.toLowerCase() === 'featured';
      const isPublished = post.status === 'published';

      return isBlog && isFeatured && isPublished;
    })
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, props.maxItems);

  return blogPosts;
});

const featuredEvents = computed(() => {
  const eventPosts = postsStore.posts
    .filter(post => {
      const hasEventSubType = post.subType?.toLowerCase() === 'event';
      const hasEventFields = post.eventTitle || post.eventLocation || post.eventStartDatetime;
      const hasEventTag = post.tags?.some((tag: string) =>
        typeof tag === 'string' && tag.toLowerCase() === 'event'
      );

      const isEvent = hasEventSubType || hasEventFields || hasEventTag;
      const isFeatured = post.tags?.includes('featured') || post.category?.toLowerCase() === 'featured';
      const isPublished = post.status === 'published';

      return isEvent && isFeatured && isPublished;
    })
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, props.maxItems);

  return eventPosts;
});

const featuredMarketplaceItems = computed(() => {
  const marketplacePosts = postsStore.posts
    .filter(post => {
      const hasMarketplaceSubType = post.subType?.toLowerCase() === 'marketplace';
      const hasMarketplaceFields = post.price !== undefined || (post.location && post.price);
      const hasMarketplaceTag = post.tags?.some((tag: string) =>
        typeof tag === 'string' && (
          tag.toLowerCase() === 'marketplace' ||
          tag.toLowerCase() === 'market'
        )
      );

      const isMarketplace = hasMarketplaceSubType || hasMarketplaceFields || hasMarketplaceTag;
      const isFeatured = post.tags?.includes('featured') || post.category?.toLowerCase() === 'featured';
      const isPublished = post.status === 'published';

      return isMarketplace && isFeatured && isPublished;
    })
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, props.maxItems);

  return marketplacePosts;
});

const featuredProfiles = computed(() => {
  const profiles = profileStore.publicProfiles
    ?.filter(profile => profile.tags?.includes('featured'))
    ?.sort((a, b) => new Date(b.created_at || b.updated_at || 0).getTime() - new Date(a.created_at || a.updated_at || 0).getTime())
    ?.slice(0, props.maxItems);

  return profiles || [];
});

const hasFeaturedContent = computed(() => {
  if (props.tab === 'feed') {
    return !!(featuredBlog.value || featuredMarketplace.value || featuredProfile.value || featuredEvent.value);
  } else if (props.tab === 'blog') {
    return featuredBlogs.value.length > 0;
  } else if (props.tab === 'events') {
    return featuredEvents.value.length > 0;
  } else if (props.tab === 'marketplace') {
    return featuredMarketplaceItems.value.length > 0;
  } else if (props.tab === 'profiles') {
    return featuredProfiles.value.length > 0;
  }
  return false;
});

// Track which categories have featured content
const featuredContentStatus = computed(() => {
  return {
    blog: !!featuredBlog.value,
    event: !!featuredEvent.value,
    marketplace: !!featuredMarketplace.value,
    profile: !!featuredProfile.value,
    total: [featuredBlog.value, featuredEvent.value, featuredMarketplace.value, featuredProfile.value].filter(Boolean).length
  };
});

// Removed mock content - only show real database content

// Methods
async function loadFeaturedContent() {
  try {
    loading.value = true;
    console.log('Loading featured content...');

    // Load all posts first to ensure we have the complete dataset
    await postsStore.fetchPosts({
      status: 'published'
    });

    // Load featured profiles if available
    if (profileStore.loadPublicProfiles) {
      await profileStore.loadPublicProfiles(1, 12, {
        tags: ['featured']
      });
    }

    console.log('Featured content loaded from database.');
    console.log('Featured content status:', featuredContentStatus.value);
    console.log('Featured blog:', featuredBlog.value?.title || 'None');
    console.log('Featured event:', featuredEvent.value?.title || 'None');
    console.log('Featured marketplace:', featuredMarketplace.value?.title || 'None');
    console.log('Featured profile:', featuredProfile.value?.full_name || 'None');
  } catch (error) {
    console.error('Error loading featured content:', error);
  } finally {
    loading.value = false;
  }
}

function handleItemClick(type: string, item: any) {
  emit('itemClick', type, item);

  // Default navigation logic using proper route names
  switch (type) {
    case 'blog':
      if (item.slug) {
        router.push({ name: 'innovation-community-article', params: { slug: item.slug } });
      } else {
        router.push({ name: 'post-details', params: { id: item.id } });
      }
      break;
    case 'marketplace':
      router.push({ name: 'post-details', params: { id: item.id } });
      break;
    case 'profile':
      router.push({ name: 'user-profile', params: { id: item.user_id || item.id } });
      break;
    case 'event':
      router.push({ name: 'event-details', params: { id: item.id } });
      break;
    case 'group':
      router.push({ name: 'group', params: { id: item.id } });
      break;
  }
}

// Lifecycle
onMounted(() => {
  console.log('FeaturedSection mounted');
  loadFeaturedContent();
});
</script>

<style scoped>
.featured-section {
  margin-bottom: 24px;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.featured-header {
  padding: 0 16px;
}

/* Blog Tab: Special layout (no horizontal scroll) */
.blog-featured-container {
  padding: 0 16px 16px 16px;
}

.blog-featured-content {
  max-width: 100%;
}

.blog-featured-main {
  width: 100%;
  max-width: 800px;
}

.blog-featured-empty {
  width: 100%;
  background: #f9fafb;
  border-radius: 12px;
  border: 2px dashed #e5e7eb;
}

/* Horizontal Scrollable Container for other tabs */
.featured-scroll-container {
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
}

.featured-scroll-content {
  display: flex;
  gap: 16px;
  padding: 0 16px 16px 16px;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  /* Visible scrollbar with light green color */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #4ade80 #f1f5f9; /* Firefox - light green thumb, light gray track */
  -webkit-overflow-scrolling: touch; /* iOS smooth scrolling */
}

/* Custom scrollbar for webkit browsers */
.featured-scroll-content::-webkit-scrollbar {
  height: 8px;
}

.featured-scroll-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.featured-scroll-content::-webkit-scrollbar-thumb {
  background: #4ade80; /* Light green color */
  border-radius: 4px;
  border: 1px solid #22c55e;
}

.featured-scroll-content::-webkit-scrollbar-thumb:hover {
  background: #22c55e; /* Darker green on hover */
}

.featured-item {
  flex: 0 0 280px;
  min-width: 280px;
  max-width: 280px;
}

.featured-empty {
  flex: 1;
  min-width: 100%;
  background: #f9fafb;
  border-radius: 12px;
  border: 2px dashed #e5e7eb;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .featured-item {
    flex: 0 0 240px;
    min-width: 240px;
    max-width: 240px;
  }

  .featured-scroll-content {
    gap: 12px;
    padding: 0 12px 12px 12px;
  }

  .blog-featured-container {
    padding: 0 12px 12px 12px;
  }
}

@media (max-width: 480px) {
  .featured-item {
    flex: 0 0 200px;
    min-width: 200px;
    max-width: 200px;
  }

  .featured-scroll-content {
    gap: 8px;
    padding: 0 8px 8px 8px;
  }

  .blog-featured-container {
    padding: 0 8px 8px 8px;
  }
}
</style>
