<template>
  <div class="feed-container">
    <div class="row q-col-gutter-md">
      <!-- Mobile Filter Toggle Button -->
      <div class="col-12 mobile-filter-toggle">
        <div class="q-px-md">
          <q-btn
            color="primary"
            :icon="showMobileFilters ? 'filter_list_off' : 'filter_list'"
            :label="showMobileFilters ? 'Hide Filters' : 'Show Filters'"
            class="full-width"
            @click="showMobileFilters = !showMobileFilters"
          />
        </div>
      </div>

      <!-- Left Sidebar - AI Filters -->
      <div
        class="col-12 col-md-3 left-sidebar"
        :class="{ 'mobile-filters-visible': showMobileFilters }"
      >
        <AIFilterComponent
          :context="`community-${activeTab}`"
          :quick-filters="currentFilterConfig.quickFilters"
          :category-filters="currentFilterConfig.categoryFilters"
          :specialized-filters="currentFilterConfig.specializedFilters"
          @trigger-activated="onTriggerActivated"
          @trigger-success="onTriggerSuccess"
          @trigger-error="onTriggerError"
        />
      </div>

      <!-- Main Content Area -->
      <div class="col-12 col-md-9 main-content">
        <q-card flat bordered class="full-width">
          <!-- Tabs - Hidden on mobile -->
          <q-tabs
            v-model="activeTab"
            class="text-primary sticky-tabs community-tabs desktop-only"
            active-color="primary"
            indicator-color="primary"
            align="justify"
            narrow-indicator
            dense
            mobile-arrows
            @update:model-value="(newTab: string) => handleTabChange(newTab, activeTab)"
          >
            <q-tab name="feed" label="Feed" icon="rss_feed" />
            <q-tab name="profiles" label="Profiles" icon="people" />
            <q-tab name="events" label="Events" icon="event" />
            <q-tab name="blog" label="Blog" icon="article" />
            <q-tab name="marketplace" label="Marketplace" icon="storefront" />
            <q-tab name="groups" label="Groups" icon="groups" />
          </q-tabs>

          <q-separator class="desktop-only" />

          <!-- Tab Panels -->
          <q-tab-panels v-model="activeTab" animated>
            <!-- Feed Tab -->
            <q-tab-panel name="feed">
              <div class="row items-center justify-between q-mb-md">
                <div>
                  <div class="text-h6">Community Feed</div>
                </div>
              </div>



              <div v-if="loading" class="text-center q-pa-lg">
                <q-spinner color="primary" size="3em" />
                <p>Loading feed...</p>
              </div>

              <div v-else-if="error" class="text-center q-pa-lg text-negative">
                <p>{{ error }}</p>
                <q-btn color="primary" label="Retry" @click="refreshFeed" />
              </div>

              <div v-else-if="posts.length === 0" class="text-center q-pa-lg">
                <p>No posts found. Be the first to share something with the community!</p>
                <p class="text-caption">Debug: posts.length = {{ posts.length }}, loading = {{ loading }}, error = {{ error }}</p>
                <q-btn
                  color="primary"
                  icon="refresh"
                  label="Refresh Feed"
                  @click="refreshFeed"
                />
              </div>

              <div v-else ref="feedContainer">
                <p class="text-caption q-mb-md">Debug: Found {{ posts.length }} posts</p>
                <div class="row q-col-gutter-md">
                  <div v-for="post in posts" :key="post.id" class="col-12 col-md-8">
                    <PostCard
                      :post="post"
                      @comment="commentOnPost"
                      @share="sharePost"
                      @save="savePost"
                      @viewDetails="viewPostDetails"
                      @register="registerForPostEvent"
                      @apply="applyForOpportunity"
                      @join="joinPostGroup"
                      @contact="contactPostSeller"
                      @connect="connectWithPostAuthor"
                      @collaborate="collaborateWithPost"
                      @requestMentorship="requestMentorshipFromPost"
                      @applyForFunding="applyForFundingFromPost"
                      @readMore="viewPostDetails"
                    />
                  </div>
                </div>

                <!-- Loading indicator for infinite scroll -->
                <div v-if="loadingMore" class="text-center q-py-md">
                  <q-spinner color="primary" size="2em" />
                  <p class="q-ma-none">Loading more posts...</p>
                </div>

                <!-- End of feed message -->
                <div v-if="!hasMorePosts && posts.length > 0" class="text-center q-py-md text-grey">
                  <p class="q-ma-none">You've reached the end of the feed</p>
                </div>
              </div>
            </q-tab-panel>

            <!-- Profiles Directory Tab -->
            <q-tab-panel name="profiles">


              <div class="row items-center justify-between q-mb-md">
                <div>
                  <div class="text-h6">All Profiles Directory</div>
                  <p class="text-subtitle1 q-mb-none">Connect with members of our community</p>
                </div>
              </div>

              <div v-if="loadingProfiles" class="text-center q-pa-lg">
                <q-spinner color="primary" size="3em" />
                <p>Loading profiles...</p>
              </div>

              <div v-else-if="profilesError" class="text-center q-pa-lg text-negative">
                <p>{{ profilesError }}</p>
                <q-btn color="primary" label="Retry" @click="fetchProfiles" />
              </div>

              <div v-else-if="profiles.length === 0" class="text-center q-pa-lg">
                <p>No profiles found matching your criteria.</p>
              </div>

              <div v-else class="row q-col-gutter-md">
                <div v-for="profile in profiles" :key="profile.id || profile.user_id" class="col-12 col-md-6 col-lg-4">
                  <ProfileCard
                    :profile="profile"
                    @view="viewProfile"
                    @message="messageProfile"
                    @connect="connectWithProfile"
                  />
                </div>
              </div>

              <div class="text-center q-mt-md">
                <q-btn
                  v-if="hasMoreProfiles"
                  color="primary"
                  outline
                  label="Load More"
                  @click="loadMoreProfiles"
                  :loading="loadingMoreProfiles"
                />
              </div>
            </q-tab-panel>

            <!-- Events Tab -->
            <q-tab-panel name="events">


              <div class="row items-center justify-between q-mb-sm">
                <div>
                  <div class="text-h6">All Events & Programs</div>
                  <p class="text-subtitle1 q-mb-none">Upcoming events and programs from our community</p>
                </div>
                <q-btn
                  flat
                  round
                  dense
                  icon="refresh"
                  color="grey-6"
                  size="sm"
                  @click="refreshAllEvents"
                  :loading="loadingEvents"
                  class="refresh-btn"
                >
                  <q-tooltip>Refresh all events</q-tooltip>
                </q-btn>
              </div>

              <div v-if="loadingEvents" class="text-center q-pa-lg">
                <q-spinner color="primary" size="3em" />
                <p>Loading events...</p>
              </div>

              <div v-else-if="eventsError" class="text-center q-pa-lg text-negative">
                <p>{{ eventsError }}</p>
                <q-btn color="primary" label="Retry" @click="fetchEvents" />
              </div>

              <div v-else-if="events.length === 0" class="text-center q-pa-lg">
                <p>No events found matching your criteria.</p>
              </div>

              <div v-else class="row q-col-gutter-md">
                <div v-for="event in events" :key="event.id" class="col-12 col-md-6 col-lg-4">
                  <EventCard
                    :event="event"
                    @register="registerForEvent"
                    @share="shareEvent"
                    @save="saveEvent"
                    @addToCalendar="addEventToCalendar"
                    @viewDetails="viewPostDetails"
                    @comment="commentOnPost"
                  />
                </div>
              </div>

              <div class="text-center q-mt-md">
                <q-btn
                  v-if="hasMoreEvents"
                  color="primary"
                  outline
                  label="Load More"
                  @click="loadMoreEvents"
                  :loading="loadingMoreEvents"
                />
              </div>
            </q-tab-panel>

            <!-- Blog Articles Tab -->
            <q-tab-panel name="blog">
              <BlogLayout
                :initial-filters="blogFilters"
              />
            </q-tab-panel>

            <!-- Marketplace Tab -->
            <q-tab-panel name="marketplace">


              <div class="text-h6">All Marketplace Listings</div>
              <p class="text-subtitle1 q-mb-md">Products, services, and opportunities</p>

              <!-- Marketplace content will be similar to other tabs -->
              <div v-if="loadingMarketplace" class="text-center q-pa-lg">
                <q-spinner color="primary" size="3em" />
                <p>Loading marketplace listings...</p>
              </div>

              <div v-else-if="marketplaceError" class="text-center q-pa-lg text-negative">
                <p>{{ marketplaceError }}</p>
                <q-btn color="primary" label="Retry" @click="fetchMarketplace" />
              </div>

              <div v-else-if="marketplace.length === 0" class="text-center q-pa-lg">
                <p>No marketplace listings found matching your criteria.</p>
              </div>

              <div v-else class="row q-col-gutter-md">
                <div v-for="item in marketplace" :key="item.id" class="col-12 col-md-6 col-lg-4">
                  <MarketplaceCard
                    :listing="item"
                    @view="viewMarketplaceItem"
                    @share="shareMarketplaceItem"
                    @contact="contactSeller"
                    @save="saveMarketplaceItem"
                  />
                </div>
              </div>

              <div class="text-center q-mt-md">
                <q-btn
                  v-if="hasMoreMarketplace"
                  color="primary"
                  outline
                  label="Load More"
                  @click="loadMoreMarketplace"
                  :loading="loadingMoreMarketplace"
                />
              </div>
            </q-tab-panel>

            <!-- Groups Tab -->
            <q-tab-panel name="groups">
              <div class="text-h6">Groups</div>
              <p class="text-subtitle1 q-mb-md">Join communities of like-minded individuals</p>

              <!-- Notice about group creation being disabled -->
              <q-banner class="bg-blue-1 q-mb-md">
                <template v-slot:avatar>
                  <q-icon name="info" color="info" />
                </template>
                Group creation is temporarily disabled. You can still view and join existing groups.
              </q-banner>

              <!-- Groups content will be similar to other tabs -->
              <div v-if="loadingGroups" class="text-center q-pa-lg">
                <q-spinner color="primary" size="3em" />
                <p>Loading groups...</p>
              </div>

              <div v-else-if="groupsError" class="text-center q-pa-lg text-negative">
                <p>{{ groupsError }}</p>
                <q-btn color="primary" label="Retry" @click="fetchGroups" />
              </div>

              <div v-else-if="groups.length === 0" class="text-center q-pa-lg">
                <p>No groups found matching your criteria.</p>
              </div>

              <div v-else class="row q-col-gutter-md">
                <div v-for="group in groups" :key="group.id" class="col-12 col-md-6 col-lg-4">
                  <GroupCard
                    :group="group"
                    @view="viewGroup"
                    @share="shareGroup"
                    @join="joinGroup"
                    @leave="leaveGroup"
                  />
                </div>
              </div>

              <div class="text-center q-mt-md">
                <q-btn
                  v-if="hasMoreGroups"
                  color="primary"
                  outline
                  label="Load More"
                  @click="loadMoreGroups"
                  :loading="loadingMoreGroups"
                />
              </div>
            </q-tab-panel>


          </q-tab-panels>
        </q-card>
      </div>
    </div>

    <!-- Create Post Button -->
    <div class="create-post-button-container">
      <q-btn
        icon="add"
        :label="isSmallScreen ? null : 'Create Post'"
        class="zb-btn-primary zb-btn-pulse create-post-button"
        :round="isSmallScreen"
        :rounded="!isSmallScreen"
        @click="showCreatePostForm"
        :loading="formLoading"
        :disable="formLoading"
      />
    </div>

    <!-- Direct Post Creation Dialog -->
    <PostCreationDialog
      ref="postCreationDialogRef"
      :active-tab="activeTab"
      @post-created="handlePostCreated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Notify, Dialog } from 'quasar';
import { useFilterStore } from '../../stores/filterStore';
import { usePostsStore } from '../../stores/posts';
import { useNotificationStore } from '../../stores/notifications';
import { useContentInteractions } from '../../composables/useContentInteractions';
import { useUserInteractionsStore } from '../../stores/userInteractions';
import { feedDataService, type PaginationState } from '../../services/feedDataService';
import { supabase } from '../../lib/supabase';
import { getUniversalUsername } from '../../utils/userUtils';
import { useGlobalServicesStore } from '../../stores/globalServices';
import AIFilterComponent from '../ai/AIFilterComponent.vue';
import { aiFilterConfigService } from '../../services/aiFilterConfigService';
import PostCreationDialog from './PostCreationDialog.vue';
import MessageDialog from '../messaging/MessageDialog.vue';
import PostCard from './cards/PostCard.vue';
import ProfileCard from './cards/ProfileCard.vue';
import BlogCard from './cards/BlogCard.vue';
import SuccessStoryCard from './cards/SuccessStoryCard.vue';
import EventCard from './cards/EventCard.vue';
import GroupCard from './cards/GroupCard.vue';
import MarketplaceCard from './cards/MarketplaceCard.vue';
import SmartAITriggers from '../ai/SmartAITriggers.vue';
import BlogLayout from '../blog/BlogLayout.vue';


// Utility function for debouncing
function debounce(fn: Function, delay: number) {
  let timeout: number | null = null;
  return function(...args: any[]) {
    if (timeout !== null) {
      clearTimeout(timeout);
    }
    timeout = window.setTimeout(() => {
      fn(...args);
      timeout = null;
    }, delay);
  };
}

const route = useRoute();
const router = useRouter();

// Active tab state
const activeTab = ref('feed');
// Keep track of the previous tab for scroll position restoration
const previousTab = ref('feed');

// AI Filter Configuration
const currentFilterConfig = computed(() => {
  return aiFilterConfigService.getFilterConfig(activeTab.value)
});

// Reference to the post creation dialog
const postCreationDialogRef = ref(null);

// Mobile filters visibility state
const showMobileFilters = ref(false);

// The offset value (60) represents the height of the header/toolbar
// This ensures the tabs stick just below the header when scrolling

// Tab initialization will be handled in the consolidated onMounted hook

// We would normally watch for tab changes, but we'll use a different approach
// to avoid issues with the watch import

// Simple tab change handler - ALWAYS make database calls
async function handleTabChange(newTab: string, oldTab?: string): Promise<void> {
  console.log(`Tab clicked: ${newTab}`);

  // Update active tab immediately
  activeTab.value = newTab;

  // Update URL query parameter
  const newQuery: Record<string, any> = { ...route.query, tab: newTab };
  router.replace({ query: newQuery }).catch(err => {
    console.error('Error updating URL:', err);
  });

  // Update filter store
  filterStore.setActiveTab(newTab, false);

  // Reset pagination for the new tab
  resetPaginationForTab(newTab);

  // ALWAYS load data for the new tab - no conditions
  console.log('Loading data for clicked tab:', newTab);
  await loadTabData(newTab);

  // Set up infinite scroll for feed tab
  if (newTab === 'feed' && feedContainer.value) {
    setupInfiniteScroll();
  }
}

// Helper function to reset pagination state
function resetPaginationForTab(tab: string) {
  if (paginationState.value[tab]) {
    paginationState.value[tab].page = 1;
    paginationState.value[tab].cursor = null;
    paginationState.value[tab].scrollPosition = 0;
    console.log(`Reset pagination for tab: ${tab}`);
  }
}

// Initial tab value will be set in the consolidated onMounted hook

// Initialize stores
const postsStore = usePostsStore();
const notifications = useNotificationStore();

// Initialize services and composables
const contentInteractions = useContentInteractions();
const userInteractionsStore = useUserInteractionsStore();
const globalServices = useGlobalServicesStore();
const connectionService = globalServices.connectionService;
const cache = globalServices.cacheService;

// Data states
const posts = ref([]);
const profiles = ref([]);
const articles = ref([]);
const events = ref([]);
const groups = ref([]);
const marketplace = ref([]);

// Refs for infinite scroll
const feedContainer = ref(null);
const scrollObserver = ref<IntersectionObserver | null>(null);

// Loading states - Initialize with false, will be set to true when loading starts
const loading = ref(false);
const loadingMore = ref(false);
const loadingProfiles = ref(false);
const loadingMoreProfiles = ref(false);
const loadingArticles = ref(false);
const loadingMoreArticles = ref(false);

// Unified cache will handle tab data caching automatically
const loadingEvents = ref(false);
const loadingMoreEvents = ref(false);
const loadingGroups = ref(false);
const loadingMoreGroups = ref(false);
const loadingMarketplace = ref(false);
const loadingMoreMarketplace = ref(false);
const formLoading = ref(false);

// Error states
const error = ref<string | null>(null);
const profilesError = ref<string | null>(null);
const articlesError = ref<string | null>(null);
const eventsError = ref<string | null>(null);
const groupsError = ref<string | null>(null);
const marketplaceError = ref<string | null>(null);

// Pagination states
const hasMorePosts = ref(true);
const hasMoreProfiles = ref(true);
const hasMoreArticles = ref(true);
const hasMoreEvents = ref(true);
const hasMoreGroups = ref(true);
const hasMoreMarketplace = ref(true);

// Use the consolidated pagination state type
interface TabPaginationState {
  feed: PaginationState;
  profiles: PaginationState;
  blog: PaginationState;
  events: PaginationState;
  groups: PaginationState;
  marketplace: PaginationState;
  [key: string]: PaginationState; // Index signature for dynamic access
}

// Pagination tracking for each tab - MAX 10 posts per page
const MAX_POSTS_PER_PAGE = 10;

const paginationState = ref<TabPaginationState>({
  feed: { page: 1, limit: MAX_POSTS_PER_PAGE, cursor: null, scrollPosition: 0 },
  profiles: { page: 1, limit: MAX_POSTS_PER_PAGE, cursor: null, scrollPosition: 0 },
  blog: { page: 1, limit: MAX_POSTS_PER_PAGE, cursor: null, scrollPosition: 0 },
  events: { page: 1, limit: MAX_POSTS_PER_PAGE, cursor: null, scrollPosition: 0 },
  groups: { page: 1, limit: MAX_POSTS_PER_PAGE, cursor: null, scrollPosition: 0 },
  marketplace: { page: 1, limit: MAX_POSTS_PER_PAGE, cursor: null, scrollPosition: 0 }
});

// Use the filter store
const filterStore = useFilterStore();

// Computed property for blog filters with safe defaults
const blogFilters = computed(() => ({
  searchQuery: filterStore.currentFilters?.searchQuery || '',
  category: filterStore.currentFilters?.category || '',
  tags: filterStore.currentFilters?.blogCategories || []
}));

// Define a ref for small screen detection
const isSmallScreen = ref(window.innerWidth < 600);

// Helper function to set loading state for specific tabs
function setLoadingStateForTab(tab: string, isLoading: boolean) {
  switch (tab) {
    case 'feed':
      loading.value = isLoading;
      break;
    case 'profiles':
      loadingProfiles.value = isLoading;
      break;
    case 'blog':
      loadingArticles.value = isLoading;
      break;
    case 'events':
      loadingEvents.value = isLoading;
      break;
    case 'groups':
      loadingGroups.value = isLoading;
      break;
    case 'marketplace':
      loadingMarketplace.value = isLoading;
      break;
  }
}

// Simple notification function
function showNotification(config: any) {
  // Use Quasar's notification system
  Notify.create(config);
}

// Simple onMounted - ALWAYS load data for current tab
onMounted(async () => {
  console.log('FeedContainer mounted - starting data load');

  // Set initial tab from URL
  const tabParam = route.query.tab as string;
  activeTab.value = ['feed', 'profiles', 'blog', 'events', 'groups', 'marketplace'].includes(tabParam)
    ? tabParam : 'feed';

  console.log('Active tab set to:', activeTab.value);

  // Set filter store tab
  filterStore.setActiveTab(activeTab.value, false);

  // Set up resize handler
  const handleResize = () => isSmallScreen.value = window.innerWidth < 600;
  handleResize();
  window.addEventListener('resize', handleResize);

  // ALWAYS load data for current tab - no conditions
  console.log('Loading data for tab:', activeTab.value);
  await loadTabData(activeTab.value);

  // Setup infinite scroll for feed
  if (activeTab.value === 'feed') {
    setupInfiniteScroll();
  }
});

// Cleanup when component unmounts
onBeforeUnmount(() => {
  console.log('FeedContainer: Component unmounting, cleaning up resources');
  cleanupInfiniteScroll();
  // Unified cache handles its own cleanup automatically
});

// Set up intersection observer for infinite scrolling with debounce
const loadMorePostsDebounced = debounce(loadMorePosts, 300);

// Cleanup function for intersection observer
function cleanupInfiniteScroll() {
  if (scrollObserver.value) {
    scrollObserver.value.disconnect();
    scrollObserver.value = null;
  }

  // Remove sentinel element if it exists
  const sentinelElement = document.querySelector('.infinite-scroll-sentinel');
  if (sentinelElement) {
    sentinelElement.remove();
  }
}

// Removed watch to prevent conflicts - infinite scroll setup is handled in handleTabChange

function setupInfiniteScroll() {
  // Disconnect any existing observer first
  if (scrollObserver.value) {
    scrollObserver.value.disconnect();
    scrollObserver.value = null;
  }

  // Only create a new observer if we have a feed container and are on the feed tab
  if (!scrollObserver.value && feedContainer.value && activeTab.value === 'feed') {
    console.log('Setting up infinite scroll observer');

    // Create a sentinel element for intersection observation
    let sentinelElement = document.querySelector('.infinite-scroll-sentinel') as HTMLElement;
    if (!sentinelElement) {
      sentinelElement = document.createElement('div') as HTMLElement;
      sentinelElement.className = 'infinite-scroll-sentinel';
      sentinelElement.style.height = '1px';
      sentinelElement.style.width = '100%';
      sentinelElement.style.position = 'absolute';
      sentinelElement.style.bottom = '400px';
      sentinelElement.style.pointerEvents = 'none';
      feedContainer.value.appendChild(sentinelElement);
    }

    // Create a new intersection observer
    scrollObserver.value = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        // Only trigger if intersecting, not already loading, has more posts, and on feed tab
        if (entry.isIntersecting &&
            !loadingMore.value &&
            hasMorePosts.value &&
            activeTab.value === 'feed' &&
            posts.value.length > 0) {
          console.log('Infinite scroll triggered, loading more posts');
          // Load more posts with debounce to prevent multiple calls
          loadMorePostsDebounced();
        }
      },
      {
        // Options
        root: null, // Use the viewport as the root
        rootMargin: '0px 0px 200px 0px', // Start loading when the sentinel is 200px from the bottom
        threshold: 0.1 // Trigger when at least 10% of the sentinel is visible
      }
    );

    // Observe the sentinel element instead of the container
    scrollObserver.value.observe(sentinelElement);
  }
}

// Methods
// Removed duplicate applyFilters functions - now handled by the simplified version above

// AI Filter handlers - no traditional filtering needed since AI handles search
// The AI triggers will directly interact with the AI chat system

// AI trigger event handlers
function onTriggerActivated(triggerKey: string) {
  console.log('AI trigger activated:', triggerKey);
}

function onTriggerSuccess(result: any) {
  console.log('AI trigger success:', result);
}

function onTriggerError(error: any) {
  console.error('AI trigger error:', error);
}

// Featured section handlers
function handleFeaturedItemClick(type: string, item: any) {
  console.log('Featured item clicked:', type, item);

  // The FeaturedSection component already handles navigation
  // No notification needed for better UX
}

function handleFeaturedEventClick(event: any) {
  console.log('Featured event clicked:', event);
  // Navigation is handled by the component, no notification needed
}

function handleFeaturedMarketplaceClick(item: any) {
  console.log('Featured marketplace item clicked:', item);
  // Navigation is handled by the component, no notification needed
}

function handleFeaturedProfileClick(profile: any) {
  console.log('Featured profile clicked:', profile);
  // Navigation is handled by the component, no notification needed
}

// Helper function to determine post type is no longer needed as we use the database schema

// Simple data loading with smart caching to prevent unnecessary database calls
async function loadTabData(tab: string): Promise<void> {
  console.log(`🔄 LOADING DATA FOR TAB: ${tab}`);

  // Temporarily disable cache to debug the issue
  // const cacheKey = `feed:tab:${tab}`;
  // const cached = cache.get<boolean>(cacheKey);
  // if (cached) {
  //   console.log(`📋 FeedContainer: Using cached data for ${tab}`);
  //   return;
  // }

  // Set loading state
  setLoadingStateForTab(tab, true);

  try {
    // ALWAYS load data based on tab type - no conditions
    console.log(`📡 Making database call for ${tab}`);
    switch (tab) {
      case 'feed':
        await refreshFeed();
        console.log('✅ Feed posts loaded:', posts.value.length);
        break;
      case 'profiles':
        await fetchProfiles();
        console.log('✅ Profiles loaded:', profiles.value.length);
        break;
      case 'blog':
        await fetchArticles();
        console.log('✅ Blog articles loaded:', articles.value.length);
        break;
      case 'events':
        await fetchEvents();
        console.log('✅ Events loaded:', events.value.length);
        break;
      case 'groups':
        await fetchGroups();
        console.log('✅ Groups loaded:', groups.value.length);
        break;
      case 'marketplace':
        await fetchMarketplace();
        console.log('✅ Marketplace items loaded:', marketplace.value.length);
        break;
    }
  } catch (error) {
    console.error(`❌ Error loading ${tab} data:`, error);
    // Set error state for the tab
    const errorMessage = 'Failed to load data. Please try again.';
    switch (tab) {
      case 'feed': error.value = errorMessage; break;
      case 'profiles': profilesError.value = errorMessage; break;
      case 'blog': articlesError.value = errorMessage; break;
      case 'events': eventsError.value = errorMessage; break;
      case 'groups': groupsError.value = errorMessage; break;
      case 'marketplace': marketplaceError.value = errorMessage; break;
    }
  } finally {
    setLoadingStateForTab(tab, false);
    // Temporarily disable cache to debug the issue
    // cache.set(cacheKey, true, {
    //   ttl: 30 * 1000, // 30 seconds
    //   storage: 'memory'
    // });
    // console.log(`📋 FeedContainer: Cached data for ${tab}`);
  }
}

// Feed tab methods - ALWAYS make database calls
async function refreshFeed() {
  // console.log('🔄 refreshFeed called - making database call');

  loading.value = true;
  error.value = null;

  try {
    // Reset pagination state for fresh load
    const pagination = paginationState.value.feed;
    pagination.page = 1;
    pagination.cursor = null;

    console.log('📡 Calling feedDataService.fetchFeedPosts...');
    const result = await feedDataService.fetchFeedPosts(pagination, filterStore.currentFilters);
    console.log('✅ Feed data received:', result.posts.length, 'posts, hasMore:', result.hasMore);

    posts.value = result.posts;
    hasMorePosts.value = result.hasMore;

    if (result.nextCursor) {
      pagination.cursor = result.nextCursor;
    }
  } catch (err) {
    console.error('❌ Error loading feed:', err);
    error.value = 'Failed to load posts. Please try again.';
    posts.value = [];
    hasMorePosts.value = false;
  } finally {
    loading.value = false;
  }
}

async function loadMorePosts() {
  if (loadingMore.value || loading.value || !hasMorePosts.value) return;

  loadingMore.value = true;
  const currentPage = paginationState.value.feed.page;

  try {
    paginationState.value.feed.page++;
    console.log('🔄 loadMorePosts: Loading page', paginationState.value.feed.page, 'current posts:', posts.value.length);

    const result = await feedDataService.fetchFeedPosts(paginationState.value.feed, filterStore.currentFilters);

    // The store handles appending automatically, we just need to get the updated posts
    posts.value = postsStore.filteredPosts;
    hasMorePosts.value = result.hasMore;

    console.log('✅ loadMorePosts: Page', paginationState.value.feed.page, 'loaded. Total posts:', posts.value.length, 'hasMore:', hasMorePosts.value);
  } catch (err) {
    console.error('❌ Error loading more posts:', err);
    paginationState.value.feed.page = currentPage;
  } finally {
    loadingMore.value = false;
  }
}

// Profiles tab methods - ALWAYS make database calls
async function fetchProfiles() {
  loadingProfiles.value = true;
  profilesError.value = null;

  try {
    const pagination = paginationState.value.profiles;
    const result = await feedDataService.fetchProfiles(pagination, filterStore.currentFilters);

    profiles.value = result.profiles || [];
    hasMoreProfiles.value = result.hasMore;
  } catch (err) {
    console.error('❌ Error loading profiles:', err);
    profilesError.value = 'Failed to load profiles. Please try again.';
    profiles.value = [];
  } finally {
    loadingProfiles.value = false;
  }
}

// Handle post creation
async function handlePostCreated(post: any): Promise<void> {
  // Reset form loading state
  formLoading.value = false;

  // Invalidate relevant cache entries
  cache.invalidate('feed:tab:*');
  console.log('FeedContainer: Cache invalidated after post creation');

  // Refresh the feed to get the latest posts from the database
  await refreshFeed();

  // Show success notification
  showNotification({
    color: 'positive',
    message: 'Post created successfully!',
    icon: 'check_circle',
    position: 'top',
    timeout: 2000
  });

  // If the post type doesn't match the current tab, switch to the appropriate tab
  const postTypeToTabMap: Record<string, string> = {
    // Handle both uppercase and lowercase post types
    GENERAL: 'feed',
    general: 'feed',
    OPPORTUNITY: 'feed',
    opportunity: 'feed',
    BLOG: 'blog',
    blog: 'blog',
    EVENT: 'events',
    event: 'events',
    GROUP: 'groups',
    group: 'groups',
    MARKETPLACE: 'marketplace',
    marketplace: 'marketplace'
  };

  const targetTab = postTypeToTabMap[post.postType];
  if (targetTab && targetTab !== activeTab.value) {
    activeTab.value = targetTab;
  }
}

async function loadMoreProfiles() {
  loadingMoreProfiles.value = true;

  try {
    // Increment the page number for the profiles tab
    paginationState.value.profiles.page++;

    // Call fetchProfiles with the updated pagination state
    await fetchProfiles();
  } catch (err) {
    console.error('Error loading more profiles:', err);
    // Revert the page increment on error
    paginationState.value.profiles.page = Math.max(1, paginationState.value.profiles.page - 1);
  } finally {
    loadingMoreProfiles.value = false;
  }
}

// Blog tab methods - ALWAYS make database calls
async function fetchArticles() {
  // console.log('🔄 fetchArticles called - making database call');

  loadingArticles.value = true;
  articlesError.value = null;

  try {
    const pagination = paginationState.value.blog;
    const filters = filterStore.currentFilters;

    // console.log('📡 Calling feedDataService.fetchArticles...');
    const result = await feedDataService.fetchArticles(pagination, {
      searchQuery: filters.searchQuery,
      tags: filters.blogCategories
    });
    // console.log('✅ Articles data received:', result);

    articles.value = result.articles;
    hasMoreArticles.value = result.hasMore;

    if (result.nextCursor) {
      pagination.cursor = result.nextCursor;
    }

    console.log('Articles loaded:', articles.value.length);
  } catch (err) {
    console.error('❌ Error fetching articles:', err);
    articlesError.value = 'Failed to load articles. Please try again.';
    articles.value = [];
  } finally {
    loadingArticles.value = false;
  }
}

async function loadMoreArticles() {
  loadingMoreArticles.value = true;

  try {
    // Increment the page number for the blog tab
    paginationState.value.blog.page++;

    // Call fetchArticles with the updated pagination state
    await fetchArticles();
  } catch (err) {
    console.error('Error loading more articles:', err);
    // Revert the page increment on error
    paginationState.value.blog.page = Math.max(1, paginationState.value.blog.page - 1);
  } finally {
    loadingMoreArticles.value = false;
  }
}

// Events tab methods - ALWAYS make database calls
async function fetchEvents() {
  console.log('🔄 fetchEvents called - making database call');

  loadingEvents.value = true;
  eventsError.value = null;

  try {
    const pagination = paginationState.value.events;
    const filters = filterStore.currentFilters;

    console.log('📡 Calling feedDataService.fetchEvents...');
    const result = await feedDataService.fetchEvents(pagination, filters);
    console.log('✅ Events data received:', result);

    events.value = result.events;
    hasMoreEvents.value = result.hasMore;

    console.log('Events loaded:', events.value.length);
  } catch (err) {
    console.error('❌ Error fetching events:', err);
    eventsError.value = 'Failed to load events. Please try again.';
    events.value = [];
  } finally {
    loadingEvents.value = false;
  }
}

async function loadMoreEvents() {
  loadingMoreEvents.value = true;

  try {
    // Increment the page number for the events tab
    paginationState.value.events.page++;

    // Call fetchEvents with the updated pagination state
    await fetchEvents();
  } catch (err) {
    console.error('Error loading more events:', err);
    // Revert the page increment on error
    paginationState.value.events.page = Math.max(1, paginationState.value.events.page - 1);
  } finally {
    loadingMoreEvents.value = false;
  }
}

async function refreshAllEvents() {
  console.log('🔄 Refreshing all events...');

  // Reset pagination to start fresh
  paginationState.value.events.page = 1;
  paginationState.value.events.cursor = null;

  // Clear any existing error state
  eventsError.value = null;

  // Fetch events from the beginning
  await fetchEvents();
}

// Groups tab methods - ALWAYS make database calls
async function fetchGroups() {
  console.log('🔄 fetchGroups called - making database call');

  loadingGroups.value = true;
  groupsError.value = null;

  try {
    const pagination = paginationState.value.groups;
    const filters = filterStore.currentFilters;

    console.log('📡 Calling feedDataService.fetchGroups...');
    const result = await feedDataService.fetchGroups(pagination, {
      searchQuery: filters.searchQuery,
      tags: filters.groupCategories
    });
    console.log('✅ Groups data received:', result);

    groups.value = result.groups;
    hasMoreGroups.value = result.hasMore;

    if (result.nextCursor) {
      pagination.cursor = result.nextCursor;
    }

    console.log('Groups loaded:', groups.value.length);
  } catch (err) {
    console.error('❌ Error fetching groups:', err);
    groupsError.value = 'Failed to load groups. Please try again.';
    groups.value = [];
  } finally {
    loadingGroups.value = false;
  }
}

async function loadMoreGroups() {
  loadingMoreGroups.value = true;

  try {
    // Increment the page number for the groups tab
    paginationState.value.groups.page++;

    // Call fetchGroups with the updated pagination state
    await fetchGroups();
  } catch (err) {
    console.error('Error loading more groups:', err);
    // Revert the page increment on error
    paginationState.value.groups.page = Math.max(1, paginationState.value.groups.page - 1);
  } finally {
    loadingMoreGroups.value = false;
  }
}

// Marketplace tab methods - ALWAYS make database calls
async function fetchMarketplace() {
  console.log('🔄 fetchMarketplace called - making database call');

  loadingMarketplace.value = true;
  marketplaceError.value = null;

  try {
    const pagination = paginationState.value.marketplace;
    const filters = filterStore.currentFilters;

    console.log('📡 Calling feedDataService.fetchMarketplace...');
    const result = await feedDataService.fetchMarketplace(pagination, filters);
    console.log('✅ Marketplace data received:', result);

    marketplace.value = result.marketplace;
    hasMoreMarketplace.value = result.hasMore;

    console.log('Marketplace listings loaded:', marketplace.value.length);
  } catch (err) {
    console.error('❌ Error fetching marketplace listings:', err);
    marketplaceError.value = 'Failed to load marketplace listings. Please try again.';
    marketplace.value = [];
  } finally {
    loadingMarketplace.value = false;
  }
}

async function loadMoreMarketplace() {
  loadingMoreMarketplace.value = true;

  try {
    // Increment the page number for the marketplace tab
    paginationState.value.marketplace.page++;

    // Call fetchMarketplace
    await fetchMarketplace();
  } catch (err) {
    console.error('Error loading more marketplace listings:', err);
    // Revert the page increment on error
    paginationState.value.marketplace.page = Math.max(1, paginationState.value.marketplace.page - 1);
  } finally {
    loadingMoreMarketplace.value = false;
  }
}

// Interaction methods connected to the posts store
// Note: Like functionality is now handled directly by InteractionButtons component

async function commentOnPost(data: { postId: number, comment: string }): Promise<void> {
  try {
    const { postId, comment } = data;
    await postsStore.commentOnPost(postId, comment);
    // Note: Comment count is already updated by the posts store, no need to duplicate here

    showNotification({
      color: 'positive',
      message: 'Comment added successfully!',
      icon: 'check_circle',
      position: 'top',
      timeout: 2000
    });
  } catch (err) {
    console.error('Error commenting on post:', err);
    showNotification({
      color: 'negative',
      message: 'Failed to add comment. Please try again.',
      icon: 'error',
      position: 'top',
      timeout: 2000
    });
  }
}

// Note: Generic share function moved to useContentInteractions composable

function sharePost(postId: number): void {
  const post = posts.value.find(p => p.id === postId);
  contentInteractions.shareContent(postId, 'post', post);
}

// Note: Generic save/favorite function moved to useContentInteractions composable

function savePost(postId: number): void {
  const post = posts.value.find(p => p.id === postId);
  contentInteractions.toggleSaveContent(postId, 'post', post);
}

function viewProfile(profileId: string): void {
  const profile = profiles.value.find(p => p.id === profileId);
  contentInteractions.viewContent(profileId, 'profile', profile);
}

async function messageProfile(profileId: string): Promise<void> {
  console.log('Messaging profile:', profileId);

  // Check if user is authenticated
  const { data } = await supabase.auth.getUser();
  const user = data.user;

  if (!user) {
    notifications.warning('Please sign in to send messages');
    router.push({ name: 'sign-in', query: { redirect: router.currentRoute.value.fullPath } });
    return;
  }

  // Check if this is the user's own profile
  if (user.id === profileId) {
    notifications.warning('You cannot message yourself');
    return;
  }

  // Open message dialog
  const profile = profiles.value.find(p => p.id === profileId || p.user_id === profileId);
  Dialog.create({
    component: MessageDialog,
    componentProps: {
      userId: profileId,
      userName: profile ? getUniversalUsername(profile) : 'User'
    }
  });
}

async function connectWithProfile(profileId: string): Promise<void> {
  console.log('Connecting with profile:', profileId);

  // Check if user is authenticated
  const { data } = await supabase.auth.getUser();
  const user = data.user;

  if (!user) {
    notifications.warning('Please sign in to connect with other users');
    router.push({ name: 'sign-in', query: { redirect: router.currentRoute.value.fullPath } });
    return;
  }

  // Don't allow connecting with yourself
  if (user.id === profileId) {
    notifications.warning('You cannot connect with yourself');
    return;
  }

  try {
    // Check connection status first
    const status = await connectionService.getConnectionStatus(profileId);

    if (status === 'accepted') {
      notifications.info('You are already connected with this user');
      return;
    } else if (status === 'pending') {
      notifications.info('Your connection request is pending');
      return;
    } else if (status === 'incoming_pending') {
      notifications.info('This user has already sent you a connection request. Check your notifications.');
      return;
    }

    // Show loading notification
    Notify.create({
      message: 'Sending connection request...',
      color: 'info',
      position: 'top',
      timeout: 1000
    });

    // Send connection request
    const success = await connectionService.connectWithUser(profileId);

    if (success) {
      console.log('Connection request sent successfully');
      notifications.success('Connection request sent');

      // Update the profile in the list to show pending status
      const profileIndex = profiles.value.findIndex(p => p.id === profileId || p.user_id === profileId);
      if (profileIndex !== -1) {
        profiles.value[profileIndex].isPending = true;
      }
    } else {
      console.error('Failed to send connection request');
      notifications.error('Failed to send connection request');
    }
  } catch (error) {
    console.error('Error connecting with user:', error);
    notifications.error('An error occurred while trying to connect');
  }
}

function readArticle(articleId: number): void {
  console.log('Read article:', articleId);
}

function shareArticle(articleId: number): void {
  console.log('Share article:', articleId);
}

function saveArticle(articleId: number): void {
  console.log('Save article:', articleId);
}

function registerForEvent(eventId: number): void {
  console.log('Register for event:', eventId);
}

function shareEvent(eventId: number): void {
  const event = events.value.find(e => e.id === eventId);
  contentInteractions.shareContent(eventId, 'event', event);
}

function saveEvent(eventId: number): void {
  const event = events.value.find(e => e.id === eventId);
  contentInteractions.toggleSaveContent(eventId, 'event', event);
}

function addEventToCalendar(eventId: number): void {
  console.log('Add event to calendar:', eventId);
}

// This function is used by the viewPostDetails function when the post is an event
// Keeping it for reference but not directly used
// function viewEvent(eventId: number): void {
//   console.log('View event details:', eventId);
//
//   // In a real implementation, this would navigate to the event details page
//   showNotification({
//     color: 'info',
//     message: 'Viewing details for event #' + eventId,
//     icon: 'event',
//     position: 'top',
//     timeout: 2000
//   });
// }

function viewGroup(groupId: number): void {
  const group = groups.value.find(g => g.id === groupId);
  contentInteractions.viewContent(groupId, 'group', group);
}

function shareGroup(groupId: number): void {
  const group = groups.value.find(g => g.id === groupId);
  contentInteractions.shareContent(groupId, 'group', group);
}

function joinGroup(groupId: number): void {
  contentInteractions.joinGroup(groupId);
}

function leaveGroup(groupId: number): void {
  console.log('Leave group:', groupId);
}

function viewListing(listingId: number): void {
  const listing = marketplace.value.find(l => l.id === listingId);
  // Marketplace posts should be treated as posts, not separate listings
  // This ensures consistent routing to post-details instead of marketplace-listing
  contentInteractions.viewContent(listingId, 'post', {
    ...listing,
    postType: 'marketplace',
    subType: 'marketplace'
  });
}

function shareListing(listingId: number): void {
  const listing = marketplace.value.find(l => l.id === listingId);
  // Marketplace posts should be treated as posts for consistent sharing URLs
  contentInteractions.shareContent(listingId, 'post', {
    ...listing,
    postType: 'marketplace',
    subType: 'marketplace'
  });
}

function favoriteListing(listingId: number): void {
  const listing = marketplace.value.find(l => l.id === listingId);
  // Marketplace posts should be treated as posts for consistent database operations
  contentInteractions.toggleSaveContent(listingId, 'post', {
    ...listing,
    postType: 'marketplace',
    subType: 'marketplace'
  });
}

// Helper function to copy text to clipboard
function copyToClipboard(text: string): void {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text).then(() => {
      showNotification({
        color: 'positive',
        message: 'Link copied to clipboard',
        icon: 'content_copy',
        position: 'top',
        timeout: 2000
      });
    }).catch(err => {
      console.error('Failed to copy to clipboard:', err);
      showNotification({
        color: 'negative',
        message: 'Failed to copy link',
        icon: 'error',
        position: 'top',
        timeout: 2000
      });
    });
  } else {
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand('copy');
      showNotification({
        color: 'positive',
        message: 'Link copied to clipboard',
        icon: 'content_copy',
        position: 'top',
        timeout: 2000
      });
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
      showNotification({
        color: 'negative',
        message: 'Failed to copy link',
        icon: 'error',
        position: 'top',
        timeout: 2000
      });
    }
    document.body.removeChild(textArea);
  }
}

async function contactSeller(listingId: number): Promise<void> {
  const listing = marketplace.value.find(l => l.id === listingId);
  // Marketplace posts should be treated as posts for consistent contact functionality
  await contentInteractions.contactUser(listingId, 'post', {
    ...listing,
    postType: 'marketplace',
    subType: 'marketplace'
  });
}

// New handler methods for post card actions
function viewPostDetails(postId: number): void {
  const post = posts.value.find(p => p.id === postId);
  contentInteractions.viewContent(postId, 'post', post);
}

function registerForPostEvent(postId: number): void {
  contentInteractions.registerForEvent(postId);
}

function applyForOpportunity(postId: number): void {
  contentInteractions.applyForOpportunity(postId);
}

function joinPostGroup(postId: number): void {
  contentInteractions.joinGroup(postId);
}

function contactPostSeller(postId: number): void {
  const post = posts.value.find(p => p.id === postId);
  contentInteractions.contactUser(postId, 'post', post);
}

async function connectWithPostAuthor(postId: number): Promise<void> {
  const post = posts.value.find(p => p.id === postId);
  if (!post || !post.user_id) {
    notifications.error('Unable to connect: Post author not found');
    return;
  }

  // Use the same connection logic as profile connections
  await connectWithProfile(post.user_id);
}

// New handler methods for additional CTAs
function collaborateWithPost(postId: number): void {
  contentInteractions.requestCollaboration(postId);
}

function requestMentorshipFromPost(postId: number): void {
  contentInteractions.requestMentorship(postId);
}

function applyForFundingFromPost(postId: number): void {
  console.log('Apply for funding from post:', postId);

  showNotification({
    color: 'positive',
    message: 'Funding application submitted for post #' + postId,
    icon: 'attach_money',
    position: 'top',
    timeout: 2000
  });
}

function showCreatePostForm() {
  // Set loading state
  formLoading.value = true;

  try {
    // Get the post type based on active tab
    const postType = getPostTypeForTab(activeTab.value);

    // Check if the dialog ref is available
    if (postCreationDialogRef.value) {
      // Set the selected post type in the dialog ref
      postCreationDialogRef.value.openDialog(postType);

      // If this was triggered by a URL parameter, remove the action parameter
      // to prevent reopening the dialog on page refresh
      if (route.query.action === 'create') {
        const newQuery = { ...route.query };
        delete newQuery.action;
        router.replace({ query: newQuery });
      }
    } else {
      console.error('Post creation dialog reference is not available');
      // Show a notification to the user
      showNotification({
        color: 'negative',
        message: 'Unable to open post creation form. Please try again.',
        icon: 'error',
        position: 'top',
        timeout: 2000
      });
    }
  } catch (error) {
    console.error('Error opening post creation form:', error);
    showNotification({
      color: 'negative',
      message: 'An error occurred. Please try again.',
      icon: 'error',
      position: 'top',
      timeout: 2000
    });
  } finally {
    // Reset loading state after a short delay to allow the dialog to open
    setTimeout(() => {
      formLoading.value = false;
    }, 500);
  }
}

// Helper function to get post type based on active tab
function getPostTypeForTab(tab: string): string {
  switch (tab) {
    case 'feed':
      return 'general';
    case 'blog':
      return 'blog';
    case 'events':
      return 'event';
    case 'groups':
      return 'group';
    case 'marketplace':
      return 'marketplace';
    case 'profiles':
      return 'general';
    default:
      return 'general';
  }
}

// AI Trigger Handler
const handleAITrigger = async (triggerKey: string) => {
  try {
    await globalServices.aiChatTriggerService.triggerChat(triggerKey);
  } catch (error) {
    console.error('Error triggering AI chat:', error);
  }
};
</script>

<style scoped>
.feed-container {
  max-width: 1600px;
  margin: 0 auto;
  padding-top: 8px;
}

.left-sidebar {
  position: sticky;
  top: 20px;
}

/* Sticky tabs that remain visible when scrolling */
.sticky-tabs {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: white;
}

/* Desktop tab styling improvements */
@media (min-width: 768px) {
  .q-tab__icon {
    font-size: 1.1rem !important; /* Slightly smaller on desktop */
    margin-bottom: 0.15rem;
  }

  .q-tab__label {
    font-size: 0.85rem;
    font-weight: 500;
    letter-spacing: 0.02em; /* Subtle letter spacing for refinement */
  }

  .q-tab {
    padding: 0.75rem 1rem;
    min-height: 52px;
    transition: all 0.2s ease; /* Smooth transitions */
  }

  .q-tab:hover .q-tab__icon {
    opacity: 1;
    transform: translateY(-1px); /* Subtle hover effect */
  }

  .q-tab:hover .q-tab__label {
    color: var(--q-primary);
  }
}

/* Ensure proper spacing between posts */
.row.q-col-gutter-md > .col-12 {
  margin-bottom: 16px;
}

/* Ensure posts don't overflow */
.q-tab-panel {
  overflow: hidden;
}


/* Create Post Button */
.create-post-button-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9998; /* Lower than AI chat (9999) but higher than navigation */
  text-align: center;
}

.create-post-button {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  padding: 10px 20px;
  border-radius: 24px;
  animation: pulse 2s infinite;
}

/* Pulse animation for the button */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(13, 138, 62, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(13, 138, 62, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(13, 138, 62, 0);
  }
}

/* Mobile filter toggle button - only visible on mobile */
.mobile-filter-toggle {
  display: none;
}

@media (max-width: 767px) {
  .feed-container {
    padding-top: 0;
    padding-left: 0;
    padding-right: 0;
    width: 100%;
    max-width: 100%;
  }
  .mobile-filter-toggle {
    display: block;
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: #fff;
    padding: 8px 0;
    margin-bottom: 0;
    width: 100%;
  }

  .left-sidebar {
    position: static;
    margin-bottom: 16px;
    display: none; /* Hide by default on mobile */
    padding: 0;
  }

  .left-sidebar.mobile-filters-visible {
    display: block; /* Show when toggle is active */
    padding: 0 16px;
  }

  .main-content {
    padding: 0 !important;
    margin-top: 8px;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .main-content .q-card {
    width: 100%;
    margin: 0;
    border-radius: 0;
    max-width: 100%;
  }

  .q-tab {
    min-height: 48px;
    padding: 0 8px;
  }

  .q-tab__label {
    font-size: 0.8rem;
    font-weight: 500;
  }

  .sticky-tabs {
    top: 56px; /* Position below the filter toggle button */
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    width: 100%;
  }

  .q-tabs {
    width: 100%;
  }

  /* Refined tab icons */
  .q-tab__icon {
    font-size: 1.2rem !important; /* Smaller icon size */
    margin-bottom: 0.2rem; /* Reduced spacing between icon and label */
    opacity: 0.85; /* Slightly transparent for a refined look */
  }

  /* Active tab icon styling */
  .q-tab--active .q-tab__icon {
    opacity: 1; /* Full opacity for active tab */
  }

  /* Improve tab padding for a more compact look */
  .q-tab {
    padding: 0.5rem 0.75rem;
    min-height: 48px; /* Slightly reduced height */
  }

  .create-post-button-container {
    bottom: 15px;
    right: 15px;
  }

  .create-post-button {
    font-size: 0.9rem;
  }

  /* Improve card layouts on mobile */
  .col-md-6, .col-lg-4 {
    padding: 8px !important;
  }

  .q-card {
    margin-bottom: 16px;
  }

  .q-tab-panels {
    width: 100%;
  }

  .q-tab-panel {
    padding: 16px !important;
  }

  /* Ensure content doesn't overflow on the left */
  .q-tab-panel > div {
    padding-left: 8px;
    padding-right: 8px;
  }

  /* Center align content */
  .text-h6, .text-subtitle1 {
    padding-left: 8px;
    padding-right: 8px;
    text-align: center;
  }

  /* Fix row gutter issues */
  .row.q-col-gutter-md {
    margin-left: 0;
    margin-right: 0;
  }
}

/* Refresh button styling */
.refresh-btn {
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background-color: rgba(0, 0, 0, 0.04);
  transform: rotate(90deg);
}

/* Hide tabs on mobile */
@media (max-width: 767px) {
  .desktop-only {
    display: none !important;
  }
}
</style>
