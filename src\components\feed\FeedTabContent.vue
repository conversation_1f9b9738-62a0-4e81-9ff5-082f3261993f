<template>
  <div class="feed-tab-content">
    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <q-spinner-dots size="2rem" color="primary" />
      <p>Loading posts...</p>
    </div>

    <!-- Posts List -->
    <div v-else-if="posts.length > 0" class="posts-list">
      <div
        v-for="post in posts"
        :key="post.id"
        class="post-card"
      >
        <q-card flat bordered class="q-mb-md">
          <q-card-section>
            <div class="post-header">
              <UserAvatar :user="post.author" :size="40" />
              <div class="post-meta">
                <h6 class="post-author">{{ post.author?.full_name || 'Anonymous' }}</h6>
                <p class="post-date">{{ formatDate(post.created_at) }}</p>
              </div>
            </div>
          </q-card-section>

          <q-card-section v-if="post.title">
            <h5 class="post-title">{{ post.title }}</h5>
          </q-card-section>

          <q-card-section v-if="post.content">
            <p class="post-content">{{ post.content }}</p>
          </q-card-section>

          <q-card-section v-if="post.featured_image">
            <q-img
              :src="post.featured_image"
              :alt="post.title"
              class="post-image"
              ratio="16/9"
            />
          </q-card-section>

          <q-card-actions>
            <q-btn flat round icon="thumb_up" :label="post.likes_count || 0" />
            <q-btn flat round icon="comment" :label="post.comments_count || 0" />
            <q-btn flat round icon="share" />
          </q-card-actions>
        </q-card>
      </div>

      <!-- Load More Button -->
      <div v-if="hasMore" class="load-more-container">
        <q-btn
          unelevated
          color="primary"
          label="Load More"
          @click="$emit('load-more')"
          :loading="loading"
        />
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="empty-state">
      <q-icon name="dynamic_feed" size="4rem" color="grey-5" />
      <h4>No Posts Yet</h4>
      <p>Be the first to share something with the community!</p>
      <q-btn
        unelevated
        color="primary"
        label="Create Post"
        icon="add"
        @click="openCreateDialog"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import UserAvatar from '../common/UserAvatar.vue'

// Props
interface Props {
  posts: any[]
  loading: boolean
  hasMore: boolean
}

defineProps<Props>()

// Emits
defineEmits<{
  'load-more': []
  'refresh': []
}>()

// Methods
function formatDate(dateString: string) {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) return 'Today'
  if (diffDays === 2) return 'Yesterday'
  if (diffDays <= 7) return `${diffDays} days ago`
  
  return date.toLocaleDateString()
}

function openCreateDialog() {
  // This would typically emit an event to open the create dialog
  console.log('Open create dialog')
}
</script>

<style scoped>
.feed-tab-content {
  padding: 16px;
}

.loading-container {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.posts-list {
  max-width: 600px;
  margin: 0 auto;
}

.post-card {
  margin-bottom: 16px;
}

.post-header {
  display: flex;
  gap: 12px;
  align-items: center;
}

.post-meta {
  flex: 1;
}

.post-author {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.post-date {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

.post-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.post-content {
  margin: 0;
  color: #374151;
  line-height: 1.5;
}

.post-image {
  border-radius: 8px;
  overflow: hidden;
}

.load-more-container {
  text-align: center;
  padding: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
  
  h4 {
    margin: 16px 0 8px 0;
    color: #374151;
  }
  
  p {
    margin: 0 0 24px 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .feed-tab-content {
    padding: 8px;
  }
  
  .posts-list {
    max-width: 100%;
  }
}
</style>
