<template>
  <q-dialog
    v-model="dialogOpen"
    maximized
    transition-show="slide-up"
    transition-hide="slide-down"
  >
    <q-card class="filter-results-dialog">
      <!-- Header -->
      <q-card-section class="dialog-header">
        <div class="row items-center">
          <div class="col">
            <div class="text-h6">{{ dialogTitle }}</div>
            <div class="text-caption text-grey-6">
              {{ resultCount }} {{ resultCount === 1 ? 'result' : 'results' }} found
            </div>
          </div>
          <div class="col-auto">
            <q-btn
              flat
              round
              dense
              icon="close"
              v-close-popup
              class="text-grey-6"
            />
          </div>
        </div>
      </q-card-section>

      <q-separator />

      <!-- Scrollable Content -->
      <q-card-section class="dialog-content">
        <q-scroll-area class="full-height">
          <div class="results-container">
            <!-- Feed Results -->
            <div v-if="activeTab === 'feed' && posts.length > 0" class="results-grid">
              <PostCard
                v-for="post in posts"
                :key="post.id"
                :post="post"
                @like="$emit('like', $event)"
                @comment="$emit('comment', $event)"
                @share="$emit('share', $event)"
              />
            </div>

            <!-- Events Results -->
            <div v-else-if="activeTab === 'events' && events.length > 0" class="results-grid">
              <EventCard
                v-for="event in events"
                :key="event.id"
                :event="event"
                @share="$emit('share', $event)"
              />
            </div>

            <!-- Profiles Results -->
            <div v-else-if="activeTab === 'profiles' && profiles.length > 0" class="results-grid">
              <ProfileCard
                v-for="profile in profiles"
                :key="profile.id"
                :profile="profile"
                @contact="$emit('contact', $event)"
                @collaborate="$emit('collaborate', $event)"
              />
            </div>

            <!-- Blog Results -->
            <div v-else-if="activeTab === 'blog' && articles.length > 0" class="results-grid">
              <BlogCard
                v-for="article in articles"
                :key="article.id"
                :article="article"
                @like="$emit('like', $event)"
                @share="$emit('share', $event)"
              />
            </div>

            <!-- Marketplace Results -->
            <div v-else-if="activeTab === 'marketplace' && marketplace.length > 0" class="results-grid">
              <MarketplaceCard
                v-for="item in marketplace"
                :key="item.id"
                :item="item"
                @contact="$emit('contact', $event)"
                @share="$emit('share', $event)"
              />
            </div>

            <!-- No Results -->
            <div v-else class="no-results">
              <q-icon name="search_off" size="4rem" color="grey-4" />
              <div class="text-h6 text-grey-6 q-mt-md">No results found</div>
              <div class="text-body2 text-grey-5 q-mt-sm">
                Try adjusting your filters or search terms
              </div>
            </div>

            <!-- Load More Button -->
            <div v-if="hasMore && resultCount > 0" class="load-more-container q-mt-lg">
              <q-btn
                unelevated
                color="primary"
                label="Load More"
                @click="$emit('load-more')"
                :loading="loading"
                class="full-width"
              />
            </div>
          </div>
        </q-scroll-area>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import PostCard from './cards/PostCard.vue'
import EventCard from './cards/EventCard.vue'
import ProfileCard from './cards/ProfileCard.vue'
import BlogCard from './cards/BlogCard.vue'
import MarketplaceCard from './cards/MarketplaceCard.vue'

// Props
interface Props {
  modelValue: boolean
  activeTab: string
  posts?: any[]
  events?: any[]
  profiles?: any[]
  articles?: any[]
  marketplace?: any[]
  loading?: boolean
  hasMore?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  posts: () => [],
  events: () => [],
  profiles: () => [],
  articles: () => [],
  marketplace: () => [],
  loading: false,
  hasMore: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'like': [id: string]
  'comment': [id: string]
  'share': [id: string]
  'contact': [id: string]
  'collaborate': [id: string]
  'load-more': []
}>()

// Computed
const dialogOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogTitle = computed(() => {
  const tabNames = {
    feed: 'Feed Results',
    events: 'Events Results',
    profiles: 'Profiles Results',
    blog: 'Blog Results',
    marketplace: 'Marketplace Results'
  }
  return tabNames[props.activeTab] || 'Search Results'
})

const resultCount = computed(() => {
  switch (props.activeTab) {
    case 'feed':
      return props.posts.length
    case 'events':
      return props.events.length
    case 'profiles':
      return props.profiles.length
    case 'blog':
      return props.articles.length
    case 'marketplace':
      return props.marketplace.length
    default:
      return 0
  }
})
</script>

<style scoped>
.filter-results-dialog {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.dialog-header {
  flex-shrink: 0;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

.dialog-content {
  flex: 1;
  padding: 0;
  overflow: hidden;
}

.results-container {
  padding: 20px;
}

.results-grid {
  display: grid;
  gap: 20px;
}

/* Desktop grid layouts */
@media (min-width: 769px) {
  .results-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    max-width: 1200px;
    margin: 0 auto;
  }
}

/* Mobile grid layout */
@media (max-width: 768px) {
  .results-grid {
    grid-template-columns: 1fr;
  }
  
  .results-container {
    padding: 16px;
  }
}

.no-results {
  text-align: center;
  padding: 60px 20px;
}

.load-more-container {
  text-align: center;
  max-width: 300px;
  margin: 0 auto;
}
</style>
