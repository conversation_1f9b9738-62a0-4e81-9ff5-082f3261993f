<template>
  <div class="marketplace-tab-content">
    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <q-spinner-dots size="2rem" color="primary" />
      <p>Loading marketplace...</p>
    </div>

    <!-- Marketplace Grid -->
    <div v-else-if="marketplace.length > 0" class="marketplace-grid">
      <div
        v-for="item in marketplace"
        :key="item.id"
        class="marketplace-item"
      >
        <q-card flat bordered>
          <div class="item-image-container" v-if="item.image">
            <q-img
              :src="item.image"
              :alt="item.title"
              class="item-image"
              ratio="1"
            />
            <div class="item-type-badge">
              <q-chip
                size="sm"
                :color="getTypeColor(item.type)"
                text-color="white"
                :label="item.type"
              />
            </div>
          </div>

          <q-card-section>
            <h6 class="item-title">{{ item.title }}</h6>
            <p class="item-description">{{ item.description }}</p>
            
            <div class="item-price">
              <span class="price-amount">${{ item.price }}</span>
              <span class="price-currency">{{ item.currency }}</span>
            </div>

            <div class="item-meta">
              <div class="seller-info">
                <UserAvatar :user="{ full_name: item.seller }" :size="20" />
                <span class="seller-name">{{ item.seller }}</span>
              </div>
              <span class="item-location">{{ item.location }}</span>
            </div>
          </q-card-section>

          <q-card-actions>
            <q-btn
              flat
              color="primary"
              label="View Details"
              @click="viewItem(item.id)"
            />
            <q-btn
              unelevated
              color="primary"
              label="Contact Seller"
              @click="contactSeller(item.userId)"
            />
          </q-card-actions>
        </q-card>
      </div>

      <!-- Load More Button -->
      <div v-if="hasMore" class="load-more-container">
        <q-btn
          unelevated
          color="primary"
          label="Load More"
          @click="$emit('load-more')"
          :loading="loading"
        />
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="empty-state">
      <q-icon name="store" size="4rem" color="grey-5" />
      <h4>No Items Found</h4>
      <p>No marketplace items match your current filters.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import { useRouter } from 'vue-router'
import UserAvatar from '../common/UserAvatar.vue'

// Props
interface Props {
  marketplace: any[]
  loading: boolean
  hasMore: boolean
}

defineProps<Props>()

// Emits
defineEmits<{
  'load-more': []
  'refresh': []
}>()

// Composables
const router = useRouter()

// Methods
function getTypeColor(type: string) {
  const colors: { [key: string]: string } = {
    'PRODUCT': 'blue',
    'SERVICE': 'green',
    'JOB': 'orange',
    'OPPORTUNITY': 'purple'
  }
  return colors[type] || 'grey'
}

function viewItem(itemId: string) {
  router.push(`/virtual-community/marketplace/${itemId}`)
}

function contactSeller(sellerId: string) {
  console.log('Contact seller:', sellerId)
  // Implementation would go here
}
</script>

<style scoped>
.marketplace-tab-content {
  padding: 16px 24px; /* Increase horizontal padding to prevent edge overflow */
}

.loading-container {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.marketplace-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  max-width: 700px; /* Limit to max 2 columns */
  margin: 0 auto;
}

.marketplace-item {
  height: fit-content;
}

.item-image-container {
  position: relative;
  overflow: hidden;
}

.item-image {
  border-radius: 8px 8px 0 0;
}

.item-type-badge {
  position: absolute;
  top: 8px;
  right: 8px;
}

.item-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-description {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #374151;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-price {
  margin-bottom: 12px;
}

.price-amount {
  font-size: 20px;
  font-weight: 700;
  color: #059669;
}

.price-currency {
  font-size: 14px;
  color: #6b7280;
  margin-left: 4px;
}

.item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.seller-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.seller-name {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.item-location {
  font-size: 12px;
  color: #6b7280;
}

.load-more-container {
  grid-column: 1 / -1;
  text-align: center;
  padding: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
  
  h4 {
    margin: 16px 0 8px 0;
    color: #374151;
  }
  
  p {
    margin: 0 0 24px 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .marketplace-tab-content {
    padding: 8px 16px; /* Maintain horizontal padding on mobile */
  }

  .marketplace-grid {
    grid-template-columns: 1fr; /* Force single column on mobile */
    gap: 12px;
    max-width: 100%;
  }
}
</style>
