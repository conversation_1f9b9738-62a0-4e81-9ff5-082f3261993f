<template>
  <div class="profiles-tab-content">
    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <q-spinner-dots size="2rem" color="primary" />
      <p>Loading profiles...</p>
    </div>

    <!-- Profiles Grid -->
    <div v-else-if="profiles.length > 0" class="profiles-grid">
      <div
        v-for="profile in profiles"
        :key="profile.id"
        class="profile-card"
      >
        <q-card flat bordered>
          <q-card-section class="profile-header">
            <UserAvatar :user="profile" :size="60" />
            <h6 class="profile-name">{{ profile.full_name || 'Anonymous' }}</h6>
            <p class="profile-type">{{ profile.profile_type || 'Member' }}</p>
            <p class="profile-location">{{ profile.location || 'Location not specified' }}</p>
          </q-card-section>

          <q-card-section v-if="profile.bio">
            <p class="profile-bio">{{ profile.bio }}</p>
          </q-card-section>

          <q-card-actions align="center">
            <q-btn
              flat
              color="primary"
              label="View Profile"
              @click="viewProfile(profile.id)"
            />
            <q-btn
              flat
              color="primary"
              label="Connect"
              @click="connectWithUser(profile.id)"
            />
          </q-card-actions>
        </q-card>
      </div>

      <!-- Load More Button -->
      <div v-if="hasMore" class="load-more-container">
        <q-btn
          unelevated
          color="primary"
          label="Load More"
          @click="$emit('load-more')"
          :loading="loading"
        />
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="empty-state">
      <q-icon name="people" size="4rem" color="grey-5" />
      <h4>No Profiles Found</h4>
      <p>No community members match your current filters.</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import { useRouter } from 'vue-router'
import UserAvatar from '../common/UserAvatar.vue'

// Props
interface Props {
  profiles: any[]
  loading: boolean
  hasMore: boolean
}

defineProps<Props>()

// Emits
defineEmits<{
  'load-more': []
  'refresh': []
}>()

// Composables
const router = useRouter()

// Methods
function viewProfile(profileId: string) {
  router.push(`/virtual-community/user/${profileId}`)
}

function connectWithUser(userId: string) {
  console.log('Connect with user:', userId)
  // Implementation would go here
}
</script>

<style scoped>
.profiles-tab-content {
  padding: 16px 24px; /* Increase horizontal padding to prevent edge overflow */
}

.loading-container {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.profiles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  max-width: 700px; /* Limit to max 2 columns */
  margin: 0 auto;
}

.profile-card {
  height: fit-content;
}

.profile-header {
  text-align: center;
  padding: 20px;
}

.profile-name {
  margin: 12px 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.profile-type {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #6366f1;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.profile-location {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

.profile-bio {
  margin: 0;
  font-size: 14px;
  color: #374151;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.load-more-container {
  grid-column: 1 / -1;
  text-align: center;
  padding: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
  
  h4 {
    margin: 16px 0 8px 0;
    color: #374151;
  }
  
  p {
    margin: 0 0 24px 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .profiles-tab-content {
    padding: 8px 16px; /* Maintain horizontal padding on mobile */
  }

  .profiles-grid {
    grid-template-columns: 1fr; /* Force single column on mobile */
    gap: 12px;
    max-width: 100%;
  }
}
</style>
