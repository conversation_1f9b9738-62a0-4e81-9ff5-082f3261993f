<template>
  <div class="marketplace-post-form">
    <q-form @submit="handleSubmit" class="q-gutter-md">
      <!-- Title -->
      <q-input
        v-model="formData.title"
        label="Listing Title"
        outlined
        :rules="[val => !!val || 'Title is required', val => val.length <= 100 || 'Maximum 100 characters']"
        counter
        maxlength="100"
      />

      <!-- Listing Type -->
      <q-select
        v-model="formData.listingType"
        :options="listingTypeOptions"
        label="Listing Type"
        outlined
        emit-value
        map-options
        :rules="[val => !!val || 'Listing type is required']"
      >
        <template v-slot:prepend>
          <q-icon name="sell" />
        </template>
      </q-select>

      <!-- Category -->
      <q-select
        v-model="formData.category"
        :options="categoryOptions"
        label="Category"
        outlined
        emit-value
        map-options
        :rules="[val => !!val || 'Category is required']"
      >
        <template v-slot:prepend>
          <q-icon name="category" />
        </template>
      </q-select>

      <!-- Price -->
      <div class="row q-col-gutter-md">
        <div class="col-12 col-md-6">
          <q-input
            v-model.number="formData.price"
            label="Price"
            outlined
            type="number"
            :rules="[val => (formData.listingType !== 'free' && val !== null && val !== undefined) || 'Price is required']"
            :disable="formData.listingType === 'free'"
          >
            <template v-slot:prepend>
              <q-icon name="attach_money" />
            </template>
          </q-input>
        </div>

        <div class="col-12 col-md-6">
          <q-select
            v-model="formData.priceType"
            :options="priceTypeOptions"
            label="Price Type"
            outlined
            emit-value
            map-options
            :disable="formData.listingType === 'free'"
          >
            <template v-slot:prepend>
              <q-icon name="payments" />
            </template>
          </q-select>
        </div>
      </div>

      <!-- Description -->
      <q-input
        v-model="formData.content"
        type="textarea"
        label="Description"
        outlined
        autogrow
        :rules="[val => !!val || 'Description is required', val => val.length <= 2000 || 'Maximum 2000 characters']"
        counter
        maxlength="2000"
        rows="5"
      />

      <!-- Condition (for products) -->
      <q-select
        v-if="formData.listingType === 'product'"
        v-model="formData.condition"
        :options="conditionOptions"
        label="Condition"
        outlined
        emit-value
        map-options
        :rules="[val => !!val || 'Condition is required']"
      >
        <template v-slot:prepend>
          <q-icon name="inventory_2" />
        </template>
      </q-select>

      <!-- Duration (for services) -->
      <q-select
        v-if="formData.listingType === 'service'"
        v-model="formData.duration"
        :options="durationOptions"
        label="Duration"
        outlined
        emit-value
        map-options
      >
        <template v-slot:prepend>
          <q-icon name="schedule" />
        </template>
      </q-select>

      <!-- Contact Information -->
      <q-input
        v-model="formData.contactInfo"
        label="Contact Information"
        outlined
        :rules="[val => !!val || 'Contact information is required']"
      >
        <template v-slot:prepend>
          <q-icon name="contact_mail" />
        </template>
      </q-input>

      <!-- Location -->
      <q-input
        v-model="formData.location"
        label="Location"
        outlined
        :rules="[val => !!val || 'Location is required']"
      >
        <template v-slot:prepend>
          <q-icon name="location_on" />
        </template>
      </q-input>

      <!-- Tags -->
      <div class="q-mb-md">
        <div class="text-subtitle2 q-mb-xs">Tags (select multiple)</div>
        <q-input
          v-model="tagsInput"
          label="Enter tags (comma-separated)"
          outlined
          hint="Enter multiple tags separated by commas, then press Enter or click outside"
          @blur="processTags"
          @keyup.enter="processTags"
        >
          <template v-slot:prepend>
            <q-icon name="local_offer" />
          </template>
          <template v-slot:after>
            <q-badge color="primary" floating>
              {{ formData.tags.length }}
            </q-badge>
          </template>
        </q-input>
      </div>

      <!-- Tags Display -->
      <div v-if="formData.tags.length > 0" class="q-mb-md">
        <div class="text-subtitle2 q-mb-xs">Selected Tags:</div>
        <div class="row q-gutter-xs">
          <q-chip
            v-for="(tag, index) in formData.tags"
            :key="index"
            removable
            @remove="removeTag(index)"
            color="primary"
            text-color="white"
            size="md"
          >
            {{ tag }}
          </q-chip>
        </div>
      </div>

      <!-- Image Upload -->
      <div class="q-mb-md">
        <q-file
          v-model="imageFile"
          label="Add Listing Images"
          outlined
          accept=".jpg, .jpeg, .png, .gif"
          @update:model-value="handleImageUpload"
          max-file-size="5242880"
          @rejected="onRejected"
          :rules="[val => !!val || 'Listing image is required']"
          multiple
        >
          <template v-slot:prepend>
            <q-icon name="attach_file" />
          </template>
        </q-file>

        <!-- Image Preview -->
        <div v-if="formData.images && formData.images.length > 0" class="image-preview-container q-mt-sm">
          <div v-for="(image, index) in formData.images" :key="index" class="image-preview">
            <q-img :src="image" style="height: 100px; width: 100px; object-fit: cover;" />
            <q-btn
              round
              color="negative"
              icon="delete"
              size="sm"
              class="absolute-top-right"
              @click="removeImage(index)"
            />
          </div>
        </div>
      </div>

      <!-- Visibility -->
      <div class="q-mb-md">
        <q-select
          v-model="formData.visibility"
          :options="visibilityOptions"
          label="Visibility"
          outlined
          emit-value
          map-options
        >
          <template v-slot:prepend>
            <q-icon name="visibility" />
          </template>
        </q-select>
      </div>

      <!-- Action Buttons -->
      <div class="row justify-end q-gutter-sm">
        <q-btn
          label="Cancel"
          color="grey"
          flat
          @click="$emit('cancel')"
        />
        <q-btn
          :label="editMode ? 'Update Listing' : 'Post Listing'"
          type="submit"
          color="primary"
          :loading="loading || props.loading"
          :disable="loading || props.loading"
        >
          <template v-slot:loading>
            <q-spinner-dots size="24px" />
          </template>
        </q-btn>
      </div>
    </q-form>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, defineEmits, defineProps, onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { filterOptions } from '../../../services/filterOptionsService';

const emit = defineEmits(['submit', 'cancel']);
const $q = useQuasar();

// State
const loading = ref(false);
const imageFile = ref(null);
const tagsInput = ref('');

// Props
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  editMode: {
    type: Boolean,
    default: false
  },
  initialData: {
    type: Object,
    default: () => ({})
  }
});
const formData = ref({
  title: '',
  content: '',
  images: [],
  listingType: '',
  category: '',
  price: null,
  priceType: 'fixed',
  condition: '',
  duration: '',
  contactInfo: '',
  location: '',
  tags: [],
  visibility: 'public',
  postType: 'marketplace'
});

// Initialize form data with initial data if in edit mode
function initializeFormData() {
  if (props.editMode && props.initialData) {
    // Parse marketplace details from JSON content if available
    let marketplaceDetails = {};
    let description = '';

    if (props.initialData.content) {
      try {
        const contentData = JSON.parse(props.initialData.content);
        description = contentData.description || props.initialData.content;
        marketplaceDetails = contentData.marketplaceDetails || {};
      } catch (e) {
        // If content is not JSON, use it as description
        description = props.initialData.content;
      }
    }

    formData.value = {
      title: props.initialData.title || '',
      content: description,
      images: props.initialData.images || (props.initialData.featuredImage ? [props.initialData.featuredImage] : []),
      listingType: props.initialData.listingType || marketplaceDetails.listingType || '',
      category: props.initialData.category || '',
      price: props.initialData.price || marketplaceDetails.price || null,
      priceType: marketplaceDetails.priceType || 'fixed',
      condition: props.initialData.condition || marketplaceDetails.condition || '',
      duration: marketplaceDetails.duration || '',
      contactInfo: marketplaceDetails.contactInfo || '',
      location: props.initialData.location || marketplaceDetails.location || '',
      tags: props.initialData.tags || [],
      visibility: props.initialData.visibility || 'public',
      postType: 'marketplace'
    };

    // Set tags input if tags exist
    if (props.initialData.tags && props.initialData.tags.length > 0) {
      tagsInput.value = props.initialData.tags.join(', ');
    }
  }
}

// Initialize on component mount
onMounted(() => {
  initializeFormData();
});

// Options - Use filter service to ensure consistency
const listingTypeOptions = filterOptions.listingTypeOptions;

const categoryOptions = [
  { label: 'Technology', value: 'technology', icon: 'devices' },
  { label: 'Business Services', value: 'business_services', icon: 'business' },
  { label: 'Professional Services', value: 'professional_services', icon: 'work' },
  { label: 'Equipment', value: 'equipment', icon: 'construction' },
  { label: 'Office Space', value: 'office_space', icon: 'meeting_room' },
  { label: 'Lab Space', value: 'lab_space', icon: 'science' },
  { label: 'Consulting', value: 'consulting', icon: 'support_agent' },
  { label: 'Education & Training', value: 'education', icon: 'school' },
  { label: 'Marketing', value: 'marketing', icon: 'campaign' },
  { label: 'Design', value: 'design', icon: 'design_services' },
  { label: 'Software', value: 'software', icon: 'code' },
  { label: 'Hardware', value: 'hardware', icon: 'memory' },
  { label: 'Jobs', value: 'jobs', icon: 'work' },
  { label: 'Other', value: 'other', icon: 'more_horiz' }
];

const priceTypeOptions = [
  { label: 'Fixed Price', value: 'fixed', icon: 'attach_money' },
  { label: 'Hourly Rate', value: 'hourly', icon: 'schedule' },
  { label: 'Daily Rate', value: 'daily', icon: 'today' },
  { label: 'Monthly Rate', value: 'monthly', icon: 'calendar_month' },
  { label: 'Negotiable', value: 'negotiable', icon: 'handshake' }
];

const conditionOptions = [
  { label: 'New', value: 'new', icon: 'new_releases' },
  { label: 'Like New', value: 'like_new', icon: 'thumb_up' },
  { label: 'Good', value: 'good', icon: 'check_circle' },
  { label: 'Fair', value: 'fair', icon: 'radio_button_checked' },
  { label: 'Used', value: 'used', icon: 'history' }
];

const durationOptions = [
  { label: 'One-time', value: 'one_time', icon: 'event' },
  { label: 'Hourly', value: 'hourly', icon: 'schedule' },
  { label: 'Daily', value: 'daily', icon: 'today' },
  { label: 'Weekly', value: 'weekly', icon: 'view_week' },
  { label: 'Monthly', value: 'monthly', icon: 'calendar_month' },
  { label: 'Ongoing', value: 'ongoing', icon: 'update' }
];

const visibilityOptions = [
  { label: 'Public', value: 'public', icon: 'public' },
  { label: 'Connections Only', value: 'connections', icon: 'people' },
  { label: 'Private', value: 'private', icon: 'lock' }
];

// Watch for changes in listing type
watch(() => formData.value.listingType, (newVal) => {
  if (newVal === 'free') {
    formData.value.price = 0;
    formData.value.priceType = 'fixed';
  }
});

// Methods
function handleImageUpload(files) {
  if (!files || files.length === 0) return;

  // Clear previous images if needed
  if (!Array.isArray(formData.value.images)) {
    formData.value.images = [];
  }

  // Create data URLs for preview
  Array.from(files).forEach(file => {
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target && e.target.result) {
        formData.value.images.push(e.target.result);
      }
    };
    reader.readAsDataURL(file);
  });
}

function removeImage(index) {
  formData.value.images.splice(index, 1);

  // If all images are removed, reset the file input
  if (formData.value.images.length === 0) {
    imageFile.value = null;
  }
}

function onRejected(rejectedEntries) {
  // Display notification for rejected files
  rejectedEntries.forEach(entry => {
    let message = '';
    if (entry.failedPropValidation === 'accept') {
      message = 'Please upload only image files (JPG, PNG, GIF)';
    } else if (entry.failedPropValidation === 'max-file-size') {
      message = 'File is too large. Maximum size is 5MB';
    } else {
      message = 'File upload failed';
    }

    $q.notify({
      type: 'negative',
      message
    });
  });
}

function processTags() {
  if (!tagsInput.value) return;

  // Split by comma and trim whitespace
  const tags = tagsInput.value.split(',').map(tag => tag.trim()).filter(tag => tag);

  // Remove duplicates
  formData.value.tags = [...new Set(tags)];
}

function removeTag(index) {
  formData.value.tags.splice(index, 1);
}

async function handleSubmit() {
  // Set local loading state
  loading.value = true;

  try {
    // Process tags one more time before submission
    processTags();

    // Validate required fields
    if (!formData.value.title) {
      throw new Error('Title is required');
    }
    if (!formData.value.content) {
      throw new Error('Description is required');
    }
    if (!formData.value.listingType) {
      throw new Error('Listing type is required');
    }
    if (!formData.value.category) {
      throw new Error('Category is required');
    }
    if (!formData.value.location) {
      throw new Error('Location is required');
    }
    if (!formData.value.contactInfo) {
      throw new Error('Contact information is required');
    }
    if (formData.value.listingType === 'product' && !formData.value.condition) {
      throw new Error('Condition is required for product listings');
    }
    if (formData.value.listingType !== 'free' && (formData.value.price === null || formData.value.price === undefined)) {
      throw new Error('Price is required unless listing is free');
    }
    if (!formData.value.images || formData.value.images.length === 0) {
      throw new Error('At least one image is required');
    }

    // Create a details object to store marketplace-specific fields
    // This will be stored in the content field as JSON
    const marketplaceDetails = {
      listingType: formData.value.listingType,
      price: formData.value.price,
      priceType: formData.value.priceType,
      condition: formData.value.condition,
      duration: formData.value.duration,
      contactInfo: formData.value.contactInfo,
      location: formData.value.location
    };

    // Create post object with postType
    const post = {
      title: formData.value.title,
      // Store the description and marketplace details in the content field
      content: JSON.stringify({
        description: formData.value.content,
        marketplaceDetails: marketplaceDetails
      }),
      postType: 'platform', // Must be 'platform', 'admin', or 'automated' per DB constraint
      subType: 'marketplace', // Use 'marketplace' as the subType
      category: formData.value.category,
      tags: formData.value.tags,
      visibility: formData.value.visibility,
      featuredImage: formData.value.images[0], // Use the first image as the featured image
      // Store additional images in media_urls if needed
      mediaUrls: formData.value.images.length > 1 ? formData.value.images.slice(1) : [],
      // Add marketplace-specific fields for easier filtering
      price: formData.value.price,
      location: formData.value.location,
      listingType: formData.value.listingType,
      condition: formData.value.condition
    };

    // Emit the post data to parent component
    emit('submit', post);

    // Reset form after successful submission
    // This will only happen if the parent component doesn't close the dialog
    setTimeout(() => {
      formData.value = {
        title: '',
        content: '',
        images: [],
        listingType: '',
        category: '',
        price: null,
        priceType: 'fixed',
        condition: '',
        duration: '',
        contactInfo: '',
        location: '',
        tags: [],
        visibility: 'public',
        postType: 'marketplace'
      };
      imageFile.value = null;
      tagsInput.value = '';
    }, 500);
  } catch (error) {
    console.error('Error creating marketplace listing:', error);
    $q.notify({
      type: 'negative',
      message: error.message || 'Failed to create marketplace listing. Please try again.'
    });
  } finally {
    // Reset local loading state
    loading.value = false;
  }
}
</script>

<style scoped>
.image-preview-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.image-preview {
  position: relative;
  display: inline-block;
}
</style>
