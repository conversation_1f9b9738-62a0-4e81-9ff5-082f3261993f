<template>
  <div class="innovation-community-left-sidebar">
    <!-- Community Header -->
    <div class="sidebar-header">
      <div class="community-title">
        <q-icon name="groups" class="community-icon" />
        <span>Innovation Community</span>
      </div>
      <q-btn
        flat
        dense
        round
        icon="refresh"
        class="refresh-btn"
        @click="refreshContent"
      />
    </div>

    <!-- Navigation Menu -->
    <div class="navigation-menu">
      <q-list>
        <!-- Feed Tab -->
        <q-item
          clickable
          :active="activeTab === 'feed'"
          @click="navigateToTab('feed')"
          class="nav-item"
        >
          <q-item-section avatar>
            <q-icon name="dynamic_feed" />
          </q-item-section>
          <q-item-section>
            <q-item-label>Community Feed</q-item-label>
            <q-item-label caption>Latest updates</q-item-label>
          </q-item-section>
        </q-item>

        <!-- Profiles Tab -->
        <q-item
          clickable
          :active="activeTab === 'profiles'"
          @click="navigateToTab('profiles')"
          class="nav-item"
        >
          <q-item-section avatar>
            <q-icon name="people" />
          </q-item-section>
          <q-item-section>
            <q-item-label>Profiles Directory</q-item-label>
            <q-item-label caption>Community members</q-item-label>
          </q-item-section>
        </q-item>

        <!-- Events Tab -->
        <q-item
          clickable
          :active="activeTab === 'events'"
          @click="navigateToTab('events')"
          class="nav-item"
        >
          <q-item-section avatar>
            <q-icon name="event" />
          </q-item-section>
          <q-item-section>
            <q-item-label>Events</q-item-label>
            <q-item-label caption>Upcoming activities</q-item-label>
          </q-item-section>
        </q-item>

        <!-- Blog Tab -->
        <q-item
          clickable
          :active="activeTab === 'blog'"
          @click="navigateToTab('blog')"
          class="nav-item"
        >
          <q-item-section avatar>
            <q-icon name="article" />
          </q-item-section>
          <q-item-section>
            <q-item-label>Blog</q-item-label>
            <q-item-label caption>Stories & insights</q-item-label>
          </q-item-section>
        </q-item>

        <!-- Marketplace Tab -->
        <q-item
          clickable
          :active="activeTab === 'marketplace'"
          @click="navigateToTab('marketplace')"
          class="nav-item"
        >
          <q-item-section avatar>
            <q-icon name="store" />
          </q-item-section>
          <q-item-section>
            <q-item-label>Marketplace</q-item-label>
            <q-item-label caption>Products & services</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </div>

    <!-- Community Stats -->
    <div class="community-stats">
      <div class="stats-header">
        <h6 class="stats-title">Community Stats</h6>
      </div>
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-number">{{ memberCount }}</div>
          <div class="stat-label">Members</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ postCount }}</div>
          <div class="stat-label">Posts</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ eventCount }}</div>
          <div class="stat-label">Events</div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <div class="actions-header">
        <h6 class="actions-title">Quick Actions</h6>
      </div>
      <div class="actions-list">
        <q-btn
          flat
          dense
          align="left"
          icon="psychology"
          label="Ask AI"
          class="action-btn"
          @click="triggerAI('general_help')"
        />
        <q-btn
          flat
          dense
          align="left"
          icon="search"
          label="Find Mentors"
          class="action-btn"
          @click="navigateToTab('profiles')"
        />
        <q-btn
          flat
          dense
          align="left"
          icon="add"
          label="Create Post"
          class="action-btn"
          @click="$emit('create-post')"
        />
      </div>
    </div>

    <!-- Footer Links -->
    <div class="sidebar-footer">
      <q-list dense>
        <q-item clickable @click="navigateToLegal('community-guidelines')">
          <q-item-section>
            <q-item-label caption>Community Guidelines</q-item-label>
          </q-item-section>
        </q-item>
        <q-item clickable @click="navigateToLegal('privacy-policy')">
          <q-item-section>
            <q-item-label caption>Privacy Policy</q-item-label>
          </q-item-section>
        </q-item>
        <q-item clickable @click="contactSupport">
          <q-item-section>
            <q-item-label caption>Contact Support</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { feedDataService } from '../../services/feedDataService'

// Props
const props = defineProps<{
  activeTab: string
}>()

// Emits
const emit = defineEmits<{
  navigate: [tab: string]
  refresh: []
  'ai-trigger': [query: string]
  'create-post': []
}>()

// Composables
const router = useRouter()
// feedDataService is imported as singleton

// State
const memberCount = ref(0)
const postCount = ref(0)
const eventCount = ref(0)

// Methods
function navigateToTab(tab: string) {
  emit('navigate', tab)
  router.push({
    path: '/innovation-community',
    query: { tab }
  })
}

function triggerAI(query: string) {
  emit('ai-trigger', query)
}

function refreshContent() {
  emit('refresh')
  loadStats()
}

function navigateToLegal(page: string) {
  router.push({ name: page })
}

function contactSupport() {
  window.open('mailto:<EMAIL>?subject=Support Request&body=Please describe your question or issue:')
}

async function loadStats() {
  try {
    // Load community statistics
    const stats = await feedDataService.getCommunityStats()
    memberCount.value = stats.members || 0
    postCount.value = stats.posts || 0
    eventCount.value = stats.events || 0
  } catch (error) {
    console.error('Failed to load community stats:', error)
  }
}

// Lifecycle
onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.innovation-community-left-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.community-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #111827;
  font-size: 16px;
}

.community-icon {
  color: #0d8a3e;
  font-size: 20px;
}

.refresh-btn {
  color: #6b7280;
}

.refresh-btn:hover {
  color: #0d8a3e;
}

.navigation-menu {
  flex: 1;
  padding: 8px 0;
}

.nav-item {
  margin: 4px 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.nav-item:hover {
  background-color: #f3f4f6;
}

.nav-item.q-item--active {
  background-color: #dcfce7;
  color: #0d8a3e;
}

.nav-item.q-item--active .q-icon {
  color: #0d8a3e;
}

.community-stats {
  padding: 16px;
  border-top: 1px solid #e5e7eb;
  border-bottom: 1px solid #e5e7eb;
}

.stats-header {
  margin-bottom: 12px;
}

.stats-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.stat-item {
  text-align: center;
  padding: 8px 4px;
  background: #f9fafb;
  border-radius: 6px;
}

.stat-number {
  font-size: 18px;
  font-weight: bold;
  color: #0d8a3e;
  line-height: 1;
}

.stat-label {
  font-size: 11px;
  color: #6b7280;
  margin-top: 2px;
}

.quick-actions {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.actions-header {
  margin-bottom: 12px;
}

.actions-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.actions-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.action-btn {
  justify-content: flex-start;
  padding: 8px 12px;
  border-radius: 6px;
  color: #374151;
  font-size: 13px;
}

.action-btn:hover {
  background-color: #f3f4f6;
  color: #0d8a3e;
}

.sidebar-footer {
  padding: 16px;
  margin-top: auto;
}

.sidebar-footer .q-item {
  padding: 4px 0;
  min-height: auto;
}

.sidebar-footer .q-item:hover {
  background-color: #f9fafb;
}
</style>
