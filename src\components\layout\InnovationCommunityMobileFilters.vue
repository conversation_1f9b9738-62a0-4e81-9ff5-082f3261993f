<template>
  <div class="mobile-filters-container">
    <!-- Filter Actions -->
    <div class="filter-actions">
      <!-- AI Filter Trigger (Far Left) -->
      <q-btn
        flat
        dense
        round
        icon="psychology"
        color="primary"
        @click="triggerAIChat"
        class="ai-filter-btn"
      >
        <q-tooltip>AI Chat</q-tooltip>
      </q-btn>

      <!-- Tab Dropdown (Center) - Desktop Style -->
      <q-select
        v-model="selectedTab"
        :options="tabOptions"
        filled
        dense
        emit-value
        map-options
        @update:model-value="handleTabChange"
        class="mobile-tab-select"
        dropdown-icon="arrow_drop_down"
      >
        <template v-slot:prepend>
          <q-icon :name="getTabIcon(selectedTab)" class="nav-icon" />
        </template>
      </q-select>

      <!-- Filter Menu (Far Right) -->
      <q-btn
        flat
        dense
        round
        icon="tune"
        color="primary"
        @click="showFilterDialog = true"
        class="filter-menu-btn"
      >
        <q-tooltip>Filters</q-tooltip>
        <q-badge
          v-if="hasActiveFilters"
          color="red"
          floating
          rounded
        />
      </q-btn>
    </div>

    <!-- Search Bar -->
    <div class="search-container">
      <q-input
        v-model="searchQuery"
        filled
        dense
        placeholder="Search community..."
        @update:model-value="handleSearch"
        class="search-input"
      >
        <template v-slot:prepend>
          <q-icon name="search" />
        </template>
        <template v-slot:append>
          <q-btn
            v-if="searchQuery"
            flat
            dense
            round
            icon="clear"
            @click="clearSearch"
          />
        </template>
      </q-input>
    </div>

    <!-- Filter Dialog -->
    <q-dialog v-model="showFilterDialog" position="bottom">
      <q-card class="filter-dialog-card">
        <q-card-section class="q-pb-none">
          <div class="text-h6">Filters</div>
        </q-card-section>

        <q-card-section>
          <!-- Category Filter -->
          <div class="filter-section">
            <div class="filter-label">Category</div>
            <q-select
              v-model="selectedCategory"
              :options="categoryOptions"
              filled
              dense
              emit-value
              map-options
              clearable
              @update:model-value="applyFilters"
            />
          </div>

          <!-- Date Range Filter -->
          <div class="filter-section">
            <div class="filter-label">Date Range</div>
            <q-select
              v-model="selectedDateRange"
              :options="dateRangeOptions"
              filled
              dense
              emit-value
              map-options
              clearable
              @update:model-value="applyFilters"
            />
          </div>

          <!-- Sort Order -->
          <div class="filter-section">
            <div class="filter-label">Sort By</div>
            <q-select
              v-model="selectedSort"
              :options="sortOptions"
              filled
              dense
              emit-value
              map-options
              @update:model-value="applyFilters"
            />
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn
            flat
            label="Clear All"
            @click="clearAllFilters"
          />
          <q-btn
            flat
            label="Close"
            color="primary"
            @click="showFilterDialog = false"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Results Dialog -->
    <q-dialog v-model="showResults" maximized>
      <q-card class="results-dialog">
        <q-card-section class="q-pa-none">
          <q-toolbar>
            <q-toolbar-title>Search Results</q-toolbar-title>
            <q-btn
              flat
              dense
              round
              icon="close"
              @click="showResults = false"
            />
          </q-toolbar>
        </q-card-section>

        <q-card-section class="q-pa-md">
          <q-scroll-area style="height: 400px;">
            <div class="results-container">
              <!-- Feed Results -->
              <div v-if="activeTab === 'feed' && posts.length > 0" class="results-list">
                <PostCard
                  v-for="post in posts"
                  :key="post.id"
                  :post="post"
                  class="q-mb-md"
                  @like="$emit('like', $event)"
                  @comment="$emit('comment', $event)"
                  @share="$emit('share', $event)"
                />
              </div>

              <!-- Events Results -->
              <div v-else-if="activeTab === 'events' && events.length > 0" class="results-list">
                <EventCard
                  v-for="event in events"
                  :key="event.id"
                  :event="event"
                  class="q-mb-md"
                  @share="$emit('share', $event)"
                />
              </div>

              <!-- Profiles Results -->
              <div v-else-if="activeTab === 'profiles' && profiles.length > 0" class="results-list">
                <ProfileCard
                  v-for="profile in profiles"
                  :key="profile.id"
                  :profile="profile"
                  class="q-mb-md"
                />
              </div>

              <!-- No Results -->
              <div v-else class="no-results">
                <q-icon name="search_off" size="4rem" color="grey-5" />
                <div class="text-h6 q-mt-md">No results found</div>
                <div class="text-body2 text-grey-6">Try adjusting your search or filters</div>
              </div>
            </div>
          </q-scroll-area>
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useGlobalServicesStore } from '../../stores/globalServices'
import PostCard from '../feed/cards/PostCard.vue'
import EventCard from '../feed/cards/EventCard.vue'
import ProfileCard from '../feed/cards/ProfileCard.vue'
import BlogCard from '../feed/cards/BlogCard.vue'
import MarketplaceCard from '../feed/cards/MarketplaceCard.vue'
import { filterOptions } from '../../services/filterOptionsService'

const route = useRoute()
const globalServices = useGlobalServicesStore()

// Props
const props = defineProps<{
  activeTab: string
  posts: any[]
  events: any[]
  profiles: any[]
  articles: any[]
  marketplace: any[]
  loading: boolean
  hasMore: boolean
  showResults: boolean
}>()

// Emits
const emit = defineEmits<{
  'filter-change': [filters: any]
  'filter-results': []
  'tab-change': [tab: string]
  like: [data: any]
  comment: [data: any]
  share: [data: any]
  'load-more': []
}>()

// State
const showFilterDialog = ref(false)
const showResults = ref(false)
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedDateRange = ref('')
const selectedSort = ref('newest')

// Computed
const selectedTab = computed({
  get: () => props.activeTab,
  set: (value) => emit('tab-change', value)
})

const tabOptions = computed(() => [
  { label: 'Feed', value: 'feed', icon: 'home' },
  { label: 'Profiles', value: 'profiles', icon: 'people' },
  { label: 'Events', value: 'events', icon: 'event' },
  { label: 'Blog', value: 'blog', icon: 'article' },
  { label: 'Marketplace', value: 'marketplace', icon: 'storefront' }
])

const categoryOptions = computed(() => {
  return filterOptions.getFilterOptions(props.activeTab).categories || []
})

const dateRangeOptions = computed(() => [
  { label: 'Today', value: 'today' },
  { label: 'This Week', value: 'week' },
  { label: 'This Month', value: 'month' },
  { label: 'This Year', value: 'year' }
])

const sortOptions = computed(() => [
  { label: 'Newest First', value: 'newest' },
  { label: 'Oldest First', value: 'oldest' },
  { label: 'Most Popular', value: 'popular' },
  { label: 'Most Relevant', value: 'relevant' }
])

const hasActiveFilters = computed(() => {
  return !!(searchQuery.value || selectedCategory.value || selectedDateRange.value || selectedSort.value !== 'newest')
})

// Methods
function getTabIcon(tab: string) {
  const option = tabOptions.value.find(opt => opt.value === tab)
  return option?.icon || 'home'
}

function handleTabChange(tab: string) {
  emit('tab-change', tab)
}

function handleSearch() {
  applyFilters()
}

function clearSearch() {
  searchQuery.value = ''
  applyFilters()
}

function applyFilters() {
  const filters = {
    searchQuery: searchQuery.value,
    category: selectedCategory.value,
    dateRange: selectedDateRange.value,
    sort: selectedSort.value
  }
  emit('filter-change', filters)
}

function clearAllFilters() {
  searchQuery.value = ''
  selectedCategory.value = ''
  selectedDateRange.value = ''
  selectedSort.value = 'newest'
  applyFilters()
  showFilterDialog.value = false
}

function triggerAIChat() {
  globalServices.triggerAIChat(`help with ${props.activeTab}`)
}

// Watch for prop changes
watch(() => props.showResults, (newVal) => {
  showResults.value = newVal
})
</script>

<style scoped>
.mobile-filters-container {
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

.filter-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
}

.ai-filter-btn,
.filter-menu-btn {
  flex-shrink: 0;
}

.mobile-tab-select {
  flex: 1;
  min-width: 0;
}

.search-container {
  padding: 0 16px 12px;
}

.search-input {
  width: 100%;
}

.filter-dialog-card {
  width: 100%;
  max-width: 400px;
}

.filter-section {
  margin-bottom: 16px;
}

.filter-label {
  font-weight: 600;
  margin-bottom: 8px;
  color: #374151;
}

.results-dialog {
  height: 100%;
}

.results-container {
  max-width: 600px;
  margin: 0 auto;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.no-results {
  text-align: center;
  padding: 40px 20px;
}

.nav-icon {
  color: #6b7280;
}
</style>
