<template>
  <div v-if="showBanner" class="cookie-consent-banner">
    <div class="banner-content">
      <div class="banner-text">
        <div class="banner-icon">
          <q-icon name="cookie" size="24px" color="amber" />
        </div>
        <div class="banner-message">
          <h4>We use cookies</h4>
          <p>
            We use cookies to enhance your experience, analyze site usage, and provide personalized content. 
            By continuing to use our site, you consent to our use of cookies.
            <router-link to="/legal/gdpr-compliance" class="learn-more-link">Learn more</router-link>
          </p>
        </div>
      </div>
      
      <div class="banner-actions">
        <q-btn
          flat
          dense
          color="grey-7"
          label="Manage Preferences"
          @click="openPreferences"
          class="manage-btn"
        />
        <q-btn
          flat
          dense
          color="grey-7"
          label="Decline"
          @click="declineCookies"
          class="decline-btn"
        />
        <q-btn
          unelevated
          color="primary"
          label="Accept All"
          @click="acceptAllCookies"
          class="accept-btn"
        />
      </div>
    </div>

    <!-- Cookie Preferences Dialog -->
    <q-dialog v-model="showPreferences" class="cookie-preferences-dialog">
      <q-card class="preferences-card">
        <q-card-section class="preferences-header">
          <div class="text-h6">Cookie Preferences</div>
          <q-btn
            flat
            round
            dense
            icon="close"
            @click="showPreferences = false"
            class="close-btn"
          />
        </q-card-section>

        <q-card-section class="preferences-content">
          <p class="preferences-description">
            Choose which cookies you want to allow. You can change these settings at any time.
          </p>

          <div class="cookie-categories">
            <!-- Essential Cookies -->
            <div class="cookie-category">
              <div class="category-header">
                <div class="category-info">
                  <h5>Essential Cookies</h5>
                  <p>Required for basic site functionality. These cannot be disabled.</p>
                </div>
                <q-toggle
                  v-model="cookiePreferences.essential"
                  disable
                  color="primary"
                />
              </div>
            </div>

            <!-- Performance Cookies -->
            <div class="cookie-category">
              <div class="category-header">
                <div class="category-info">
                  <h5>Performance Cookies</h5>
                  <p>Help us understand how you use our site to improve performance.</p>
                </div>
                <q-toggle
                  v-model="cookiePreferences.performance"
                  color="primary"
                />
              </div>
            </div>

            <!-- Functional Cookies -->
            <div class="cookie-category">
              <div class="category-header">
                <div class="category-info">
                  <h5>Functional Cookies</h5>
                  <p>Remember your preferences and provide enhanced features.</p>
                </div>
                <q-toggle
                  v-model="cookiePreferences.functional"
                  color="primary"
                />
              </div>
            </div>

            <!-- Marketing Cookies -->
            <div class="cookie-category">
              <div class="category-header">
                <div class="category-info">
                  <h5>Marketing Cookies</h5>
                  <p>Used to deliver relevant content and track marketing effectiveness.</p>
                </div>
                <q-toggle
                  v-model="cookiePreferences.marketing"
                  color="primary"
                />
              </div>
            </div>
          </div>
        </q-card-section>

        <q-card-actions class="preferences-actions">
          <q-btn
            flat
            color="grey-7"
            label="Decline All"
            @click="declineAllCookies"
          />
          <q-spacer />
          <q-btn
            flat
            color="grey-7"
            label="Cancel"
            @click="showPreferences = false"
          />
          <q-btn
            unelevated
            color="primary"
            label="Save Preferences"
            @click="savePreferences"
          />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useQuasar } from 'quasar'

// Composables
const $q = useQuasar()

// State
const showBanner = ref(false)
const showPreferences = ref(false)

// Cookie preferences
const cookiePreferences = ref({
  essential: true,  // Always true, cannot be disabled
  performance: false,
  functional: false,
  marketing: false
})

// Constants
const COOKIE_CONSENT_KEY = 'zb-cookie-consent'
const COOKIE_PREFERENCES_KEY = 'zb-cookie-preferences'

// Methods
function checkCookieConsent() {
  const consent = localStorage.getItem(COOKIE_CONSENT_KEY)
  if (!consent) {
    showBanner.value = true
  } else {
    // Load saved preferences
    const savedPreferences = localStorage.getItem(COOKIE_PREFERENCES_KEY)
    if (savedPreferences) {
      try {
        const preferences = JSON.parse(savedPreferences)
        cookiePreferences.value = { ...cookiePreferences.value, ...preferences }
      } catch (error) {
        console.error('Error parsing cookie preferences:', error)
      }
    }
    applyCookieSettings()
  }
}

function acceptAllCookies() {
  cookiePreferences.value = {
    essential: true,
    performance: true,
    functional: true,
    marketing: true
  }
  saveConsentAndPreferences()
  showBanner.value = false
  
  $q.notify({
    message: 'All cookies accepted',
    type: 'positive',
    position: 'top',
    timeout: 2000
  })
}

function declineCookies() {
  cookiePreferences.value = {
    essential: true,
    performance: false,
    functional: false,
    marketing: false
  }
  saveConsentAndPreferences()
  showBanner.value = false
  
  $q.notify({
    message: 'Only essential cookies will be used',
    type: 'info',
    position: 'top',
    timeout: 2000
  })
}

function declineAllCookies() {
  cookiePreferences.value = {
    essential: true,
    performance: false,
    functional: false,
    marketing: false
  }
  saveConsentAndPreferences()
  showPreferences.value = false
  showBanner.value = false
  
  $q.notify({
    message: 'Only essential cookies will be used',
    type: 'info',
    position: 'top',
    timeout: 2000
  })
}

function openPreferences() {
  showPreferences.value = true
}

function savePreferences() {
  saveConsentAndPreferences()
  showPreferences.value = false
  showBanner.value = false
  
  $q.notify({
    message: 'Cookie preferences saved',
    type: 'positive',
    position: 'top',
    timeout: 2000
  })
}

function saveConsentAndPreferences() {
  // Save consent timestamp
  localStorage.setItem(COOKIE_CONSENT_KEY, new Date().toISOString())
  
  // Save preferences
  localStorage.setItem(COOKIE_PREFERENCES_KEY, JSON.stringify(cookiePreferences.value))
  
  // Apply cookie settings
  applyCookieSettings()
  
  // Emit event for other components to listen to
  window.dispatchEvent(new CustomEvent('cookieConsentUpdated', {
    detail: cookiePreferences.value
  }))
}

function applyCookieSettings() {
  // Apply Google Analytics based on performance cookies
  if (cookiePreferences.value.performance) {
    enableGoogleAnalytics()
  } else {
    disableGoogleAnalytics()
  }
  
  // Apply other cookie-dependent features
  if (cookiePreferences.value.functional) {
    enableFunctionalCookies()
  }
  
  if (cookiePreferences.value.marketing) {
    enableMarketingCookies()
  }
}

function enableGoogleAnalytics() {
  // Enable Google Analytics if it exists
  if (window.gtag) {
    window.gtag('consent', 'update', {
      'analytics_storage': 'granted'
    })
  }
}

function disableGoogleAnalytics() {
  // Disable Google Analytics if it exists
  if (window.gtag) {
    window.gtag('consent', 'update', {
      'analytics_storage': 'denied'
    })
  }
}

function enableFunctionalCookies() {
  // Enable functional features that depend on cookies
  console.log('Functional cookies enabled')
}

function enableMarketingCookies() {
  // Enable marketing features that depend on cookies
  console.log('Marketing cookies enabled')
}

// Lifecycle
onMounted(() => {
  // Check cookie consent after a short delay to ensure page is loaded
  setTimeout(checkCookieConsent, 1000)
})

// Expose methods for external use
defineExpose({
  openPreferences,
  getCookiePreferences: () => cookiePreferences.value
})
</script>

<style scoped>
.cookie-consent-banner {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e5e7eb;
  box-shadow: 0 -4px 6px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  padding: 20px;
}

.banner-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
}

.banner-text {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  flex: 1;
}

.banner-icon {
  flex-shrink: 0;
  margin-top: 4px;
}

.banner-message h4 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 1.1rem;
  font-weight: 600;
}

.banner-message p {
  margin: 0;
  color: #6b7280;
  font-size: 0.95rem;
  line-height: 1.5;
}

.learn-more-link {
  color: #6366f1;
  text-decoration: none;
  font-weight: 500;
}

.learn-more-link:hover {
  text-decoration: underline;
}

.banner-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.manage-btn,
.decline-btn {
  font-size: 0.9rem;
}

.accept-btn {
  font-weight: 600;
  min-width: 120px;
}

.preferences-card {
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
}

.preferences-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.preferences-content {
  max-height: 60vh;
  overflow-y: auto;
}

.preferences-description {
  color: #6b7280;
  margin-bottom: 24px;
  line-height: 1.6;
}

.cookie-categories {
  display: grid;
  gap: 20px;
}

.cookie-category {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.category-info {
  flex: 1;
}

.category-info h5 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 600;
}

.category-info p {
  margin: 0;
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.5;
}

.preferences-actions {
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .cookie-consent-banner {
    padding: 16px;
  }
  
  .banner-content {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .banner-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .preferences-card {
    margin: 16px;
    max-height: 90vh;
  }
  
  .category-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .banner-actions .q-btn {
    flex: 1;
    min-width: 100px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .cookie-consent-banner {
    background: #1f2937;
    border-top-color: #374151;
  }
  
  .banner-message h4 {
    color: #f9fafb;
  }
  
  .banner-message p {
    color: #d1d5db;
  }
  
  .cookie-category {
    border-color: #374151;
    background: #111827;
  }
  
  .category-info h5 {
    color: #f9fafb;
  }
  
  .category-info p {
    color: #d1d5db;
  }
}
</style>
