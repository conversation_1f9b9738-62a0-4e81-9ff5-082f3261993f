<template>
  <q-card class="compact-menu-card innovation-community-menu">
    <q-card-section class="q-pa-sm bg-primary text-white">
      <div class="text-subtitle1 text-weight-medium">Innovation Community</div>
      <div class="text-caption q-mt-xs">Discover resources, connections, and opportunities</div>
    </q-card-section>

    <div class="q-pa-md">
      <div class="row q-col-gutter-md">
        <!-- First Column -->
        <div class="col-12 col-md-6">
          <div class="text-subtitle2 text-weight-bold text-primary q-mb-sm">
            <q-icon name="explore" size="sm" class="q-mr-xs" />
            Explore
          </div>
          <q-list padding dense class="rounded-borders menu-list">
            <q-item clickable v-ripple @click="navigateTo('/innovation-community?tab=feed')">
              <q-item-section avatar>
                <q-icon name="rss_feed" color="primary" size="sm" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Community Feed</q-item-label>
                <q-item-label caption>Latest updates and opportunities</q-item-label>
              </q-item-section>
            </q-item>

            <q-item clickable v-ripple @click="navigateTo('/innovation-community?tab=profiles')">
              <q-item-section avatar>
                <q-icon name="people" color="primary" size="sm" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Profiles Directory</q-item-label>
                <q-item-label caption>Connect with innovators and mentors</q-item-label>
              </q-item-section>
            </q-item>

            <q-item clickable v-ripple @click="navigateTo('/innovation-community?tab=blog')">
              <q-item-section avatar>
                <q-icon name="article" color="primary" size="sm" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Blog</q-item-label>
                <q-item-label caption>Insights and success stories</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>

        <!-- Second Column -->
        <div class="col-12 col-md-6">
          <div class="text-subtitle2 text-weight-bold text-primary q-mb-sm">
            <q-icon name="handshake" size="sm" class="q-mr-xs" />
            Connect
          </div>
          <q-list padding dense class="rounded-borders menu-list">
            <q-item clickable v-ripple @click="navigateTo('/innovation-community?tab=events')">
              <q-item-section avatar>
                <q-icon name="event" color="primary" size="sm" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Events & Programs</q-item-label>
                <q-item-label caption>Workshops, seminars and more</q-item-label>
              </q-item-section>
            </q-item>

            <q-item clickable v-ripple @click="navigateTo('/innovation-community?tab=groups')">
              <q-item-section avatar>
                <q-icon name="groups" color="primary" size="sm" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Groups</q-item-label>
                <q-item-label caption>Join communities of interest</q-item-label>
              </q-item-section>
            </q-item>

            <q-item clickable v-ripple @click="navigateTo('/innovation-community?tab=marketplace')">
              <q-item-section avatar>
                <q-icon name="storefront" color="primary" size="sm" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Marketplace</q-item-label>
                <q-item-label caption>Products, services and resources</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </div>
    </div>

    <q-separator />

    <q-card-actions align="right">
      <q-btn
        flat
        color="primary"
        label="View Innovation Community"
        icon-right="arrow_forward"
        @click="navigateTo('/innovation-community?tab=feed')"
      />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useMegaMenuStore } from '../../stores/megaMenu';
import { useRouter } from 'vue-router';

const megaMenuStore = useMegaMenuStore();
const router = useRouter();

function closeMenu() {
  megaMenuStore.closeMenu();
}

async function navigateTo(path) {
  // Close the menu first
  closeMenu();

  // Use the unified navigation service
  const { navigateWithMenuClose } = await import('../../services/navigationService').then(m => ({ navigateWithMenuClose: m.useNavigation().navigateWithMenuClose }));

  const success = await navigateWithMenuClose(path);

  if (!success) {
    console.error('InnovationCommunityMenu: Navigation failed for path:', path);
  }
}
</script>

<style scoped>
.compact-menu-card {
  width: 100%;
  max-width: 600px;
}

.menu-list .q-item {
  border-radius: 4px;
}

.menu-list .q-item:hover {
  background-color: rgba(13, 138, 62, 0.05);
}
</style>
