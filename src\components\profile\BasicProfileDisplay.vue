<template>
  <div class="basic-profile-display">
    <!-- Loading state -->
    <div v-if="loading" class="q-pa-md flex flex-center">
      <q-spinner color="primary" size="3em" />
      <div class="q-ml-md">Loading profile data...</div>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="q-pa-md flex flex-center column">
      <q-card class="q-pa-lg text-center" style="max-width: 500px">
        <q-card-section>
          <q-icon name="error" color="negative" size="3em" />
          <div class="text-h5 q-mt-md">Error Loading Profile</div>
          <div class="text-subtitle1 q-mt-sm">{{ error }}</div>
        </q-card-section>
        <q-card-actions align="center">
          <q-btn color="primary" label="Refresh" @click="loadProfile" />
          <q-btn color="secondary" label="Back to Dashboard" to="/dashboard" />
        </q-card-actions>
      </q-card>
    </div>

    <!-- Profile data -->
    <div v-else-if="profile" class="q-pa-md">
      <q-card class="q-mb-md">
        <q-card-section class="bg-primary text-white">
          <div class="row items-center">
            <div class="col-12 col-md-auto">
              <q-avatar size="100px" class="q-mr-md bg-white text-primary">
                {{ getInitials(profile.first_name, profile.last_name) }}
              </q-avatar>
            </div>
            <div class="col">
              <div class="text-h4">{{ profile.first_name }} {{ profile.last_name }}</div>
              <div class="text-subtitle1">{{ formatProfileType(profile.profile_type) }}</div>
              <div class="text-caption text-white-8">{{ profile.email }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Personal Details Section -->
      <q-card class="q-mb-md">
        <q-card-section>
          <div class="text-h6">Personal Details</div>
        </q-card-section>
        <q-separator />
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <div class="text-subtitle2">First Name</div>
              <div>{{ profile.first_name || 'Not provided' }}</div>
            </div>
            <div class="col-12 col-md-6">
              <div class="text-subtitle2">Last Name</div>
              <div>{{ profile.last_name || 'Not provided' }}</div>
            </div>
            <div class="col-12 col-md-6">
              <div class="text-subtitle2">Email</div>
              <div>{{ profile.email || 'Not provided' }}</div>
            </div>
            <div class="col-12 col-md-6">
              <div class="text-subtitle2">Phone</div>
              <div>
                <span v-if="profile.phone_country_code && profile.phone_number">
                  {{ profile.phone_country_code }} {{ profile.phone_number }}
                </span>
                <span v-else>Not provided</span>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Debug Info -->
      <q-card class="q-mt-lg">
        <q-card-section>
          <div class="text-h6">Debug Information</div>
        </q-card-section>
        <q-separator />
        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12">
              <div class="text-subtitle2">Profile ID</div>
              <div>{{ profile.user_id }}</div>
            </div>
            <div class="col-12">
              <div class="text-subtitle2">Profile Type</div>
              <div>{{ profile.profile_type }}</div>
            </div>
            <div class="col-12">
              <div class="text-subtitle2">Raw Profile Data</div>
              <pre class="bg-grey-2 q-pa-sm">{{ JSON.stringify(profile, null, 2) }}</pre>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </div>

    <!-- No profile state -->
    <div v-else class="q-pa-md text-center">
      <q-icon name="person_off" color="grey" size="3em" />
      <div class="text-h6 q-mt-md">No profile found</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useProfileStore } from '../../stores/profile'
import { useGlobalServicesStore } from '../../stores/globalServices'

const props = defineProps({
  userId: {
    type: String,
    required: true
  }
})

// State
const loading = ref(true)
const error = ref(null)
const profile = ref(null)

// Services and stores
const globalServices = useGlobalServicesStore()
const profileStore = useProfileStore()
const formatProfileType = globalServices.profileService.formatProfileType

// Helper functions
function getInitials(firstName, lastName) {
  const first = firstName ? firstName.charAt(0) : ''
  const last = lastName ? lastName.charAt(0) : ''
  return (first + last).toUpperCase()
}

// Load profile data from the profile store
async function loadProfile() {
  loading.value = true
  error.value = null
  profile.value = null

  try {
    console.log('BasicProfileDisplay: Loading profile for user ID:', props.userId)

    // Load profile from the store
    const baseProfile = await profileStore.loadProfileById(props.userId)

    if (!baseProfile) {
      console.error('BasicProfileDisplay: No profile found')
      error.value = 'Profile not found'
      return
    }

    console.log('BasicProfileDisplay: Profile loaded successfully:', baseProfile)
    profile.value = baseProfile

    // If the profile has a type, load specialized data
    if (baseProfile.profile_type) {
      try {
        // Load specialized profile data through the profile store
        await profileStore.loadSpecializedProfileData(props.userId, baseProfile.profile_type)

        // If we have specialized profile data, merge it with the base profile
        if (profileStore.currentSpecializedProfile) {
          console.log('BasicProfileDisplay: Specialized profile data loaded:', profileStore.currentSpecializedProfile)
          // Merge base profile data with specialized profile data
          profile.value = {
            ...baseProfile,
            ...profileStore.currentSpecializedProfile
          }
        }
      } catch (specializedErr) {
        console.warn('BasicProfileDisplay: Error loading specialized data:', specializedErr)
        // Continue with just the base profile
      }
    }
  } catch (err) {
    console.error('BasicProfileDisplay: Error:', err)
    error.value = 'Error loading profile: ' + err.message
  } finally {
    loading.value = false
  }
}

// Load profile on mount
onMounted(() => {
  loadProfile()

  // Add a safety timeout to prevent infinite loading
  setTimeout(() => {
    if (loading.value) {
      console.log('BasicProfileDisplay: Safety timeout reached, forcing loading to false')
      loading.value = false
      if (!error.value) {
        error.value = 'Loading timed out. Please try refreshing the page.'
      }
    }
  }, 10000) // 10 second timeout
})
</script>

<style scoped>
.basic-profile-display {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
