<template>
  <q-card class="enhanced-completion-card">
    <q-card-section class="completion-header">
      <div class="row items-center q-col-gutter-md">
        <div class="col-auto">
          <q-circular-progress
            :value="analysis.overallScore"
            size="80px"
            :thickness="0.15"
            :color="completionColor"
            track-color="grey-3"
            class="completion-circle"
          >
            <div class="text-h6 text-weight-bold">{{ analysis.overallScore }}%</div>
          </q-circular-progress>
        </div>
        
        <div class="col">
          <div class="text-h6 text-weight-bold">Profile Completion</div>
          <div class="text-subtitle2 text-grey-7">{{ completionLevelText }}</div>
          <div class="text-caption">
            {{ analysis.completedFields }} of {{ analysis.totalFields }} fields completed
          </div>
        </div>
        
        <div class="col-auto">
          <q-badge 
            :color="completionColor" 
            :label="analysis.completionLevel.toUpperCase()"
            class="completion-badge"
          />
        </div>
      </div>
    </q-card-section>

    <!-- Critical Fields Status -->
    <q-card-section v-if="analysis.criticalFieldsTotal > 0" class="critical-fields">
      <div class="text-subtitle2 q-mb-sm">
        <q-icon name="priority_high" class="q-mr-xs" />
        Critical Fields
      </div>
      <q-linear-progress
        :value="criticalFieldsProgress"
        :color="criticalFieldsProgress === 1 ? 'positive' : 'warning'"
        size="8px"
        class="q-mb-xs"
      />
      <div class="text-caption">
        {{ analysis.criticalFieldsCompleted }} of {{ analysis.criticalFieldsTotal }} critical fields completed
      </div>
    </q-card-section>

    <!-- Category Breakdown -->
    <q-card-section class="category-breakdown">
      <div class="text-subtitle2 q-mb-md">Completion by Category</div>
      <div class="row q-col-gutter-sm">
        <div 
          v-for="(score, category) in analysis.categoryScores" 
          :key="category"
          class="col-6 col-md-4"
        >
          <div class="category-item">
            <div class="category-header">
              <q-icon :name="getCategoryIcon(category)" size="sm" class="q-mr-xs" />
              <span class="text-caption">{{ formatCategoryName(category) }}</span>
            </div>
            <q-linear-progress
              :value="score / 100"
              :color="getCategoryColor(score)"
              size="4px"
              class="q-mt-xs q-mb-xs"
            />
            <div class="text-caption text-center">{{ score }}%</div>
          </div>
        </div>
      </div>
    </q-card-section>

    <!-- User Guidance -->
    <q-card-section v-if="analysis.userGuidance.length > 0" class="user-guidance">
      <div class="text-subtitle2 q-mb-sm">
        <q-icon name="lightbulb" class="q-mr-xs" />
        Recommendations
      </div>
      <div v-for="(guidance, index) in analysis.userGuidance" :key="index" class="guidance-item">
        <q-icon name="arrow_right" size="xs" class="q-mr-xs" />
        <span class="text-body2">{{ guidance }}</span>
      </div>
    </q-card-section>

    <!-- Next Recommended Fields -->
    <q-card-section v-if="analysis.nextRecommendedFields.length > 0" class="next-fields">
      <div class="text-subtitle2 q-mb-sm">
        <q-icon name="trending_up" class="q-mr-xs" />
        Quick Wins
      </div>
      <div class="recommended-fields">
        <div 
          v-for="field in analysis.nextRecommendedFields.slice(0, 3)" 
          :key="field.fieldName"
          class="field-recommendation"
        >
          <div class="field-info">
            <div class="field-name">{{ field.config.description }}</div>
            <div class="field-guidance text-caption text-grey-6">
              {{ field.config.userGuidance }}
            </div>
          </div>
          <div class="field-impact">
            <q-badge 
              :color="getImpactColor(field.impact)"
              :label="`+${field.impact.toFixed(1)}%`"
              class="impact-badge"
            />
          </div>
        </div>
      </div>
    </q-card-section>

    <!-- Action Button -->
    <q-card-actions class="completion-actions">
      <q-btn
        v-if="analysis.overallScore < 100"
        color="primary"
        label="Complete Profile"
        icon="edit"
        @click="$emit('edit-profile')"
        class="full-width"
      />
      <q-btn
        v-else
        color="positive"
        label="Profile Complete!"
        icon="check_circle"
        disable
        class="full-width"
      />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useEnhancedProfileCompletion, FieldCategory, type ProfileCompletionAnalysis } from '../../services/enhancedProfileCompletionService'
import { useProfileStore } from '../../stores/profile'

// Props
const props = defineProps({
  profile: {
    type: Object,
    default: null
  },
  profileData: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['edit-profile'])

// Services
const { calculateEnhancedCompletion } = useEnhancedProfileCompletion()
const profileStore = useProfileStore()

// Computed
const currentProfile = computed(() => props.profile || profileStore.currentProfile)

const analysis = computed((): ProfileCompletionAnalysis => {
  if (!currentProfile.value) {
    return {
      overallScore: 0,
      categoryScores: {} as Record<FieldCategory, number>,
      completedFields: 0,
      totalFields: 0,
      criticalFieldsCompleted: 0,
      criticalFieldsTotal: 0,
      nextRecommendedFields: [],
      completionLevel: 'basic',
      userGuidance: ['Please create a profile to get started']
    }
  }
  
  return calculateEnhancedCompletion(currentProfile.value, props.profileData)
})

const completionColor = computed(() => {
  const score = analysis.value.overallScore
  if (score >= 90) return 'positive'
  if (score >= 70) return 'primary'
  if (score >= 50) return 'warning'
  return 'negative'
})

const completionLevelText = computed(() => {
  const level = analysis.value.completionLevel
  switch (level) {
    case 'complete': return 'Profile Complete!'
    case 'excellent': return 'Excellent Profile'
    case 'good': return 'Good Progress'
    case 'basic': return 'Getting Started'
    default: return 'Profile Incomplete'
  }
})

const criticalFieldsProgress = computed(() => {
  if (analysis.value.criticalFieldsTotal === 0) return 1
  return analysis.value.criticalFieldsCompleted / analysis.value.criticalFieldsTotal
})

// Methods
function getCategoryIcon(category: FieldCategory): string {
  const icons = {
    [FieldCategory.IDENTITY]: 'person',
    [FieldCategory.CONTACT]: 'contact_mail',
    [FieldCategory.PROFESSIONAL]: 'work',
    [FieldCategory.GOALS]: 'flag',
    [FieldCategory.SOCIAL]: 'share',
    [FieldCategory.ASSETS]: 'cloud_upload',
    [FieldCategory.PREFERENCES]: 'settings'
  }
  return icons[category] || 'info'
}

function formatCategoryName(category: FieldCategory): string {
  return category.charAt(0).toUpperCase() + category.slice(1)
}

function getCategoryColor(score: number): string {
  if (score >= 80) return 'positive'
  if (score >= 60) return 'primary'
  if (score >= 40) return 'warning'
  return 'negative'
}

function getImpactColor(impact: number): string {
  if (impact >= 10) return 'positive'
  if (impact >= 5) return 'primary'
  return 'info'
}
</script>

<style scoped>
.enhanced-completion-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.completion-header {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.completion-circle {
  font-weight: bold;
}

.completion-badge {
  font-size: 0.7rem;
  font-weight: bold;
}

.critical-fields {
  border-left: 4px solid #ff9800;
  background-color: #fff8e1;
}

.category-breakdown {
  background-color: #fafafa;
}

.category-item {
  text-align: center;
  padding: 8px;
  border-radius: 8px;
  background: white;
  border: 1px solid #e0e0e0;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 4px;
}

.user-guidance {
  border-left: 4px solid #2196f3;
  background-color: #e3f2fd;
}

.guidance-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.next-fields {
  background-color: #f3e5f5;
  border-left: 4px solid #9c27b0;
}

.field-recommendation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  margin-bottom: 8px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.field-info {
  flex: 1;
}

.field-name {
  font-weight: 500;
  margin-bottom: 2px;
}

.field-guidance {
  font-size: 0.75rem;
}

.impact-badge {
  font-size: 0.7rem;
  font-weight: bold;
}

.completion-actions {
  padding: 16px;
}
</style>
