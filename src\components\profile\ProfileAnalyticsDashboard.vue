<template>
  <div class="profile-analytics-dashboard">
    <!-- Header -->
    <div class="dashboard-header q-mb-lg">
      <div class="text-h5 text-weight-bold q-mb-sm">Profile Analytics</div>
      <div class="text-subtitle2 text-grey-7">
        Track your profile performance and engagement metrics
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <q-spinner-dots size="50px" color="primary" />
      <div class="text-center q-mt-md">Loading analytics...</div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="error-container">
      <q-banner class="bg-negative text-white" rounded>
        <template v-slot:avatar>
          <q-icon name="error" />
        </template>
        {{ error }}
      </q-banner>
    </div>

    <!-- Analytics Content -->
    <div v-else-if="analytics" class="analytics-content">
      <!-- Key Metrics Cards -->
      <div class="row q-col-gutter-md q-mb-lg">
        <div class="col-12 col-md-3">
          <q-card class="metric-card">
            <q-card-section class="text-center">
              <div class="metric-icon">
                <q-icon name="visibility" size="2rem" color="primary" />
              </div>
              <div class="metric-value">{{ analytics.metrics.profileViews }}</div>
              <div class="metric-label">Total Views</div>
              <div class="metric-change">
                <q-icon 
                  :name="analytics.trends.viewsTrend === 'up' ? 'trending_up' : 
                         analytics.trends.viewsTrend === 'down' ? 'trending_down' : 'trending_flat'"
                  :color="analytics.trends.viewsTrend === 'up' ? 'positive' : 
                          analytics.trends.viewsTrend === 'down' ? 'negative' : 'grey'"
                  size="sm"
                />
                <span class="text-caption">{{ analytics.metrics.profileViewsThisWeek }} this week</span>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-md-3">
          <q-card class="metric-card">
            <q-card-section class="text-center">
              <div class="metric-icon">
                <q-icon name="people" size="2rem" color="secondary" />
              </div>
              <div class="metric-value">{{ analytics.metrics.connectionsAccepted }}</div>
              <div class="metric-label">Connections</div>
              <div class="metric-change">
                <q-icon 
                  :name="analytics.trends.connectionsTrend === 'up' ? 'trending_up' : 
                         analytics.trends.connectionsTrend === 'down' ? 'trending_down' : 'trending_flat'"
                  :color="analytics.trends.connectionsTrend === 'up' ? 'positive' : 
                          analytics.trends.connectionsTrend === 'down' ? 'negative' : 'grey'"
                  size="sm"
                />
                <span class="text-caption">{{ analytics.metrics.connectionRate }}% acceptance rate</span>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-md-3">
          <q-card class="metric-card">
            <q-card-section class="text-center">
              <div class="metric-icon">
                <q-icon name="star" size="2rem" color="warning" />
              </div>
              <div class="metric-value">{{ analytics.metrics.engagementScore }}</div>
              <div class="metric-label">Engagement Score</div>
              <div class="metric-change">
                <q-icon 
                  :name="analytics.trends.engagementTrend === 'up' ? 'trending_up' : 
                         analytics.trends.engagementTrend === 'down' ? 'trending_down' : 'trending_flat'"
                  :color="analytics.trends.engagementTrend === 'up' ? 'positive' : 
                          analytics.trends.engagementTrend === 'down' ? 'negative' : 'grey'"
                  size="sm"
                />
                <span class="text-caption">Out of 100</span>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-md-3">
          <q-card class="metric-card">
            <q-card-section class="text-center">
              <div class="metric-icon">
                <q-icon name="check_circle" size="2rem" color="positive" />
              </div>
              <div class="metric-value">{{ analytics.metrics.profileCompletionScore }}%</div>
              <div class="metric-label">Profile Complete</div>
              <div class="metric-change">
                <q-icon name="info" size="sm" color="info" />
                <span class="text-caption">Completion level</span>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- Charts Row -->
      <div class="row q-col-gutter-md q-mb-lg">
        <!-- Profile Views Chart -->
        <div class="col-12 col-lg-8">
          <q-card class="chart-card">
            <q-card-section>
              <div class="text-h6 q-mb-md">Profile Views Over Time</div>
              <div class="chart-container">
                <!-- Simple chart representation -->
                <div v-if="analytics.viewsData.length > 0" class="views-chart">
                  <div 
                    v-for="(day, index) in analytics.viewsData.slice(-14)" 
                    :key="day.date"
                    class="chart-bar"
                    :style="{ height: `${Math.max(10, (day.views / maxViews) * 100)}px` }"
                  >
                    <q-tooltip>
                      {{ formatDate(day.date) }}: {{ day.views }} views ({{ day.uniqueViews }} unique)
                    </q-tooltip>
                  </div>
                </div>
                <div v-else class="no-data">
                  <q-icon name="bar_chart" size="3rem" color="grey-5" />
                  <div class="text-grey-6 q-mt-sm">No view data available yet</div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- Viewer Demographics -->
        <div class="col-12 col-lg-4">
          <q-card class="chart-card">
            <q-card-section>
              <div class="text-h6 q-mb-md">Viewer Types</div>
              <div class="demographics-chart">
                <div v-if="viewerTypeData.length > 0">
                  <div 
                    v-for="type in viewerTypeData" 
                    :key="type.name"
                    class="demographic-item"
                  >
                    <div class="demographic-label">{{ formatProfileType(type.name) }}</div>
                    <div class="demographic-bar">
                      <div 
                        class="demographic-fill"
                        :style="{ width: `${(type.count / totalViewerTypes) * 100}%` }"
                      ></div>
                    </div>
                    <div class="demographic-count">{{ type.count }}</div>
                  </div>
                </div>
                <div v-else class="no-data">
                  <q-icon name="pie_chart" size="3rem" color="grey-5" />
                  <div class="text-grey-6 q-mt-sm">No viewer data available</div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>

      <!-- Insights and Recommendations -->
      <div class="row q-col-gutter-md">
        <!-- Performance Insights -->
        <div class="col-12 col-lg-6">
          <q-card class="insights-card">
            <q-card-section>
              <div class="text-h6 q-mb-md">
                <q-icon name="insights" class="q-mr-sm" />
                Performance Insights
              </div>
              <div class="insights-list">
                <div 
                  v-for="field in analytics.insights.topPerformingFields" 
                  :key="field.field"
                  class="insight-item"
                >
                  <div class="insight-header">
                    <span class="insight-field">{{ field.field }}</span>
                    <q-badge :color="getImpactColor(field.impact)" :label="`+${field.impact}%`" />
                  </div>
                  <div class="insight-description text-caption text-grey-7">
                    {{ field.description }}
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- Improvement Suggestions -->
        <div class="col-12 col-lg-6">
          <q-card class="suggestions-card">
            <q-card-section>
              <div class="text-h6 q-mb-md">
                <q-icon name="lightbulb" class="q-mr-sm" />
                Improvement Suggestions
              </div>
              <div class="suggestions-list">
                <div 
                  v-for="suggestion in analytics.insights.improvementSuggestions" 
                  :key="suggestion.suggestion"
                  class="suggestion-item"
                >
                  <div class="suggestion-header">
                    <q-icon 
                      :name="getSuggestionIcon(suggestion.impact)" 
                      :color="getSuggestionColor(suggestion.impact)"
                      size="sm"
                      class="q-mr-sm"
                    />
                    <span class="suggestion-category">{{ suggestion.category }}</span>
                    <q-badge 
                      :color="getSuggestionColor(suggestion.impact)" 
                      :label="suggestion.impact.toUpperCase()"
                      class="q-ml-auto"
                    />
                  </div>
                  <div class="suggestion-text">{{ suggestion.suggestion }}</div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useProfileAnalytics } from '../../services/profileAnalyticsService'
import { useProfileStore } from '../../stores/profile'
import { formatProfileType as formatType } from '../../services/profileTypes'

// Props
const props = defineProps({
  userId: {
    type: String,
    default: null
  }
})

// Services
const { loading, error, analytics, loadAnalyticsDashboard } = useProfileAnalytics()
const profileStore = useProfileStore()

// Computed
const currentUserId = computed(() => props.userId || profileStore.currentProfile?.user_id)
const currentProfileType = computed(() => profileStore.currentProfile?.profile_type || 'user')

const maxViews = computed(() => {
  if (!analytics.value?.viewsData.length) return 1
  return Math.max(...analytics.value.viewsData.map(d => d.views))
})

const viewerTypeData = computed(() => {
  if (!analytics.value?.viewsData.length) return []
  
  const typeCount: Record<string, number> = {}
  analytics.value.viewsData.forEach(day => {
    Object.entries(day.viewerTypes).forEach(([type, count]) => {
      typeCount[type] = (typeCount[type] || 0) + count
    })
  })
  
  return Object.entries(typeCount)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
})

const totalViewerTypes = computed(() => {
  return viewerTypeData.value.reduce((sum, type) => sum + type.count, 0)
})

// Methods
function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric' 
  })
}

function formatProfileType(type: string): string {
  return formatType(type)
}

function getImpactColor(impact: number): string {
  if (impact >= 70) return 'positive'
  if (impact >= 40) return 'warning'
  return 'info'
}

function getSuggestionIcon(impact: string): string {
  switch (impact) {
    case 'high': return 'priority_high'
    case 'medium': return 'info'
    case 'low': return 'help'
    default: return 'info'
  }
}

function getSuggestionColor(impact: string): string {
  switch (impact) {
    case 'high': return 'negative'
    case 'medium': return 'warning'
    case 'low': return 'info'
    default: return 'grey'
  }
}

// Lifecycle
onMounted(async () => {
  if (currentUserId.value && currentProfileType.value) {
    await loadAnalyticsDashboard(currentUserId.value, currentProfileType.value)
  }
})
</script>

<style scoped>
.profile-analytics-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.dashboard-header {
  text-align: center;
}

.loading-container, .error-container {
  text-align: center;
  padding: 40px;
}

.metric-card {
  height: 100%;
  border-radius: 12px;
  transition: transform 0.2s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
}

.metric-icon {
  margin-bottom: 8px;
}

.metric-value {
  font-size: 2rem;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 8px;
}

.metric-change {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.chart-card {
  height: 100%;
  border-radius: 12px;
}

.chart-container {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.views-chart {
  display: flex;
  align-items: end;
  gap: 4px;
  height: 150px;
  width: 100%;
}

.chart-bar {
  flex: 1;
  background: linear-gradient(to top, #1976d2, #42a5f5);
  border-radius: 2px 2px 0 0;
  min-height: 10px;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.chart-bar:hover {
  opacity: 0.8;
}

.demographics-chart {
  space-y: 12px;
}

.demographic-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.demographic-label {
  min-width: 80px;
  font-size: 0.85rem;
}

.demographic-bar {
  flex: 1;
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.demographic-fill {
  height: 100%;
  background: linear-gradient(to right, #1976d2, #42a5f5);
  transition: width 0.3s ease;
}

.demographic-count {
  min-width: 30px;
  text-align: right;
  font-size: 0.85rem;
  font-weight: 500;
}

.insights-card, .suggestions-card {
  height: 100%;
  border-radius: 12px;
}

.insight-item, .suggestion-item {
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 12px;
}

.insight-header, .suggestion-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.insight-field {
  font-weight: 500;
}

.suggestion-text {
  font-size: 0.9rem;
  color: #555;
  margin-top: 8px;
}

.no-data {
  text-align: center;
  padding: 40px;
}
</style>
