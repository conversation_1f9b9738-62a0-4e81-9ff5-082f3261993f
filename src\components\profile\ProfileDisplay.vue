<template>
  <div class="profile-display">
    <div v-if="loading" class="q-pa-md flex flex-center">
      <q-spinner color="primary" size="3em" />
      <div class="q-ml-md">Loading profile data...</div>
    </div>

    <div v-else-if="error" class="q-pa-md flex flex-center column">
      <q-card class="q-pa-lg text-center" style="max-width: 500px">
        <q-card-section>
          <unified-icon name="error" color="warning" size="4em" />
          <div class="text-h5 q-mt-md">Profile Setup Required</div>
          <div class="text-subtitle1 q-mt-sm">{{ error }}</div>
          <div v-if="error.includes('category')" class="text-body2 q-mt-md">
            You need to create a profile with a category before you can view it.
          </div>
        </q-card-section>
        <q-card-actions align="center" class="q-gutter-md">
          <q-btn
            v-if="error.includes('category') && isCurrentUser"
            color="primary"
            label="Create Profile"
            :to="{ name: 'profile-create' }"
            class="q-mt-md"
            icon-right="person_add"
          />
          <q-btn
            color="light-green-8"
            label="Back to Dashboard"
            to="/dashboard"
            class="q-mt-md"
            icon-right="arrow_back"
          />
        </q-card-actions>
      </q-card>
    </div>

    <!-- New User Welcome Component -->
    <div v-else-if="!baseProfile || !profileData">
      <new-user-welcome @create-profile="$emit('create-profile')" />
    </div>

    <div v-else>
      <!-- Profile Header -->
      <q-card class="profile-header-card q-mb-lg">
        <q-card-section class="bg-primary text-white">
          <div class="row items-center">
            <div class="col-12 col-md-auto">
              <user-avatar
                :name="`${baseProfile.first_name} ${baseProfile.last_name}`"
                :email="baseProfile.email"
                :avatar-url="baseProfile.avatar_url"
                :user-id="baseProfile.user_id || baseProfile.id"
                size="100px"
                :clickable="false"
                color="#0D8A3E"
                class="q-mr-md"
              />
            </div>

            <div class="col">
              <div class="text-h4">{{ baseProfile.first_name }} {{ baseProfile.last_name }}</div>
              <div class="text-subtitle1">{{ formatProfileType(baseProfile.profile_type) }}</div>
              <div class="text-caption text-white-8">{{ baseProfile.email }}</div>

              <div class="q-mt-sm">
                <q-badge v-if="baseProfile.profile_state === 'ACTIVE'" color="positive">Active</q-badge>
                <q-badge v-else-if="baseProfile.profile_state === 'PENDING_APPROVAL'" color="warning">Pending Approval</q-badge>
                <q-badge v-else-if="baseProfile.profile_state === 'IN_PROGRESS'" color="info">In Progress</q-badge>
              </div>
            </div>

            <div class="col-12 col-md-auto q-mt-md q-mt-md-none">
              <q-btn
                outline
                color="white"
                label="Edit Profile"
                :to="{ name: 'profile-edit', params: { id: baseProfile.user_id } }"
              >
                <unified-icon name="edit" class="q-mr-xs" />
              </q-btn>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Profile Completion -->
      <div v-if="baseProfile.profile_completion < 100" class="profile-completion q-mb-lg">
        <div class="text-subtitle1 q-mb-sm">Profile Completion</div>
        <q-linear-progress
          :value="baseProfile.profile_completion / 100"
          color="primary"
          class="q-mb-sm"
          size="10px"
        />
        <div class="text-caption">
          {{ Math.round(baseProfile.profile_completion) }}% complete
        </div>
      </div>

      <!-- Personal Details Section -->
      <q-card class="q-mb-md">
        <q-card-section>
          <div class="text-h6">Personal Details</div>
        </q-card-section>

        <q-separator />

        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <div class="text-subtitle2">First Name</div>
              <div>{{ baseProfile.first_name || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6">
              <div class="text-subtitle2">Last Name</div>
              <div>{{ baseProfile.last_name || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6">
              <div class="text-subtitle2">Email</div>
              <div>{{ baseProfile.email || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6">
              <div class="text-subtitle2">Phone</div>
              <div>
                <span v-if="baseProfile.phone_country_code && baseProfile.phone_number">
                  {{ baseProfile.phone_country_code }} {{ baseProfile.phone_number }}
                </span>
                <span v-else>Not provided</span>
              </div>
            </div>

            <div class="col-12">
              <div class="text-subtitle2">Bio</div>
              <div>{{ profileData.bio || baseProfile.bio || 'Not provided' }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Specialized Profile Data -->
      <q-card class="q-mb-md">
        <q-card-section>
          <div class="text-h6">{{ formatProfileType(baseProfile.profile_type) }} Profile</div>
        </q-card-section>

        <q-separator />

        <q-card-section>
          <div class="row q-col-gutter-md">
            <!-- Profile Name -->
            <div class="col-12 col-md-6">
              <div class="text-subtitle2">Profile Name</div>
              <div>{{ profileData.profile_name || 'Not provided' }}</div>
            </div>

            <!-- Public Status -->
            <div class="col-12 col-md-6">
              <div class="text-subtitle2">Public Status</div>
              <div>
                <q-badge :color="profileData.is_public ? 'positive' : 'negative'">
                  {{ profileData.is_public ? 'Public' : 'Private' }}
                </q-badge>
              </div>
            </div>

            <!-- Website -->
            <div class="col-12 col-md-6" v-if="profileData.website !== undefined">
              <div class="text-subtitle2">Website</div>
              <div>
                <a v-if="profileData.website" :href="profileData.website" target="_blank">
                  {{ profileData.website }}
                </a>
                <span v-else>Not provided</span>
              </div>
            </div>

            <!-- Social Media Links -->
            <div class="col-12">
              <div class="text-subtitle2">Social Media</div>
              <div class="row q-col-gutter-sm q-mt-xs">
                <div v-if="socialLinks.linkedin" class="col-auto">
                  <q-btn
                    :href="formatSocialLink('linkedin', socialLinks.linkedin)"
                    target="_blank"
                    round
                    flat
                    color="primary"
                    size="sm"
                  >
                    <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M19 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14m-.5 15.5v-5.3a3.26 3.26 0 0 0-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 0 1 1.4 1.4v4.93h2.79M6.88 8.56a1.68 1.68 0 0 0 1.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 0 0-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37h2.77z'/%3E%3C/svg%3E" size="20px" />
                    <q-tooltip>LinkedIn</q-tooltip>
                  </q-btn>
                </div>
                <div v-if="socialLinks.twitter" class="col-auto">
                  <q-btn
                    :href="formatSocialLink('twitter', socialLinks.twitter)"
                    target="_blank"
                    round
                    flat
                    color="primary"
                    size="sm"
                  >
                    <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M22.46 6c-.77.35-1.6.58-**********-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .***********.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z'/%3E%3C/svg%3E" size="20px" />
                    <q-tooltip>Twitter</q-tooltip>
                  </q-btn>
                </div>
                <div v-if="socialLinks.facebook" class="col-auto">
                  <q-btn
                    :href="formatSocialLink('facebook', socialLinks.facebook)"
                    target="_blank"
                    round
                    flat
                    color="primary"
                    size="sm"
                  >
                    <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z'/%3E%3C/svg%3E" size="20px" />
                    <q-tooltip>Facebook</q-tooltip>
                  </q-btn>
                </div>
                <div v-if="socialLinks.instagram" class="col-auto">
                  <q-btn
                    :href="formatSocialLink('instagram', socialLinks.instagram)"
                    target="_blank"
                    round
                    flat
                    color="primary"
                    size="sm"
                  >
                    <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M7.8 2h8.4C19.4 2 22 4.6 22 7.8v8.4a5.8 5.8 0 0 1-5.8 5.8H7.8C4.6 22 2 19.4 2 16.2V7.8A5.8 5.8 0 0 1 7.8 2m-.2 2A3.6 3.6 0 0 0 4 7.6v8.8C4 18.39 5.61 20 7.6 20h8.8a3.6 3.6 0 0 0 3.6-3.6V7.6C20 5.61 18.39 4 16.4 4H7.6m9.65 1.5a1.25 1.25 0 0 1 1.25 1.25A1.25 1.25 0 0 1 17.25 8 1.25 1.25 0 0 1 16 6.75a1.25 1.25 0 0 1 1.25-1.25M12 7a5 5 0 0 1 5 5 5 5 0 0 1-5 5 5 5 0 0 1-5-5 5 5 0 0 1 5-5m0 2a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3z'/%3E%3C/svg%3E" size="20px" />
                    <q-tooltip>Instagram</q-tooltip>
                  </q-btn>
                </div>
                <div v-if="!hasSocialLinks" class="col-12">
                  No social media links provided
                </div>
              </div>
            </div>

            <!-- Contact Information -->
            <div class="col-12">
              <div class="text-subtitle2">Contact Information</div>
              <div class="row q-col-gutter-md q-mt-xs">
                <div class="col-12 col-md-6" v-if="profileData.contact_email !== undefined">
                  <div class="text-subtitle2">Contact Email</div>
                  <div>{{ profileData.contact_email || 'Not provided' }}</div>
                </div>

                <div class="col-12 col-md-6">
                  <div class="text-subtitle2">Contact Phone</div>
                  <div>
                    <span v-if="profileData.contact_phone">
                      {{ profileData.contact_phone }}
                    </span>
                    <span v-else-if="profileData.contact_phone_country_code && profileData.contact_phone_number">
                      {{ profileData.contact_phone_country_code }} {{ profileData.contact_phone_number }}
                    </span>
                    <span v-else>Not provided</span>
                  </div>
                </div>

                <div class="col-12 col-md-6">
                  <div class="text-subtitle2">WhatsApp</div>
                  <div>
                    <span v-if="profileData.whatsapp">
                      {{ profileData.whatsapp }}
                    </span>
                    <span v-else-if="profileData.whatsapp_country_code && profileData.whatsapp_number">
                      {{ profileData.whatsapp_country_code }} {{ profileData.whatsapp_number }}
                    </span>
                    <span v-else>Not provided</span>
                  </div>
                </div>

                <div class="col-12 col-md-6" v-if="profileData.telegram">
                  <div class="text-subtitle2">Telegram</div>
                  <div>{{ profileData.telegram }}</div>
                </div>

                <div class="col-12 col-md-6" v-if="profileData.skype">
                  <div class="text-subtitle2">Skype</div>
                  <div>{{ profileData.skype }}</div>
                </div>

                <div class="col-12 col-md-6" v-if="profileData.preferred_contact_method">
                  <div class="text-subtitle2">Preferred Contact Method</div>
                  <div>{{ profileData.preferred_contact_method }}</div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>


    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '../../stores/auth'
import { useProfileStore } from '../../stores/profile'
import { useGlobalServicesStore } from '../../stores/globalServices'
import { supabase } from '../../lib/supabase'
import UnifiedIcon from '../ui/UnifiedIcon.vue'
import NewUserWelcome from './NewUserWelcome.vue'
import UserAvatar from '../common/UserAvatar.vue'

const props = defineProps({
  showCreateProfileButton: {
    type: Boolean,
    default: true
  },
  userId: {
    type: String,
    required: true
  },
  profileData: {
    type: Object,
    default: null
  },
  specializedProfileData: {
    type: Object,
    default: null
  },
  showDebug: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['create-profile'])

const authStore = useAuthStore()
const profileStore = useProfileStore()
const globalServices = useGlobalServicesStore()

const loading = ref(true)
const error = ref(null)
const profileData = ref(null)
const baseProfile = ref(null)
const source = ref(null)

// Computed properties
const isCurrentUser = computed(() => {
  return authStore.currentUser?.id === props.userId
})

const socialLinks = computed(() => {
  if (!profileData.value) return {}

  const links = {}
  const socialFields = ['linkedin', 'twitter', 'facebook', 'instagram', 'youtube']

  socialFields.forEach(field => {
    if (profileData.value[field]) {
      links[field] = profileData.value[field]
    }
  })

  if (profileData.value.other_social) {
    links.other = profileData.value.other_social
  }

  return links
})

const hasSocialLinks = computed(() => {
  return Object.keys(socialLinks.value).length > 0
})

// Use the formatProfileType function from the profile service
const formatProfileType = globalServices.profileService.formatProfileType

function getInitials(firstName, lastName) {
  const first = firstName ? firstName.charAt(0) : ''
  const last = lastName ? lastName.charAt(0) : ''
  return (first + last).toUpperCase()
}

function getSocialIcon(platform) {
  const icons = {
    linkedin: 'linkedin',
    twitter: 'twitter',
    facebook: 'facebook',
    instagram: 'instagram',
    youtube: 'youtube',
    github: 'github',
    medium: 'medium',
    other: 'link'
  }

  return icons[platform] || 'link'
}

function formatSocialName(platform) {
  const names = {
    linkedin: 'LinkedIn',
    twitter: 'Twitter',
    facebook: 'Facebook',
    instagram: 'Instagram',
    youtube: 'YouTube',
    github: 'GitHub',
    medium: 'Medium',
    other: 'Other'
  }

  return names[platform] || platform
}

function formatSocialLink(platform, value) {
  // If the value already includes http:// or https://, return it as is
  if (value.startsWith('http://') || value.startsWith('https://')) {
    return value
  }

  // Otherwise, add the appropriate prefix based on the platform
  const prefixes = {
    linkedin: 'https://linkedin.com/in/',
    twitter: 'https://twitter.com/',
    facebook: 'https://facebook.com/',
    instagram: 'https://instagram.com/',
    youtube: 'https://youtube.com/',
    github: 'https://github.com/',
    medium: 'https://medium.com/@'
  }

  // If we have a prefix for this platform, use it, otherwise just add https://
  const prefix = prefixes[platform] || 'https://'

  // If the value already includes the domain name, just add https://
  if (value.includes('.com') || value.includes('.org') || value.includes('.net')) {
    return `https://${value}`
  }

  return `${prefix}${value}`
}

// Load profile data using the profile store
async function loadProfileDataForUser() {
  loading.value = true
  error.value = null

  try {
    console.log('ProfileDisplay: Loading profile for user ID:', props.userId)

    // Initialize the profile store if needed
    if (profileStore.userProfiles.length === 0) {
      console.log('ProfileDisplay: Initializing profile store')
      await profileStore.initialize()
    }

    // Use the fetchProfile method from the profile store
    const profile = await profileStore.fetchProfile(props.userId)

    if (!profile) {
      console.error('ProfileDisplay: Profile not found in store')
      error.value = 'Profile not found'
      baseProfile.value = null
      profileData.value = null
      return
    }

    console.log('ProfileDisplay: Profile loaded from store:', profile)
    baseProfile.value = profile

    // If we have specialized profile data, merge it with the base profile
    if (profileStore.currentSpecializedProfile.value) {
      console.log('ProfileDisplay: Using specialized profile data:', profileStore.currentSpecializedProfile.value)
      profileData.value = {
        ...profile,
        ...profileStore.currentSpecializedProfile.value
      }
      source.value = `${profile.profile_type}_profiles`
    } else {
      profileData.value = profile
      source.value = 'personal_details'
    }

    console.log('ProfileDisplay: Final profile data:', profileData.value)
  } catch (err) {
    console.error('ProfileDisplay: Error loading profile:', err)
    error.value = 'Error loading profile: ' + err.message
    baseProfile.value = null
    profileData.value = null
  } finally {
    loading.value = false
  }
}

// Lifecycle hooks
onMounted(() => {
  // If profile data is provided via props, use it
  if (props.profileData) {
    console.log('ProfileDisplay: Using profile data from props:', props.profileData)
    baseProfile.value = props.profileData

    // If specialized profile data is provided, use it
    if (props.specializedProfileData) {
      console.log('ProfileDisplay: Using specialized profile data from props:', props.specializedProfileData)
      profileData.value = {
        ...props.profileData,
        ...props.specializedProfileData
      }
      source.value = `${props.profileData.profile_type}_profiles`
    } else {
      // Otherwise just use the base profile data
      profileData.value = props.profileData
      source.value = 'personal_details'
    }

    loading.value = false
  } else {
    // Otherwise load the data from the database
    loadProfileDataForUser()

    // Add a safety timeout to prevent infinite loading
    setTimeout(() => {
      if (loading.value) {
        console.log('ProfileDisplay: Safety timeout reached, forcing loading to false')
        loading.value = false
      }
    }, 5000) // 5 second timeout
  }
})
</script>

<style scoped>
.profile-display {
  max-width: 1200px;
  margin: 0 auto;
}



/* Mobile responsiveness improvements */
@media (max-width: 599px) {
  .profile-display {
    padding: 8px !important;
  }

  .q-card {
    margin-bottom: 16px;
  }

  .q-card-section {
    padding: 16px 12px;
  }

  .profile-header-card .q-card-section {
    padding: 16px;
  }

  .profile-header-card .row {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .profile-header-card .q-avatar {
    margin-bottom: 16px;
    margin-right: 0 !important;
  }

  .profile-header-card .col-md-auto {
    margin-top: 16px !important;
  }
}
</style>
