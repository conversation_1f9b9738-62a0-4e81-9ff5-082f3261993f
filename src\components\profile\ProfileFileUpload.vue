<template>
  <div class="profile-file-upload">
    <div class="text-subtitle2 q-mb-sm">{{ label }}</div>
    <div class="text-caption text-grey-7 q-mb-md">{{ description }}</div>
    
    <!-- File Upload Input -->
    <q-file
      v-model="fileModel"
      :label="uploadLabel"
      outlined
      :accept="acceptedTypes"
      @update:model-value="handleFileUpload"
      :max-file-size="maxFileSize"
      @rejected="onRejected"
      :rules="required ? [val => !!val || `${label} is required`] : []"
    >
      <template v-slot:prepend>
        <q-icon :name="uploadIcon" />
      </template>
    </q-file>

    <!-- File Preview -->
    <div v-if="previewUrl" class="file-preview q-mt-md">
      <!-- Image Preview -->
      <div v-if="fileType === 'image'" class="image-preview">
        <q-img 
          :src="previewUrl" 
          style="max-height: 200px; max-width: 100%; border-radius: 8px;"
          class="preview-image"
        />
        <q-btn
          round
          color="negative"
          icon="delete"
          size="sm"
          class="absolute-top-right q-ma-sm"
          @click="removeFile"
        />
      </div>
      
      <!-- Document Preview -->
      <div v-else-if="fileType === 'document'" class="document-preview">
        <q-card flat bordered class="document-card">
          <q-card-section class="row items-center">
            <q-icon name="description" size="md" color="primary" class="q-mr-md" />
            <div class="col">
              <div class="text-subtitle2">{{ fileName }}</div>
              <div class="text-caption text-grey-6">{{ formatFileSize(fileSize) }}</div>
            </div>
            <q-btn
              round
              flat
              color="negative"
              icon="delete"
              size="sm"
              @click="removeFile"
            />
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Upload Progress -->
    <div v-if="uploading" class="upload-progress q-mt-md">
      <q-linear-progress 
        indeterminate 
        color="primary" 
        class="q-mb-sm"
      />
      <div class="text-caption text-center">Uploading {{ label.toLowerCase() }}...</div>
    </div>

    <!-- Upload Success -->
    <div v-if="uploadSuccess" class="upload-success q-mt-md">
      <q-banner class="bg-positive text-white" rounded>
        <template v-slot:avatar>
          <q-icon name="check_circle" />
        </template>
        {{ label }} uploaded successfully!
      </q-banner>
    </div>

    <!-- Upload Error -->
    <div v-if="uploadError" class="upload-error q-mt-md">
      <q-banner class="bg-negative text-white" rounded>
        <template v-slot:avatar>
          <q-icon name="error" />
        </template>
        Failed to upload {{ label.toLowerCase() }}: {{ uploadError }}
      </q-banner>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useNotificationStore } from '../../stores/notifications'
import { useStorageService } from '../../services/storageService'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  fileType: {
    type: String,
    default: 'image', // 'image' or 'document'
    validator: (value: string) => ['image', 'document'].includes(value)
  },
  required: {
    type: Boolean,
    default: false
  },
  maxFileSize: {
    type: Number,
    default: 5242880 // 5MB default
  },
  storageBucket: {
    type: String,
    default: 'imagefiles'
  },
  storageFolder: {
    type: String,
    default: 'profiles'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'upload-success', 'upload-error'])

// Services
const notifications = useNotificationStore()
const storageService = useStorageService()

// State
const fileModel = ref<File | null>(null)
const previewUrl = ref('')
const fileName = ref('')
const fileSize = ref(0)
const uploading = ref(false)
const uploadSuccess = ref(false)
const uploadError = ref('')

// Computed
const acceptedTypes = computed(() => {
  if (props.fileType === 'image') {
    return '.jpg, .jpeg, .png, .gif'
  } else if (props.fileType === 'document') {
    return '.pdf, .doc, .docx, .txt'
  }
  return '*'
})

const uploadLabel = computed(() => {
  if (props.fileType === 'image') {
    return `Upload ${props.label}`
  } else if (props.fileType === 'document') {
    return `Upload ${props.label}`
  }
  return `Upload ${props.label}`
})

const uploadIcon = computed(() => {
  if (props.fileType === 'image') {
    return 'image'
  } else if (props.fileType === 'document') {
    return 'description'
  }
  return 'attach_file'
})

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (newValue && newValue !== previewUrl.value) {
    previewUrl.value = newValue
    uploadSuccess.value = true
  }
}, { immediate: true })

// Methods
async function handleFileUpload(file: File | null) {
  if (!file) return

  // Reset states
  uploadError.value = ''
  uploadSuccess.value = false
  fileName.value = file.name
  fileSize.value = file.size

  // Create preview for images
  if (props.fileType === 'image') {
    const reader = new FileReader()
    reader.onload = (e: ProgressEvent<FileReader>) => {
      if (e.target && e.target.result) {
        previewUrl.value = e.target.result as string
      }
    }
    reader.readAsDataURL(file)
  } else {
    // For documents, just show the file info
    previewUrl.value = 'document'
  }

  // Upload file to storage
  try {
    uploading.value = true
    const uploadedUrl = await storageService.uploadFile(
      file,
      props.storageBucket,
      props.storageFolder
    )
    
    // Update the model value with the uploaded URL
    emit('update:modelValue', uploadedUrl)
    emit('upload-success', uploadedUrl)
    
    uploadSuccess.value = true
    notifications.success(`${props.label} uploaded successfully!`)
    
  } catch (error) {
    console.error('File upload error:', error)
    uploadError.value = error.message || 'Upload failed'
    emit('upload-error', error)
    notifications.error(`Failed to upload ${props.label.toLowerCase()}`)
  } finally {
    uploading.value = false
  }
}

function removeFile() {
  fileModel.value = null
  previewUrl.value = ''
  fileName.value = ''
  fileSize.value = 0
  uploadSuccess.value = false
  uploadError.value = ''
  emit('update:modelValue', '')
}

function onRejected(rejectedEntries: any[]) {
  const reasons = rejectedEntries.map(entry => {
    if (entry.failedPropValidation === 'max-file-size') {
      return `File too large (max ${formatFileSize(props.maxFileSize)})`
    }
    return 'Invalid file type'
  })
  
  notifications.warning(`File rejected: ${reasons.join(', ')}`)
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.profile-file-upload {
  width: 100%;
}

.file-preview {
  position: relative;
}

.preview-image {
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
}

.document-card {
  border: 2px dashed #e0e0e0;
}

.upload-progress {
  text-align: center;
}
</style>
