<template>
  <div class="profile-type-selection">
    <q-card class="profile-type-selection-card">
      <q-card-section class="bg-primary text-white">
        <div class="text-h6">Select Your Profile Type</div>
        <div class="text-subtitle2">Choose the category that best describes your role in the ecosystem</div>
      </q-card-section>

      <q-card-section>
        <q-form @submit.prevent="submitProfileType" class="q-gutter-md">
          <!-- Profile Type Selection -->
          <div class="text-subtitle1 q-my-md">Select Profile Category:</div>
          <div class="row q-col-gutter-md">
            <div
              v-for="option in profileTypeOptions"
              :key="option.value"
              class="col-12 col-md-4"
            >
              <q-card
                :class="{
                  'profile-type-card': true,
                  'selected': profileType === option.value
                }"
                clickable
                @click="profileType = option.value"
              >
                <q-card-section>
                  <div class="text-h6">{{ option.label }}</div>
                  <div class="text-caption">{{ getProfileTypeDescription(option.value) }}</div>
                </q-card-section>
              </q-card>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="q-mt-lg text-center">
            <q-btn
              type="submit"
              color="primary"
              label="Continue"
              :loading="creating"
              :disable="!profileType"
              size="lg"
            />
          </div>
        </q-form>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useProfileStore } from '../../stores/profile'
import { useNotificationStore } from '../../stores/notifications'
import UnifiedIcon from '../ui/UnifiedIcon.vue'
import { getProfileTypeOptions } from '../../services/profileTypes'

// Props and emits
const emit = defineEmits(['profile-created', 'close'])

// Router and stores
const router = useRouter()
const profileStore = useProfileStore()
const notifications = useNotificationStore()

// State
const profileType = ref('')
const creating = ref(false)
const profileTypeOptions = getProfileTypeOptions()

// Methods
function getProfileTypeDescription(type) {
  const descriptions = {
    'innovator': 'Entrepreneurs, startups, and innovators with ideas or products.',
    'investor': 'Angel investors, VCs, and investment firms looking for opportunities.',
    'mentor': 'Experienced professionals offering guidance and mentorship.',
    'professional': 'Industry professionals looking to network and collaborate.',
    'industry_expert': 'Subject matter experts with specialized knowledge.',
    'academic_student': 'Students looking for opportunities and connections.',
    'academic_institution': 'Universities, colleges, and research institutions.',
    'organisation': 'Companies, NGOs, and other organizations.'
  }

  return descriptions[type] || ''
}

async function submitProfileType() {
  if (!profileType.value) {
    notifications.warning('Please select a profile type')
    return
  }

  try {
    creating.value = true

    console.log('Creating profile:', { type: profileType.value })

    // Create the profile using the profile store
    const profileId = await profileStore.createProfile(
      profileType.value
    )

    if (profileId) {
      console.log('Profile created successfully with ID:', profileId)
      notifications.success('Profile created successfully!')

      // Emit event to parent component
      emit('profile-created', {
        id: profileId,
        type: profileType.value
      })

      // Close the modal
      emit('close')

      // Navigate to the profile edit page
      router.push({
        name: 'profile-edit',
        params: { id: profileId }
      })
    } else {
      throw new Error('Failed to create profile - no profile ID returned')
    }
  } catch (error) {
    console.error('Error creating profile:', error)
    notifications.error('Failed to create profile. Please try again.')
  } finally {
    creating.value = false
  }
}
</script>

<style scoped>
.profile-type-selection {
  width: 100%;
  max-width: 900px;
}

.profile-type-selection-card {
  width: 100%;
}

.profile-type-card {
  height: 100%;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.profile-type-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.profile-type-card.selected {
  border-color: var(--q-primary);
  background-color: rgba(var(--q-primary-rgb), 0.05);
}

@media (max-width: 599px) {
  .profile-type-selection {
    max-width: 100%;
  }
}
</style>
