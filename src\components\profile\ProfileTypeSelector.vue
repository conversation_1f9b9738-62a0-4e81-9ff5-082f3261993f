<template>
  <div class="q-pa-md">
    <p>Select the type of profile you want to create. This will determine the specific information we'll collect.</p>

    <div class="text-subtitle1 q-my-md">Select Profile Category:</div>
    <div class="row q-col-gutter-md">
      <div
        v-for="option in profileTypeOptions"
        :key="option.value"
        class="col-12 col-md-4"
      >
        <q-card
          :class="{
            'profile-type-card': true,
            'selected': selectedProfileType === option.value,
            'disabled': selectedProfileType && selectedProfileType !== option.value && !isCreatingNewProfile
          }"
          :clickable="!selectedProfileType || selectedProfileType === option.value || isCreatingNewProfile"
          @click="selectProfileType(option.value)"
        >
          <q-card-section>
            <div class="text-h6">{{ option.label }}</div>
            <div class="text-caption">{{ getProfileTypeDescription(option.value) }}</div>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <div class="text-caption q-mt-md" :class="isCreatingNewProfile ? 'text-negative' : 'text-warning'" v-if="selectedProfileType">
      <unified-icon name="warning" size="xs" />
      <span v-if="isCreatingNewProfile">Changing profile type will reset any profile-specific information you've already entered.</span>
      <span v-else>Profile type cannot be changed after profile creation.</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useNotificationStore } from '../../stores/notifications'

const notifications = useNotificationStore()

// Define props
const props = defineProps({
  profileType: {
    type: String,
    default: null
  },
  isCreatingNewProfile: {
    type: Boolean,
    default: true
  }
})

// Define emits
const emit = defineEmits(['update:profileType'])

// Local state
const selectedProfileType = ref(props.profileType)

// Profile type options
const profileTypeOptions = [
  { label: 'Innovator', value: 'innovator' },
  { label: 'Investor', value: 'investor' },
  { label: 'Mentor', value: 'mentor' },
  { label: 'Professional', value: 'professional' },
  { label: 'Industry Expert', value: 'industry_expert' },
  { label: 'Academic Student', value: 'academic_student' },
  { label: 'Academic Institution', value: 'academic_institution' },
  { label: 'Organisation', value: 'organisation' }
]

// Watch for changes to the props
watch(() => props.profileType, (newValue) => {
  selectedProfileType.value = newValue
})

// Watch for changes to the local state
watch(selectedProfileType, (newValue) => {
  emit('update:profileType', newValue)
})

// Select profile type
function selectProfileType(type) {
  // If clicking the same type, do nothing
  if (selectedProfileType.value === type) {
    console.log('ProfileTypeSelector: Profile type already set to:', type)
    return
  }

  // If in edit mode (not creating new profile) and profile type is already set, prevent changing
  if (!props.isCreatingNewProfile && selectedProfileType.value) {
    notifications.info('Profile type cannot be changed after creation')
    return
  }

  console.log('ProfileTypeSelector: Setting profile type to:', type)
  selectedProfileType.value = type
}

// Get profile type description
function getProfileTypeDescription(type) {
  const descriptions = {
    innovator: 'For startups, entrepreneurs, and innovation creators',
    investor: 'For venture capitalists, angel investors, and funding organizations',
    mentor: 'For advisors, coaches, and experienced professionals',
    professional: 'For individuals working in various industries',
    industry_expert: 'For specialists with deep domain knowledge',
    academic_student: 'For students in academic institutions',
    academic_institution: 'For universities, colleges, and research institutions',
    organisation: 'For companies, NGOs, and other organizations'
  }

  return descriptions[type] || ''
}
</script>

<style scoped>
.profile-type-card {
  transition: all 0.3s ease;
  height: 100%;
}

.profile-type-card.selected {
  border: 2px solid var(--q-primary);
  transform: translateY(-4px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.profile-type-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  filter: grayscale(0.7);
}
</style>
