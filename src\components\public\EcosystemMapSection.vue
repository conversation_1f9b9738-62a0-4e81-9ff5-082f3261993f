<template>
  <section class="ecosystem-map-section q-py-xl">
    <div class="container q-mx-auto q-px-md">
      <div class="row">
        <div class="col-1" />
        <div class="col-10">
          <div class="text-center">
            <h2 class="text-h3 text-weight-light q-mb-md">Our Innovation Ecosystem</h2>
            <p class="text-body1 q-mb-xl text-center">
              Discover how our platform connects all stakeholders in the innovation ecosystem, creating a powerful network of collaboration and growth.
            </p>
          </div>

          <div class="ecosystem-map">
            <div class="map-container">
              <!-- Central Hub Node -->
              <div class="hub-node node">
                <q-icon name="business" size="2rem" color="white" />
                <div class="node-label">Innovation Hub</div>
              </div>

              <!-- Stakeholder Nodes -->
              <div
                class="stakeholder-node node node-1"
                @click="navigateToProfiles('startups')"
                role="button"
                aria-label="View Startups profiles"
              >
                <q-icon name="rocket_launch" size="1.5rem" color="white" />
                <div class="node-label">Startups</div>
              </div>

              <div
                class="stakeholder-node node node-2"
                @click="navigateToProfiles('investors')"
                role="button"
                aria-label="View Investors profiles"
              >
                <q-icon name="account_balance" size="1.5rem" color="white" />
                <div class="node-label">Investors</div>
              </div>

              <div
                class="stakeholder-node node node-3"
                @click="navigateToProfiles('mentors')"
                role="button"
                aria-label="View Mentors profiles"
              >
                <q-icon name="psychology" size="1.5rem" color="white" />
                <div class="node-label">Mentors</div>
              </div>

              <div
                class="stakeholder-node node node-4"
                @click="navigateToProfiles('academia')"
                role="button"
                aria-label="View Academia profiles"
              >
                <q-icon name="school" size="1.5rem" color="white" />
                <div class="node-label">Academia</div>
              </div>

              <div
                class="stakeholder-node node node-5"
                @click="navigateToProfiles('corporates')"
                role="button"
                aria-label="View Corporate profiles"
              >
                <q-icon name="domain" size="1.5rem" color="white" />
                <div class="node-label">Corporates</div>
              </div>

              <div
                class="stakeholder-node node node-6"
                @click="navigateToProfiles('government')"
                role="button"
                aria-label="View Government profiles"
              >
                <q-icon name="account_balance" size="1.5rem" color="white" />
                <div class="node-label">Government</div>
              </div>

              <!-- Connection Lines -->
              <svg class="connections" viewBox="0 0 500 500" preserveAspectRatio="xMidYMid meet">
                <path class="connection-line" d="M250,250 L250,75" />
                <path class="connection-line" d="M250,250 L100,175" />
                <path class="connection-line" d="M250,250 L400,175" />
                <path class="connection-line" d="M250,250 L100,325" />
                <path class="connection-line" d="M250,250 L400,325" />
                <path class="connection-line" d="M250,250 L250,425" />

                <!-- Animated Particles -->
                <circle class="particle particle-1" r="3" />
                <circle class="particle particle-2" r="3" />
                <circle class="particle particle-3" r="3" />
                <circle class="particle particle-4" r="3" />
                <circle class="particle particle-5" r="3" />
                <circle class="particle particle-6" r="3" />
              </svg>
            </div>
          </div>

          <div class="text-center q-mt-xl">
            <q-btn
              color="primary"
              label="Join Our Ecosystem"
              no-caps
              class="q-px-xl"
              unelevated
              rounded
              @click="scrollToSignup"
            />
          </div>
        </div>
        <div class="col-1" />
      </div>
    </div>
  </section>
</template>

<script setup>
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// Removed scroll animations to prevent misalignment issues
onMounted(() => {
  // No animations needed - nodes are now static
});

// Map ecosystem nodes to profile types for filtering
const nodeToProfileTypeMap = {
  'startups': ['innovator'],
  'investors': ['investor'],
  'mentors': ['mentor'],
  'academia': ['academic_student', 'academic_institution'],
  'corporates': ['organisation', 'industry_expert'],
  'government': ['government_agency', 'regulatory_body']
};

// Navigate to innovation community - always default to feed tab
const navigateToProfiles = (nodeType) => {
  const profileTypes = nodeToProfileTypeMap[nodeType];
  if (profileTypes && profileTypes.length > 0) {
    // Navigate to innovation community with profiles tab and filter
    router.push({
      path: '/innovation-community',
      query: {
        tab: 'profiles',
        profileTypes: profileTypes.join(',')
      }
    });
  } else {
    // Always navigate to feed tab as default
    router.push('/innovation-community?tab=feed');
  }
};

const scrollToSignup = () => {
  const signupSection = document.getElementById('signup-section');
  if (signupSection) {
    signupSection.scrollIntoView({ behavior: 'smooth' });
  }
};
</script>

<style scoped>
.ecosystem-map-section {
  background-color: white;
  padding: 80px 0;
  position: relative;
  overflow: hidden;
}

.ecosystem-map {
  height: 500px;
  position: relative;
  margin: 40px auto;
  max-width: 800px;
}

.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.node {
  position: absolute;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
}

/* Removed scroll animations to prevent misalignment */
.scroll-node {
  opacity: 1;
  transform: scale(1);
}

.scroll-node.animate {
  opacity: 1;
  transform: scale(1);
}

.hub-node {
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, #0D8A3E 0%, #74b524 100%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 5px 15px rgba(13, 138, 62, 0.3);
}

.stakeholder-node {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #74b524 0%, #a4ca39 100%);
  box-shadow: 0 3px 10px rgba(116, 181, 36, 0.3);
}

.node-1 {
  top: 15%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.node-2 {
  top: 35%;
  left: 20%;
  transform: translate(-50%, -50%);
}

.node-3 {
  top: 35%;
  left: 80%;
  transform: translate(-50%, -50%);
}

.node-4 {
  top: 65%;
  left: 20%;
  transform: translate(-50%, -50%);
}

.node-5 {
  top: 65%;
  left: 80%;
  transform: translate(-50%, -50%);
}

.node-6 {
  top: 85%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.stakeholder-node {
  cursor: pointer;
}

/* Removed hover animations */

.stakeholder-node:active {
  transform: translate(-50%, -50%) scale(0.95);
}

.node-label {
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  margin-top: 5px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  max-width: 100%;
  padding: 0 2px;
  display: block;
  width: 100%;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}



.connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.connection-line {
  stroke: #a4ca39;
  stroke-width: 2;
  stroke-dasharray: 5;
  fill: none;
  opacity: 0.6;
}

.particle {
  fill: #0D8A3E;
  opacity: 0.8;
}

.particle-1 {
  animation: moveParticle1 8s infinite linear;
}

.particle-2 {
  animation: moveParticle2 10s infinite linear;
}

.particle-3 {
  animation: moveParticle3 7s infinite linear;
}

.particle-4 {
  animation: moveParticle4 9s infinite linear;
}

.particle-5 {
  animation: moveParticle5 11s infinite linear;
}

.particle-6 {
  animation: moveParticle6 9s infinite linear;
}

@keyframes moveParticle1 {
  0% { transform: translate(250px, 250px); }
  50% { transform: translate(250px, 75px); }
  100% { transform: translate(250px, 250px); }
}

@keyframes moveParticle2 {
  0% { transform: translate(250px, 250px); }
  50% { transform: translate(100px, 175px); }
  100% { transform: translate(250px, 250px); }
}

@keyframes moveParticle3 {
  0% { transform: translate(250px, 250px); }
  50% { transform: translate(400px, 175px); }
  100% { transform: translate(250px, 250px); }
}

@keyframes moveParticle4 {
  0% { transform: translate(250px, 250px); }
  50% { transform: translate(100px, 325px); }
  100% { transform: translate(250px, 250px); }
}

@keyframes moveParticle5 {
  0% { transform: translate(250px, 250px); }
  50% { transform: translate(400px, 325px); }
  100% { transform: translate(250px, 250px); }
}

@keyframes moveParticle6 {
  0% { transform: translate(250px, 250px); }
  50% { transform: translate(250px, 425px); }
  100% { transform: translate(250px, 250px); }
}

/* Removed bounce animations */



@media (max-width: 767px) {
  .ecosystem-map {
    height: 400px;
  }

  .hub-node {
    width: 80px;
    height: 80px;
  }

  .stakeholder-node {
    width: 60px;
    height: 60px;
  }

  .node-label {
    font-size: 0.75rem;
    line-height: 1.1;
    padding: 0 3px;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 70px !important;
  }

  .central-hub .node-label {
    white-space: nowrap !important;
    max-width: 90px !important;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .ecosystem-node {
    width: 65px;
    height: 65px;
  }

  .node-label {
    font-size: 0.7rem;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 65px !important;
    padding: 0 2px !important;
  }

  .central-hub {
    width: 80px;
    height: 80px;
  }

  .central-hub .node-label {
    font-size: 0.75rem;
    white-space: nowrap !important;
    max-width: 80px !important;
  }
}
</style>
