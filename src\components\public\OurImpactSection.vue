<template>
  <section class="our-impact-section q-py-xl">
    <div class="container q-mx-auto q-px-md">
      <div class="row">
        <div class="col-1" />
        <div class="col-10">
          <div class="text-center">
            <h2 class="text-h3 text-weight-light q-mb-md">Our Impact</h2>
            <p class="text-body1 q-mb-xl text-center">
              See the measurable difference we're making in Zimbabwe's innovation ecosystem through our growing community and initiatives.
            </p>
          </div>

          <!-- Impact Metrics Grid -->
          <div class="impact-metrics-grid">
            <div 
              v-for="(metric, index) in impactMetrics" 
              :key="index" 
              class="impact-metric-card"
              :class="{ 'metric-visible': isVisible }"
              :style="{ animationDelay: `${index * 0.2}s` }"
            >
              <div class="metric-icon-container">
                <q-icon :name="metric.icon" size="3rem" :color="metric.color" class="metric-icon" />
                <div class="icon-bg" :style="{ backgroundColor: `var(--q-${metric.color})` }"></div>
              </div>
              
              <div class="metric-content">
                <div class="metric-number">
                  <span class="animated-counter" :data-target="metric.value">
                    {{ scrollCounters[index].currentValue.value }}
                  </span>
                  <span class="metric-suffix">{{ metric.suffix }}</span>
                </div>
                <h3 class="metric-title">{{ metric.title }}</h3>
                <p class="metric-description">{{ metric.description }}</p>
              </div>
            </div>
          </div>

          <!-- Call to Action -->
          <div class="text-center q-mt-xl">
            <q-btn
              outline
              color="primary"
              :label="isAuthenticated ? 'Join Our Community' : 'Get Started Today'"
              :icon-right="isAuthenticated ? 'people' : 'arrow_forward'"
              no-caps
              class="q-px-xl cta-button"
              rounded
              size="md"
              @click="handleCTAClick"
            >
              <q-tooltip>{{ isAuthenticated ? 'Explore our community features' : 'Join our innovation ecosystem' }}</q-tooltip>
            </q-btn>
          </div>
        </div>
        <div class="col-1" />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useScrollCounter } from '@/composables/useScrollAnimations'

const router = useRouter()
const authStore = useAuthStore()

// Check if user is authenticated
const isAuthenticated = computed(() => authStore.isAuthenticated)

const isVisible = ref(false)
const countersAnimated = ref(false)

// Mobile fallback - trigger animation after a delay if not already triggered
const mobileAnimationFallback = () => {
  setTimeout(() => {
    if (!countersAnimated.value) {
      isVisible.value = true
      countersAnimated.value = true
      scrollCounters.forEach((counter, index) => {
        setTimeout(() => {
          counter.animateCounter()
        }, index * 100)
      })
    }
  }, 2000) // Wait 2 seconds before fallback
}

const impactMetrics = ref([
  {
    icon: 'people',
    color: 'primary',
    value: 500,
    suffix: '+',
    title: 'Community Members',
    description: 'Innovators, entrepreneurs, and industry experts connected'
  },
  {
    icon: 'lightbulb',
    color: 'secondary',
    value: 150,
    suffix: '+',
    title: 'Innovation Projects',
    description: 'Ideas transformed into viable business solutions'
  },
  {
    icon: 'handshake',
    color: 'positive',
    value: 75,
    suffix: '+',
    title: 'Partnerships Formed',
    description: 'Strategic connections between innovators and investors'
  },
  {
    icon: 'trending_up',
    color: 'warning',
    value: 25,
    suffix: 'M+',
    title: 'Funding Facilitated',
    description: 'USD in investment opportunities created'
  },
  {
    icon: 'school',
    color: 'info',
    value: 200,
    suffix: '+',
    title: 'Training Sessions',
    description: 'Workshops and mentorship programs delivered'
  },
  {
    icon: 'business',
    color: 'purple',
    value: 50,
    suffix: '+',
    title: 'Startups Launched',
    description: 'New businesses successfully established'
  }
])

// Create scroll counters for each metric
const scrollCounters = impactMetrics.value.map(metric => useScrollCounter(metric.value, 2000))

// Initialize intersection observer for scroll-triggered animations
onMounted(() => {
  // Add a small delay to ensure DOM is fully rendered
  setTimeout(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !countersAnimated.value) {
            isVisible.value = true
            countersAnimated.value = true

            // Start all counter animations
            scrollCounters.forEach((counter, index) => {
              // Add slight delay between counters for better visual effect
              setTimeout(() => {
                counter.animateCounter()
              }, index * 100)
            })

            // Stop observing after animation starts
            observer.unobserve(entry.target)
          }
        })
      },
      {
        // Lower threshold for mobile devices
        threshold: 0.1,
        // Reduced root margin for better mobile detection
        rootMargin: '0px 0px -50px 0px'
      }
    )

    // Observe the impact section
    const impactSection = document.querySelector('.our-impact-section')
    if (impactSection) {
      observer.observe(impactSection)
    }

    // Fallback: If section is already visible on mount, trigger animation
    const rect = impactSection?.getBoundingClientRect()
    if (rect && rect.top < window.innerHeight && rect.bottom > 0 && !countersAnimated.value) {
      isVisible.value = true
      countersAnimated.value = true
      scrollCounters.forEach((counter, index) => {
        setTimeout(() => {
          counter.animateCounter()
        }, index * 100)
      })
    }

    // Start mobile fallback timer
    mobileAnimationFallback()
  }, 100)
})

const handleCTAClick = async () => {
  if (isAuthenticated.value) {
    const { navigateTo } = await import('../../services/navigationService').then(m => ({ navigateTo: m.useNavigation().navigateTo }));
    await navigateTo('/innovation-community');
  } else {
    const signupSection = document.getElementById('signup-section')
    if (signupSection) {
      signupSection.scrollIntoView({ behavior: 'smooth' })
    }
  }
}
</script>

<style scoped>
.our-impact-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow: hidden;
}

.container {
  width: 100%;
  max-width: 1400px;
}

.impact-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

.impact-metric-card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(30px);
}

.impact-metric-card.metric-visible {
  opacity: 1;
  transform: translateY(0);
  animation: slideInUp 0.6s ease-out forwards;
}

.impact-metric-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.metric-icon-container {
  position: relative;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.metric-icon {
  position: relative;
  z-index: 2;
}

.icon-bg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  opacity: 0.1;
  z-index: 1;
}

.metric-content {
  position: relative;
}

.metric-number {
  font-size: 3rem;
  font-weight: 700;
  color: #0D8A3E;
  line-height: 1;
  margin-bottom: 0.5rem;
  display: block;
}

.metric-suffix {
  font-size: 2rem;
  color: #a4ca39;
}

.metric-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.metric-description {
  font-size: 0.9rem;
  color: #6c757d;
  line-height: 1.4;
}

.cta-button {
  transition: all 0.3s ease;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(13, 138, 62, 0.3);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .our-impact-section {
    padding: 2rem 0;
  }

  .impact-metrics-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin: 2rem 0;
  }

  .impact-metric-card {
    padding: 1.5rem;
    margin: 0 auto;
    max-width: 350px;
  }

  .metric-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #0D8A3E;
  }

  .metric-suffix {
    font-size: 1.5rem;
    font-weight: 600;
  }

  .metric-title {
    font-size: 1.1rem;
    font-weight: 700;
  }

  .metric-description {
    font-size: 0.85rem;
    line-height: 1.5;
  }
}

@media (max-width: 480px) {
  .our-impact-section {
    padding: 1.5rem 0;
  }

  .impact-metrics-grid {
    gap: 1rem;
    margin: 1.5rem 0;
  }

  .impact-metric-card {
    padding: 1.25rem;
    max-width: 300px;
  }

  .metric-number {
    font-size: 2.2rem;
    font-weight: 900;
  }

  .metric-suffix {
    font-size: 1.3rem;
    font-weight: 700;
  }

  .metric-title {
    font-size: 1rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
  }

  .metric-description {
    font-size: 0.8rem;
    line-height: 1.4;
  }

  .metric-icon-container {
    margin-bottom: 1rem;
  }

  .metric-icon {
    font-size: 2.5rem !important;
  }
}

/* Ensure counters are always visible */
.animated-counter {
  display: inline-block;
  min-width: 1ch;
  text-align: center;
}

/* Force visibility on mobile */
@media (max-width: 767px) {
  .impact-metric-card {
    opacity: 1 !important;
    transform: translateY(0) !important;
  }

  .metric-number {
    visibility: visible !important;
    opacity: 1 !important;
  }
}
</style>
