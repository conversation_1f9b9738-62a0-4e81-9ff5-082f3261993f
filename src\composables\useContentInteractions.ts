/**
 * Content Interactions Composable
 * 
 * This composable provides shared functionality for content interactions
 * across all tabs (feed, marketplace, events, groups, profiles).
 * It centralizes common actions like sharing, saving, viewing, and favoriting.
 */

import { useRouter } from 'vue-router';
import { Ref } from 'vue';
import { useNotificationStore } from '@/stores/notifications';
import { usePostsStore } from '@/stores/posts';
import { supabase } from '@/lib/supabase';
import { useGlobalServicesStore } from '@/stores/globalServices';
import { sanitizeText, sanitizeFormInput, defaultRateLimiter } from '@/lib/security';

export type ContentType = 'post' | 'listing' | 'event' | 'group' | 'profile';

export interface ContentItem {
  id: number | string;
  title?: string;
  name?: string;
  content?: string;
  description?: string;
  bio?: string;
  slug?: string;
  postType?: string;
  subType?: string; // Added for marketplace posts
  userId?: string;
  author?: string;
  isSaved?: boolean;
  isFavorite?: boolean;
}

export function useContentInteractions() {
  const router = useRouter();
  const notificationStore = useNotificationStore();
  const postsStore = usePostsStore();
  const globalServices = useGlobalServicesStore();
  const activityService = globalServices.activityService;

  /**
   * Generic share function that works for all content types
   */
  const shareContent = async (
    contentId: number | string,
    contentType: ContentType,
    contentData?: ContentItem
  ): Promise<void> => {
    console.log(`Share ${contentType}:`, contentId);

    if (!contentData) {
      notificationStore.error(`Could not find ${contentType} to share`);
      return;
    }

    // Build URL and content based on type
    let shareUrl = `${window.location.origin}/innovation-community`;
    let title = '';
    let text = '';

    switch (contentType) {
      case 'post':
        if (contentData.postType?.toLowerCase() === 'blog' && contentData.slug) {
          shareUrl += `/article/${contentData.slug}`;
        } else {
          shareUrl += `/post/${contentId}`;
        }
        // Handle marketplace posts with appropriate title and description
        if (contentData.postType?.toLowerCase() === 'marketplace' || contentData.subType?.toLowerCase() === 'marketplace') {
          title = contentData.title || 'Marketplace Listing';
          text = contentData.description || contentData.content ?
            (contentData.description || contentData.content).substring(0, 100) + '...' :
            'Check out this marketplace listing';
        } else {
          title = contentData.title || 'Community Post';
          text = contentData.content ? contentData.content.substring(0, 100) + '...' : 'Check out this post';
        }
        break;
      case 'listing':
        shareUrl += `/marketplace/${contentId}`;
        title = contentData.title || 'Marketplace Listing';
        text = contentData.description ? contentData.description.substring(0, 100) + '...' : 'Check out this marketplace listing';
        break;
      case 'event':
        shareUrl += `/event/${contentId}`;
        title = contentData.title || contentData.name || 'Community Event';
        text = contentData.description ? contentData.description.substring(0, 100) + '...' : 'Check out this event';
        break;
      case 'group':
        shareUrl += `/group/${contentId}`;
        title = contentData.name || 'Community Group';
        text = contentData.description ? contentData.description.substring(0, 100) + '...' : 'Check out this group';
        break;
      case 'profile':
        shareUrl += `/profile/${contentId}`;
        title = contentData.name || 'Community Profile';
        text = contentData.bio ? contentData.bio.substring(0, 100) + '...' : 'Check out this profile';
        break;
    }

    // Try to use the Web Share API if available
    if (navigator.share) {
      try {
        await navigator.share({
          title,
          text,
          url: shareUrl
        });
      } catch (err) {
        console.log('Error sharing:', err);
        // Fallback to copying to clipboard
        await copyToClipboard(shareUrl);
      }
    } else {
      // Fallback to copying to clipboard
      await copyToClipboard(shareUrl);
    }
  };

  /**
   * Generic save/favorite function that works for all content types
   * Uses actual database tables: saved_posts for posts, matchmaking_results.is_saved for profiles
   */
  const toggleSaveContent = async (
    contentId: number | string,
    contentType: ContentType,
    contentData?: ContentItem
  ): Promise<boolean> => {
    console.log(`Toggle save ${contentType}:`, contentId);

    if (!contentData) {
      notificationStore.error(`Could not find ${contentType}`);
      return false;
    }

    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      notificationStore.warning('Please sign in to save content');
      router.push({
        name: 'sign-in',
        query: { redirect: router.currentRoute.value.fullPath }
      });
      return false;
    }

    // Determine the property name and action
    const savedKey = contentType === 'listing' ? 'isFavorite' : 'isSaved';
    const currentState = contentData[savedKey];
    const newState = !currentState;

    // Update the local state immediately for better UX
    contentData[savedKey] = newState;

    // Get appropriate messages and icons
    const actionName = contentType === 'listing' ? 'favorite' : 'save';
    const savedMessage = contentType === 'listing' ? 'Added to favorites' : `${contentType.charAt(0).toUpperCase() + contentType.slice(1)} saved`;
    const unsavedMessage = contentType === 'listing' ? 'Removed from favorites' : `${contentType.charAt(0).toUpperCase() + contentType.slice(1)} unsaved`;
    const savedIcon = contentType === 'listing' ? 'favorite' : 'bookmark';
    const unsavedIcon = contentType === 'listing' ? 'favorite_border' : 'bookmark_border';

    try {
      // Handle different content types with appropriate database operations
      if (contentType === 'post' || contentType === 'listing' || contentType === 'event' || contentType === 'group') {
        // All these are posts in the database, use saved_posts table
        if (newState) {
          // Save the post
          const { error } = await supabase
            .from('saved_posts')
            .insert({
              user_id: user.id,
              post_id: contentId
            });

          if (error && error.code !== '23505') { // 23505 is unique constraint violation (already saved)
            throw error;
          }
        } else {
          // Unsave the post
          const { error } = await supabase
            .from('saved_posts')
            .delete()
            .eq('user_id', user.id)
            .eq('post_id', contentId);

          if (error) throw error;
        }
      } else if (contentType === 'profile') {
        // Use saved_profiles table for profile bookmarking
        if (newState) {
          // Save the profile
          const { error } = await supabase
            .from('saved_profiles')
            .insert({
              user_id: user.id,
              profile_id: contentId
            });

          if (error && error.code !== '23505') { // 23505 is unique constraint violation (already saved)
            throw error;
          }
        } else {
          // Unsave the profile
          const { error } = await supabase
            .from('saved_profiles')
            .delete()
            .eq('user_id', user.id)
            .eq('profile_id', contentId);

          if (error) throw error;
        }
      }

      notificationStore.success(
        newState ? savedMessage : unsavedMessage,
        newState ? savedIcon : unsavedIcon
      );

      // Track the bookmark activity
      try {
        const activityType = newState ? 'content_save' : 'content_unsave';
        const activityDetails = {
          content_id: contentId,
          content_type: contentType,
          content_title: contentData.title || contentData.name || '',
          content_preview: (contentData.content || contentData.description || contentData.bio || '').substring(0, 100)
        };

        await activityService.trackActivity(activityType, activityDetails);
      } catch (activityError) {
        console.warn('Error tracking bookmark activity (non-critical):', activityError);
        // Don't throw here as the bookmark operation was successful
      }

      return true;
    } catch (error) {
      console.error(`Error ${actionName}ing ${contentType}:`, error);
      // Revert the local state if the operation fails
      contentData[savedKey] = currentState;
      notificationStore.error(`Failed to ${actionName} ${contentType}`);
      return false;
    }
  };

  /**
   * Generic view function that works for all content types
   */
  const viewContent = (
    contentId: number | string,
    contentType: ContentType,
    contentData?: ContentItem
  ): void => {
    console.log(`useContentInteractions: View ${contentType}:`, contentId);
    console.log('useContentInteractions: Content data:', contentData);

    try {
      // Navigate based on content type
      switch (contentType) {
        case 'post':
          if (contentData) {
            // Special handling for blog posts which use slug-based routing
            if (contentData.postType?.toLowerCase() === 'blog' && contentData.slug) {
              // Determine if we're in the innovation community context
              const isInnovationCommunity = window.location.pathname.includes('innovation-community');
              const routeName = isInnovationCommunity ? 'innovation-community-article' : 'article';
              console.log('useContentInteractions: Navigating to blog article:', { routeName, slug: contentData.slug });
              router.push({ name: routeName, params: { slug: contentData.slug } });
              return;
            }
          }
          // For all other post types (including marketplace posts), navigate to the unified post-details view
          console.log('useContentInteractions: Navigating to post details:', { contentId });
          router.push({
            name: 'post-details',
            params: { id: contentId }
          });
          break;
        case 'listing':
          console.log('useContentInteractions: Navigating to unified post view for listing:', { contentId });
          router.push({
            name: 'post-details',
            params: { id: contentId }
          });
          break;
        case 'event':
          console.log('useContentInteractions: Navigating to event details:', { contentId });
          router.push({
            name: 'event-details',
            params: { id: contentId }
          });
          break;
        case 'group':
          console.log('useContentInteractions: Navigating to group:', { contentId });
          router.push({
            name: 'group',
            params: { id: contentId }
          });
          break;
        case 'profile':
          console.log('useContentInteractions: Navigating to profile:', { contentId });
          router.push({
            name: 'profile',
            params: { id: contentId }
          });
          break;
        default:
          console.warn('useContentInteractions: Unknown content type:', contentType);
      }
    } catch (error) {
      console.error('useContentInteractions: Error in viewContent:', error);
    }
  };

  /**
   * Like a post (unified method for all components)
   */
  const likeContent = async (postId: number): Promise<boolean> => {
    try {
      console.log('likeContent: Starting like operation for post:', postId);
      const result = await postsStore.likePost(postId);
      if (result) {
        // The store already updates the specific post with fresh database data
        console.log('likeContent: Like operation successful');
        return true;
      } else {
        console.warn('likeContent: Like operation returned null/false');
        notificationStore.error('Failed to update like status');
        return false;
      }
    } catch (error: any) {
      console.error('likeContent: Error in like operation:', error);

      // Provide user-friendly error messages and redirect for authentication
      if (error.message?.includes('not authenticated')) {
        notificationStore.warning('Please sign in to like posts');
        router.push({
          name: 'sign-in',
          query: { redirect: router.currentRoute.value.fullPath }
        });
      } else if (error.message?.includes('not found')) {
        notificationStore.error('Post not found');
      } else {
        notificationStore.error('Failed to like post. Please try again.');
      }

      return false;
    }
  };

  /**
   * Contact seller/author (with authentication check)
   */
  const contactUser = async (
    contentId: number | string,
    contentType: ContentType,
    contentData?: ContentItem
  ): Promise<void> => {
    console.log(`Contact user for ${contentType}:`, contentId);

    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      // User is not logged in, show login prompt
      notificationStore.warning('Please sign in to contact the user');

      // Redirect to login page
      router.push({
        name: 'sign-in',
        query: { redirect: router.currentRoute.value.fullPath }
      });
      return;
    }

    if (!contentData || !contentData.userId) {
      notificationStore.error('Could not find user information');
      return;
    }

    // Don't allow messaging yourself
    if (contentData.userId === user.id) {
      notificationStore.warning('This is your own content');
      return;
    }

    // Open message dialog to contact the user
    const { Dialog } = await import('quasar');
    const MessageDialog = (await import('@/components/messaging/MessageDialog.vue')).default;
    const { getUniversalUsername } = await import('@/utils/userUtils');

    Dialog.create({
      component: MessageDialog,
      componentProps: {
        userId: contentData.userId,
        userName: contentData.author ? getUniversalUsername({ name: contentData.author }) : 'User'
      }
    });
  };

  /**
   * Helper function to copy text to clipboard
   */
  const copyToClipboard = async (text: string): Promise<void> => {
    if (navigator.clipboard) {
      try {
        await navigator.clipboard.writeText(text);
        notificationStore.success('Link copied to clipboard', 'content_copy');
      } catch (err) {
        console.error('Failed to copy to clipboard:', err);
        notificationStore.error('Failed to copy link');
      }
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        notificationStore.success('Link copied to clipboard', 'content_copy');
      } catch (err) {
        console.error('Failed to copy to clipboard:', err);
        notificationStore.error('Failed to copy link');
      }
      document.body.removeChild(textArea);
    }
  };

  /**
   * Register for an event
   * Uses the event_registrations table to track registrations
   */
  const registerForEvent = async (eventId: number | string): Promise<void> => {
    console.log('Register for event:', eventId);

    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      notificationStore.warning('Please sign in to register for events');
      router.push({
        name: 'sign-in',
        query: { redirect: router.currentRoute.value.fullPath }
      });
      return;
    }

    try {
      // Check if user is already registered
      const { data: existingRegistration, error: checkError } = await supabase
        .from('event_registrations')
        .select('*')
        .eq('user_id', user.id)
        .eq('post_id', eventId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
        throw checkError;
      }

      if (existingRegistration) {
        notificationStore.warning('You are already registered for this event');
        return;
      }

      // Register for the event
      const { error } = await supabase
        .from('event_registrations')
        .insert({
          user_id: user.id,
          post_id: eventId,
          registration_status: 'registered',
          registration_data: {}
        });

      if (error) throw error;

      notificationStore.success('Successfully registered for event!', 'event_available');
    } catch (error) {
      console.error('Error registering for event:', error);
      notificationStore.error('Failed to register for event');
    }
  };

  /**
   * Join a group
   * Uses the group_memberships table to track group memberships (groups are posts)
   */
  const joinGroup = async (groupId: number | string): Promise<void> => {
    console.log('Join group:', groupId);

    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      notificationStore.warning('Please sign in to join groups');
      router.push({
        name: 'sign-in',
        query: { redirect: router.currentRoute.value.fullPath }
      });
      return;
    }

    try {
      // Check if user is already a member
      const { data: existingMembership, error: checkError } = await supabase
        .from('group_memberships')
        .select('*')
        .eq('user_id', user.id)
        .eq('group_post_id', groupId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
        throw checkError;
      }

      if (existingMembership) {
        notificationStore.warning('You are already a member of this group');
        return;
      }

      // Join the group
      const { error } = await supabase
        .from('group_memberships')
        .insert({
          user_id: user.id,
          group_post_id: groupId,
          membership_status: 'member',
          role: 'member'
        });

      if (error) throw error;

      notificationStore.success('Successfully joined group!', 'group_add');
    } catch (error) {
      console.error('Error joining group:', error);
      notificationStore.error('Failed to join group');
    }
  };

  /**
   * Apply for an opportunity
   */
  const applyForOpportunity = async (opportunityId: number | string): Promise<void> => {
    console.log('Apply for opportunity:', opportunityId);

    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      notificationStore.warning('Please sign in to apply for opportunities');
      router.push({
        name: 'sign-in',
        query: { redirect: router.currentRoute.value.fullPath }
      });
      return;
    }

    try {
      // Here you would make an API call to apply for the opportunity
      notificationStore.success('Application submitted successfully!', 'check_circle');
    } catch (error) {
      notificationStore.error('Failed to submit application');
    }
  };

  /**
   * Request collaboration
   */
  const requestCollaboration = async (contentId: number | string): Promise<void> => {
    console.log('Request collaboration:', contentId);

    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      notificationStore.warning('Please sign in to request collaboration');
      router.push({
        name: 'sign-in',
        query: { redirect: router.currentRoute.value.fullPath }
      });
      return;
    }

    try {
      // Here you would make an API call to request collaboration
      notificationStore.success('Collaboration request sent!', 'people');
    } catch (error) {
      notificationStore.error('Failed to send collaboration request');
    }
  };

  /**
   * Request mentorship
   */
  const requestMentorship = async (contentId: number | string): Promise<void> => {
    console.log('Request mentorship:', contentId);

    // Check if user is authenticated
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      notificationStore.warning('Please sign in to request mentorship');
      router.push({
        name: 'sign-in',
        query: { redirect: router.currentRoute.value.fullPath }
      });
      return;
    }

    try {
      // Here you would make an API call to request mentorship
      notificationStore.success('Mentorship request sent!', 'school');
    } catch (error) {
      notificationStore.error('Failed to send mentorship request');
    }
  };

  /**
   * Toggle comments visibility and load comments if needed
   */
  const toggleComments = async (
    contentId: number | string,
    contentType: ContentType,
    showComments: Ref<boolean>,
    commentsData: Ref<any[]>,
    loadingComments: Ref<boolean>
  ): Promise<void> => {
    // Toggle the visibility
    showComments.value = !showComments.value;

    // If showing comments and we don't have any loaded yet, fetch them
    if (showComments.value && commentsData.value.length === 0) {
      await loadComments(contentId, commentsData, loadingComments);
    }
  };

  /**
   * Load comments for content
   */
  const loadComments = async (
    contentId: number | string,
    commentsData: Ref<any[]>,
    loadingComments: Ref<boolean>
  ): Promise<void> => {
    loadingComments.value = true;
    try {
      // Fetch comments from the database using Supabase
      const { data, error } = await supabase
        .from('comments_with_authors')
        .select('*')
        .eq('post_id', contentId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Import getUniversalUsername for consistent name resolution
      const { getUniversalUsername } = await import('@/utils/userUtils');

      // Map the database comments to our frontend model
      commentsData.value = data.map((comment: any) => ({
        id: comment.id,
        author: getUniversalUsername({
          first_name: comment.first_name,
          last_name: comment.last_name,
          profile_name: comment.profile_name,
          email: comment.email
        }),
        authorId: comment.user_id,
        email: comment.email,
        avatar: '', // Empty to use initials
        date: formatCommentDate(comment.created_at),
        content: comment.content
      }));
    } catch (err) {
      console.error('Error loading comments:', err);
      notificationStore.error('Failed to load comments');
    } finally {
      loadingComments.value = false;
    }
  };

  /**
   * Submit a comment with duplicate submission prevention
   */
  const submitComment = async (
    contentId: number | string,
    contentType: ContentType,
    comment: string,
    commentsData: Ref<any[]>,
    newComment: Ref<string>,
    submittingComment?: Ref<boolean>
  ): Promise<boolean> => {
    if (!comment.trim()) return false;

    // SECURITY: Sanitize and validate comment input
    const sanitizedComment = sanitizeFormInput(comment, 2000); // 2000 char limit for comments
    if (!sanitizedComment.trim()) return false;

    // SECURITY: Rate limiting for comments
    const rateLimitKey = `comment_${contentId}_${Date.now()}`;
    if (!defaultRateLimiter.isAllowed(rateLimitKey)) {
      notificationStore.warning('Please wait before commenting again');
      return false;
    }

    // Check if user is authenticated before proceeding
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      notificationStore.warning('Please sign in to comment');
      router.push({
        name: 'sign-in',
        query: { redirect: router.currentRoute.value.fullPath }
      });
      return false;
    }

    // Prevent duplicate submissions
    if (submittingComment?.value) {
      console.log('Comment submission already in progress');
      return false;
    }

    try {
      // Set submitting state
      if (submittingComment) {
        submittingComment.value = true;
      }

      // Use the posts store to submit the comment with sanitized content
      const success = await postsStore.commentOnPost(Number(contentId), sanitizedComment);

      if (success) {
        // Add to local state temporarily for immediate feedback
        commentsData.value.unshift({
          id: Date.now(),
          author: 'You',
          authorId: null,
          email: '',
          avatar: '',
          date: 'Just now',
          content: sanitizedComment
        });

        // Clear the input
        newComment.value = '';

        notificationStore.success('Comment added successfully!');
        return true;
      }
      return false;
    } catch (err) {
      console.error('Error submitting comment:', err);
      notificationStore.error('Failed to add comment');
      return false;
    } finally {
      // Reset submitting state
      if (submittingComment) {
        submittingComment.value = false;
      }
    }
  };

  /**
   * Format comment date for display
   */
  const formatCommentDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

      if (diffInMinutes < 1) return 'Just now';
      if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
      if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
      if (diffInMinutes < 10080) return `${Math.floor(diffInMinutes / 1440)}d ago`;

      return date.toLocaleDateString();
    } catch (e) {
      return 'Unknown';
    }
  };

  return {
    shareContent,
    toggleSaveContent,
    viewContent,
    likeContent,
    contactUser,
    copyToClipboard,
    registerForEvent,
    joinGroup,
    applyForOpportunity,
    requestCollaboration,
    requestMentorship,
    toggleComments,
    loadComments,
    submitComment,
    formatCommentDate
  };
}
