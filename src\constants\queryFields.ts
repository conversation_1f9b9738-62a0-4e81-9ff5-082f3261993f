/**
 * Database Query Field Selection Constants
 * 
 * Standardized field selections for efficient database queries.
 * Replaces SELECT * patterns with optimized field selections.
 */

// Profile field selections
export const PROFILE_FIELDS = {
  // Minimal fields for basic profile display
  BASIC: 'user_id, first_name, last_name, profile_type, avatar_url',
  
  // Public profile fields for discovery and listings
  PUBLIC: `
    user_id, first_name, last_name, profile_type, avatar_url, 
    bio, location, profile_completion, profile_visibility
  `,
  
  // Extended fields for detailed profile views
  EXTENDED: `
    user_id, first_name, last_name, profile_type, avatar_url, 
    bio, location, profile_completion, profile_visibility,
    skills, industries, contact_email, linkedin, website
  `,
  
  // Full profile fields for profile management
  FULL: `
    user_id, first_name, last_name, profile_type, avatar_url, 
    bio, location, profile_completion, profile_visibility,
    skills, industries, contact_email, linkedin, website,
    created_at, updated_at, profile_name, profile_state
  `
} as const

// Post field selections
export const POST_FIELDS = {
  // Basic post fields for listings
  BASIC: 'id, title, content, created_at, post_type, user_id',
  
  // Post fields with author information
  WITH_AUTHOR: `
    id, title, content, created_at, post_type, sub_type, 
    featured_image, user_id, status,
    profiles:user_id (${PROFILE_FIELDS.BASIC})
  `,
  
  // Full post fields for detailed views (from posts_with_authors view)
  FULL: `
    id, title, content, created_at, updated_at, post_type,
    sub_type, featured_image, tags, status, user_id, slug,
    likes_count, comments_count, excerpt, first_name, last_name,
    email, user_has_liked, blog_title, blog_category, blog_full_content,
    event_title, event_type, event_theme, event_start_datetime,
    event_end_datetime, event_location, event_registration_url, event_organizer
  `,
  
  // Post fields for feed display (from posts_with_authors view)
  FEED: `
    id, title, content, created_at, post_type, sub_type,
    featured_image, user_id, status, likes_count, comments_count,
    first_name, last_name, email, user_has_liked, tags, excerpt,
    blog_title, blog_category, event_title, event_type, event_location
  `
} as const

// Comment field selections
export const COMMENT_FIELDS = {
  // Basic comment fields
  BASIC: 'id, content, created_at, user_id, post_id',
  
  // Comments with author information
  WITH_AUTHOR: `
    id, content, created_at, updated_at, user_id, post_id,
    profiles:user_id (${PROFILE_FIELDS.BASIC})
  `,
  
  // Comments with post context
  WITH_POST: `
    id, content, created_at, user_id, post_id,
    profiles:user_id (${PROFILE_FIELDS.BASIC}),
    posts:post_id (id, title, post_type)
  `
} as const

// Notification field selections
export const NOTIFICATION_FIELDS = {
  // Basic notification fields
  BASIC: `
    id, type, title, message, is_read, created_at, 
    user_id, related_entity_id, related_entity_type
  `,
  
  // Notifications with metadata
  WITH_METADATA: `
    id, type, title, message, is_read, created_at, 
    user_id, related_entity_id, related_entity_type,
    metadata, action_url
  `
} as const

// Message field selections
export const MESSAGE_FIELDS = {
  // Basic message fields
  BASIC: `
    id, content, created_at, sender_id, recipient_id, 
    is_read, message_type
  `,
  
  // Messages with sender information
  WITH_SENDER: `
    id, content, created_at, sender_id, recipient_id, 
    is_read, message_type,
    sender:sender_id (${PROFILE_FIELDS.BASIC}),
    recipient:recipient_id (${PROFILE_FIELDS.BASIC})
  `
} as const

// Connection field selections
export const CONNECTION_FIELDS = {
  // Basic connection fields
  BASIC: `
    id, user_id, connected_user_id, status, created_at
  `,
  
  // Connections with user profiles
  WITH_PROFILES: `
    id, user_id, connected_user_id, status, created_at,
    user:user_id (${PROFILE_FIELDS.BASIC}),
    connected_user:connected_user_id (${PROFILE_FIELDS.BASIC})
  `
} as const

// News/Content field selections
export const CONTENT_FIELDS = {
  // Basic content fields
  BASIC: `
    id, title, content, created_at, status, featured_image
  `,
  
  // Full content fields
  FULL: `
    id, title, content, excerpt, created_at, updated_at, 
    status, featured_image, tags, category, author,
    view_count, like_count
  `
} as const

// Specialized profile field selections by type
export const SPECIALIZED_PROFILE_FIELDS = {
  innovator: `
    innovation_area, innovation_stage, industry, 
    funding_amount, current_challenges, short_term_goals
  `,
  
  investor: `
    investment_focus, investment_stage, ticket_size,
    portfolio_companies, investment_criteria
  `,
  
  mentor: `
    expertise_areas, mentoring_experience, availability,
    mentoring_approach, success_stories
  `,
  
  professional: `
    job_title, company, industry, skills, experience_level,
    career_goals, availability_for_opportunities
  `,
  
  industry_expert: `
    expertise_domain, years_of_experience, consultation_areas,
    speaking_topics, research_interests
  `,
  
  academic_student: `
    institution, degree_program, graduation_year,
    research_interests, academic_achievements
  `,
  
  academic_institution: `
    institution_name, institution_type, founding_year,
    academic_programs, research_areas, student_population
  `,
  
  organisation: `
    organisation_name, organisation_type, industry,
    size, mission, services_offered
  `
} as const

// Like field selections
export const LIKE_FIELDS = {
  // Basic like fields for checking existence
  BASIC: 'id, post_id, user_id, created_at'
} as const

// Raw post fields (for posts table, not posts_with_authors view)
export const RAW_POST_FIELDS = {
  // Basic post fields for ownership checks
  BASIC: 'id, user_id, title, content, post_type, sub_type, status, created_at',

  // Full post fields for editing
  FULL: `
    id, user_id, title, content, post_type, sub_type, status,
    featured_image, tags, slug, created_at, updated_at
  `
} as const

// Helper function to get profile fields with specialized data
export function getProfileFieldsWithSpecialized(profileType: string): string {
  const baseFields = PROFILE_FIELDS.EXTENDED
  const specializedFields = SPECIALIZED_PROFILE_FIELDS[profileType as keyof typeof SPECIALIZED_PROFILE_FIELDS]

  if (!specializedFields) {
    return baseFields
  }

  return `
    ${baseFields},
    ${profileType}_profiles:user_id (${specializedFields})
  `
}

// Query field validation
export function validateFieldSelection(fields: string, tableName: string): boolean {
  // Basic validation to ensure fields don't contain dangerous patterns
  const dangerousPatterns = [
    /\*/,  // SELECT *
    /;/,   // SQL injection
    /--/,  // SQL comments
    /\/\*/, // SQL block comments
  ]
  
  return !dangerousPatterns.some(pattern => pattern.test(fields))
}
