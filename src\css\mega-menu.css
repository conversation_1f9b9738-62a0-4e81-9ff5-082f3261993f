/* Compact Mega Menu Styles */
.compact-menu-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  width: 100%;
}

/* Menu-specific styles */
.innovation-community-menu {
  border-top: 3px solid #4caf50;
}

.hub-facilities-menu {
  border-top: 3px solid #74b524;
}

/* Menu item styles */
.menu-list .q-item {
  border-radius: 4px;
  transition: all 0.2s ease;
  padding: 8px 12px;
}

.menu-list .q-item:hover {
  background-color: rgba(13, 138, 62, 0.05);
  transform: translateX(2px);
}

.menu-list .q-item-label {
  font-weight: 500;
  font-size: 14px;
}

.menu-list .q-item-label + .q-item-label {
  font-weight: 400;
  font-size: 12px;
  color: #666;
}

/* Header styles */
.compact-menu-card .q-card-section.bg-primary {
  background: linear-gradient(135deg, var(--q-primary) 0%, #4caf50 100%) !important;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .menu-list .q-item {
    padding: 6px 8px;
  }

  .menu-list .q-item-label {
    font-size: 13px;
  }

  .menu-list .q-item-label + .q-item-label {
    font-size: 11px;
  }
}
