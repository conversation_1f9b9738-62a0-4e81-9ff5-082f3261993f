<template>
  <q-layout view="lHh LpR lFf" class="innovation-community-layout">
    <!-- Fixed Header -->
       <q-header elevated class="glass-header">  
      <InnovationCommunityTopBar
        :active-tab="activeTab"
        @toggle-left-drawer="toggleLeftDrawer"
        @open-create="openCreateDialog"
        @trigger-ai="handleAITrigger"
        @tab-change="handleNavigation"
      />
     </q-header>

    <!-- Mobile Drawers Only -->
    <q-drawer
      v-if="$q.screen.lt.md"
      v-model="leftDrawerOpen"
      side="left"
      :width="280"
      :breakpoint="768"
      bordered
      class="innovation-community-left-drawer"
    >
      <InnovationCommunityLeftSidebar
        :active-tab="activeTab"
        @navigate="handleNavigation"
      />
    </q-drawer>

    <!-- Page Container -->
    <q-page-container class="innovation-community-page-container">
      <!-- Mobile Filter Bar -->
      <div v-if="$q.screen.lt.md" class="mobile-filter-bar">
        <InnovationCommunityMobileFilters
          :active-tab="activeTab"
          :posts="[]"
          :events="[]"
          :profiles="[]"
          :articles="[]"
          :marketplace="[]"
          :loading="false"
          :has-more="false"
          :show-results="false"
          @filter-change="handleFilterChange"
          @filter-results="handleFilterResults"
          @tab-change="handleNavigation"
        />
      </div>

      <!-- Main Content Page -->
      <q-page class="innovation-community-page">
        <!-- Desktop Layout - Copy EXACT structure from reference -->
        <div v-if="$q.screen.gt.sm" class="main">
          <!-- Left nav -->
          <aside class="left">
            <!-- Community Section -->
            <div class="panel e1 q-mb-md">
              <div class="community-title q-mb-sm">
                <q-icon name="groups" class="q-mr-sm" color="primary" size="md" />
                <span class="text-h6 text-weight-bold text-primary">Innovation Community</span>
              </div>
              <p class="text-caption text-grey-7 q-mb-none">
                Discover the latest posts, updates, and conversations from our innovation community
              </p>
            </div>

            <!-- Dynamic Filters -->
            <div class="panel e1">
              <DynamicFilterComponent
                :active-tab="activeTab"
                @filter-changed="handleFilterChange"
                @filter-results="handleFilterResults"
              />
            </div>
          </aside>

          <!-- Center content -->
          <div class="center">
            <!-- Featured Section -->
            <FeaturedSection
              :tab="activeTab"
              :loading="featuredLoading"
              @refresh="refreshFeaturedContent"
            />

            <!-- Main Content -->
            <router-view />
          </div>

          <!-- Right nav -->
          <aside class="right">
            <!-- Community Announcements -->
            <div class="panel e1 q-mb-md">
              <div class="text-subtitle2 q-mb-sm">Community Announcements</div>  
              <div class="text-caption text-grey-7 q-mb-sm">Latest updates and news</div>
              <div class="announcements-list">
                <div class="announcement-item">
                  <q-icon name="campaign" color="primary" />
                  <span>Welcome to the new innovation community experience!</span>
                </div>
                <div class="announcement-item">
                  <q-icon name="event" color="orange" />
                  <span>Upcoming mentorship events this week</span>
                </div>
                <div class="announcement-item">
                  <q-icon name="info" color="blue" />
                  <span>New features coming soon</span>
                </div>
              </div>
            </div>

            <!-- Latest Featured -->
            <div class="panel e1 q-mb-md">
              <div class="text-subtitle2 q-mb-sm">Latest Featured</div>
              <div class="text-caption text-grey-7 q-mb-sm">Trending content across the platform</div>
              <q-btn
                flat
                dense
                icon="refresh"
                label="Refresh"
                size="sm"
                @click="refreshFeatured"
              />
            </div>

            <!-- Entrepreneurs -->
            <div class="panel e1 q-mb-md">
              <div class="text-subtitle2 q-mb-sm">Entrepreneurs</div>
              <div class="text-caption text-grey-7 q-mb-sm">Connect with business innovators</div>
              <q-btn
                flat
                dense
                size="sm"
                @click="discoverEntrepreneurs"
              >
                <q-icon name="business" class="q-mr-xs" />
                Discover
              </q-btn>
            </div>

            <!-- Mentors -->
            <div class="panel e1">
              <div class="text-subtitle2 q-mb-sm">Mentors</div>
              <div class="text-caption text-grey-7 q-mb-sm">Find guidance and expertise</div>
              <q-btn
                flat
                dense
                size="sm"
                @click="findMentors"
              >
                <q-icon name="school" class="q-mr-xs" />
                Find Mentors
              </q-btn>
            </div>
          </aside>
        </div>

        <!-- Mobile Layout -->
        <div v-else class="mobile-layout">
          <!-- Featured Section -->
          <div class="featured-section-container">
            <FeaturedSection
              :tab="activeTab"
              :loading="featuredLoading"
              @refresh="refreshFeaturedContent"
            />
          </div>

          <!-- Main Content -->
          <div class="content-container">
            <router-view />
          </div>
        </div>
      </q-page>
    </q-page-container>

    <!-- Post Creation Dialog -->
    <PostCreationDialog
      v-model="showCreateDialog"
      :active-tab="activeTab"
      @post-created="handlePostCreated"
    />

    <!-- AI Chat Assistant -->
    <AIChatAssistant />

    <!-- News Ticker Component -->
    <NewsTickerComponent />

    <!-- Unified Authentication Dialogs -->
    <UnifiedAuthDialogs />
  </q-layout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import InnovationCommunityTopBar from '../components/layout/InnovationCommunityTopBar.vue'
import InnovationCommunityLeftSidebar from '../components/layout/InnovationCommunityLeftSidebar.vue'
import InnovationCommunityMobileFilters from '../components/layout/InnovationCommunityMobileFilters.vue'
import PostCreationDialog from '../components/feed/PostCreationDialog.vue'       
import FeaturedSection from '../components/feed/FeaturedSection.vue'
import DynamicFilterComponent from '../components/feed/DynamicFilterComponent.vue'
import AIChatAssistant from '../components/ai/AIChatAssistant.vue'
import NewsTickerComponent from '../components/news/NewsTickerComponent.vue'     
import UnifiedAuthDialogs from '../components/auth/UnifiedAuthDialogs.vue'       

// Emits
const emit = defineEmits<{
  'tab-change': [tab: string]
  'ai-trigger': [query: string]
  'post-created': [post: any]
  'filter-change': [filters: any]
  'filter-results': [filters: any]
  'refresh-featured': []
}>()

// Composables
const route = useRoute()
const router = useRouter()

// State
const leftDrawerOpen = ref(false) // Only for mobile
const showCreateDialog = ref(false)
const featuredLoading = ref(false)
const currentFilters = ref({})

// Computed
const activeTab = computed(() => {
  return route.query.tab as string || 'feed'
})

// Methods
function toggleLeftDrawer() {
  leftDrawerOpen.value = !leftDrawerOpen.value
}

function openCreateDialog() {
  showCreateDialog.value = true
}

function handleAITrigger(query: string) {
  emit('ai-trigger', query)
}

function handleNavigation(tab: string) {
  // Navigate directly to the new tab
  router.push({
    path: '/innovation-community',
    query: { ...route.query, tab }
  })
  // Close mobile drawer after navigation
  leftDrawerOpen.value = false
}

function handlePostCreated(post: any) {
  emit('post-created', post)
  showCreateDialog.value = false
}

function handleFilterChange(filters: any) {
  console.log('Layout: Filter change received:', filters)
  currentFilters.value = filters
  emit('filter-change', filters)
}

function handleFilterResults(filters: any) {
  console.log('Layout: Filter results requested:', filters)
  currentFilters.value = filters

  // Dispatch custom event for child component to listen
  window.dispatchEvent(new CustomEvent('filter-results', { detail: filters }))   

  emit('filter-results', filters)
}

function refreshFeatured() {
  // Emit refresh event for featured content
  emit('refresh-featured')
}

function refreshFeaturedContent() {
  featuredLoading.value = true
  // Emit refresh event for featured content
  emit('refresh-featured')
  // Reset loading state after a brief delay
  setTimeout(() => {
    featuredLoading.value = false
  }, 1000)
}

function discoverEntrepreneurs() {
  // Navigate to profiles with entrepreneur filter
  emit('tab-change', 'profiles')
}

function findMentors() {
  // Navigate to profiles with mentor filter
  emit('tab-change', 'profiles')
}

// No lifecycle hooks needed for grid layout
</script>

<style scoped>
.innovation-community-layout {
  background: #f8f9fa;
  min-height: 100vh;
}

.glass-header {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.innovation-community-left-drawer {
  background: white;
  border-right: 1px solid #e5e7eb;
}

.innovation-community-page-container {
  background: #f8f9fa;
}

.mobile-filter-bar {
  position: fixed;
  top: 64px;
  left: 0;
  right: 0;
  z-index: 999;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 8px 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 0;
}

/* Remove padding to eliminate gap between header and content */
.innovation-community-page {
  padding: 0 !important;
  margin: 0 !important;
}

/* Copy EXACT styles from reference design */
.main {
  display: grid;
  grid-template-columns: 280px minmax(0, 1fr) 280px;
  gap: 16px;
  padding: 16px;
  min-height: calc(100vh - 64px);
  background: #f8f9fa;
  width: 100%;
  max-width: 100vw;
  overflow: hidden;
}

/* Left and Right panels */
.left, .right {
  display: flex;
  flex-direction: column;
}

/* Center content */
.center {
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 0;
  overflow: hidden;
  padding: 0 16px; /* Add horizontal padding to prevent cards from going over edge */
}

/* Panel styling - EXACT match */
.panel {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

/* Community title */
.community-title {
  display: flex;
  align-items: center;
}

/* Announcements */
.announcements-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.announcement-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f9fafb;
  border-radius: 6px;
  font-size: 12px;
  color: #4b5563;
}

/* Mobile Layout */
.mobile-layout {
  padding-top: 120px; /* Account for mobile filter bar */
}

.mobile-layout .featured-section-container {
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

.mobile-layout .content-container {
  padding: 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .desktop-layout {
    display: none;
  }

  .mobile-layout {
    display: block;
  }
}

@media (min-width: 769px) {
  .mobile-layout {
    display: none;
  }
}
</style>
