/**
 * AI Chat Trigger Service
 *
 * Handles triggering AI chat with contextual messages and suggestions
 */

interface TriggerContext {
  key: string
  context?: string
  profileType?: string
}

interface TriggerConfig {
  message: string
  suggestions: string[]
  action_buttons?: any[]
}

class AIChatTriggerService {
  private triggerConfigs: Record<string, TriggerConfig> = {
    // General triggers
    general_assistance: {
      message: "Hello! I'm here to help you navigate the ZbInnovation platform. What would you like to know?",
      suggestions: [
        "How do I complete my profile?",
        "Help me find connections",
        "Show me relevant opportunities",
        "How does the platform work?"
      ]
    },
    
    // Profile enhancement triggers
    complete_profile: {
      message: "I can help you complete your profile to maximize your visibility and connections. What section would you like to work on?",
      suggestions: [
        "What information should I include?",
        "How can I make my profile stand out?",
        "Show me profile examples",
        "Help me write a better bio"
      ]
    },
    
    optimize_profile: {
      message: "Let's optimize your profile for better visibility and engagement. I can help you improve various sections.",
      suggestions: [
        "Review my current profile",
        "Suggest improvements",
        "Help with keywords",
        "Optimize for my goals"
      ]
    },
    
    // Networking triggers
    find_connections: {
      message: "I can help you find and connect with relevant people in the innovation ecosystem. What type of connections are you looking for?",
      suggestions: [
        "Find mentors in my field",
        "Connect with potential collaborators",
        "Find investors for my project",
        "Network with industry experts"
      ]
    },
    
    networking: {
      message: "Let's expand your network! I can help you find the right people to connect with based on your interests and goals.",
      suggestions: [
        "Find people in my industry",
        "Connect with similar profiles",
        "Find collaboration opportunities",
        "Get networking tips"
      ]
    },
    
    // Content discovery triggers
    content_discovery: {
      message: "I can help you discover relevant content, discussions, and opportunities on the platform. What interests you?",
      suggestions: [
        "Find relevant posts",
        "Discover trending topics",
        "Find events in my area",
        "Show me marketplace items"
      ]
    },
    
    discover_content: {
      message: "Let me help you find content that matches your interests and goals. What type of content are you looking for?",
      suggestions: [
        "Latest industry news",
        "Innovation trends",
        "Learning resources",
        "Success stories"
      ]
    },
    
    // Opportunity triggers
    collaboration_opportunities: {
      message: "I can help you find collaboration opportunities that match your skills and interests. What type of collaboration are you seeking?",
      suggestions: [
        "Find project partners",
        "Join existing projects",
        "Start a new collaboration",
        "Find skill exchanges"
      ]
    },
    
    discover_opportunities: {
      message: "Let me help you discover opportunities in the innovation ecosystem. What type of opportunities interest you?",
      suggestions: [
        "Funding opportunities",
        "Partnership opportunities",
        "Learning opportunities",
        "Career opportunities"
      ]
    },

    ai_search: {
      message: "I'm your AI search assistant! I can help you find content using natural language. Just describe what you're looking for and I'll search through posts, profiles, events, and more using intelligent text analysis.",
      suggestions: [
        "Find posts about fintech startups",
        "Search for mentors in agriculture",
        "Look for funding opportunities",
        "Find events in my area"
      ]
    },
    
    // Getting started
    get_started: {
      message: "Welcome to ZbInnovation! I'm here to help you get started and make the most of the platform. What would you like to do first?",
      suggestions: [
        "Set up my profile",
        "Explore the platform",
        "Find my first connections",
        "Learn about features"
      ]
    },

    // Learn more about platform
    learn_more_platform: {
      message: "I'd be happy to help you learn more about the ZbInnovation platform! We're an innovation ecosystem connecting innovators, investors, mentors, industry experts, academic students, institutions, and government. What would you like to know?",
      suggestions: [
        "How does the platform work?",
        "What can I do here?",
        "Who can I connect with?",
        "What are the main features?",
        "How do I get started?",
        "Tell me about the community"
      ]
    },

    // Investor-specific triggers
    connect_innovators: {
      message: "I'll help you connect with innovators that match your investment profile and criteria. Let me analyze your preferences and find the best matches.",
      suggestions: [
        "Show me top-rated innovators",
        "Find innovators in my focus areas",
        "Connect with early-stage startups",
        "Find innovators seeking my investment range"
      ]
    },

    discover_projects: {
      message: "I'll help you discover innovative projects seeking investment that align with your portfolio and investment criteria.",
      suggestions: [
        "Show me projects in my investment range",
        "Find projects in my focus industries",
        "Discover early-stage opportunities",
        "Find projects with strong teams"
      ]
    },

    investment_opportunities: {
      message: "Let me help you discover investment opportunities that match your investment thesis and portfolio strategy.",
      suggestions: [
        "Find opportunities in my sectors",
        "Show me deals in my investment range",
        "Discover emerging market opportunities",
        "Find co-investment opportunities"
      ]
    },

    // Mentor-specific triggers
    mentorship_opportunities: {
      message: "I'll help you find mentorship opportunities with innovators and entrepreneurs who could benefit from your expertise.",
      suggestions: [
        "Find mentees in my expertise areas",
        "Connect with early-stage entrepreneurs",
        "Discover skill-building opportunities",
        "Find long-term mentorship matches"
      ]
    },

    find_mentees: {
      message: "Let me help you find entrepreneurs and innovators who are seeking mentorship in your areas of expertise.",
      suggestions: [
        "Find entrepreneurs in my industry",
        "Connect with early-stage founders",
        "Find mentees seeking specific skills",
        "Discover promising startups to mentor"
      ]
    },

    // Content creation triggers
    content_ideas: {
      message: "I can help you generate creative content ideas for your posts! Let me suggest topics that would resonate with your audience and align with your expertise.",
      suggestions: [
        "Generate post ideas for my industry",
        "Suggest trending topics to write about",
        "Help me brainstorm innovative content",
        "Find content gaps I could fill"
      ]
    },

    writing_assistance: {
      message: "I'm here to help you with writing! Whether you need help structuring your content, improving your writing style, or making your message more engaging, I can assist you.",
      suggestions: [
        "Help me structure my post",
        "Improve my writing style",
        "Make my content more engaging",
        "Help with grammar and clarity"
      ]
    }
  }

  /**
   * Send a direct message to AI chat
   */
  async sendMessage(message: string): Promise<void> {
    try {
      console.log('💬 Sending direct message to AI:', message.substring(0, 100) + '...')

      // Import store inside method
      const { useAIChatStore } = await import('@/stores/aiChat')
      const aiChatStore = useAIChatStore()

      // Open AI chat if not already open
      aiChatStore.openChat()

      // Send the message through the AI chat store
      await aiChatStore.sendAIMessage(message)

      // Scroll to bottom to show the new message
      setTimeout(() => {
        const event = new CustomEvent('ai-trigger-scroll')
        window.dispatchEvent(event)
      }, 100)

      console.log('✅ Direct message sent successfully')
    } catch (error) {
      console.error('❌ Error sending direct message:', error)
      throw error
    }
  }

  /**
   * Trigger AI chat with contextual message and automatic user message sending
   */
  async triggerChat(triggerKey: string, context?: string): Promise<void> {
    try {
      console.log('🎯 Triggering AI chat:', { triggerKey, context })

      // Import stores inside method (following the pattern from aiChatService.ts)
      const { useAIChatStore } = await import('@/stores/aiChat')
      const { useAuthStore } = await import('@/stores/auth')
      const aiChatStore = useAIChatStore()
      const authStore = useAuthStore()

      // Check if trigger requires authentication
      const requiresAuth = this.triggerRequiresAuth(triggerKey)

      if (requiresAuth && !authStore.isAuthenticated) {
        console.log('🔒 Trigger requires authentication, showing auth prompt')

        // Open AI chat with authentication prompt
        aiChatStore.openChat()

        // Add authentication prompt message
        aiChatStore.addMessage({
          role: 'assistant',
          content: `To access this feature, you'll need to sign in to your account. Once you're signed in, I'll be able to provide personalized assistance and access all platform features.`,
          action_buttons: [
            {
              id: 'auth-signin',
              label: 'Sign In',
              icon: 'login',
              color: 'primary',
              action_type: 'dialog',
              action_data: { dialog: 'auth-signin' },
              tooltip: 'Sign in to access all features'
            },
            {
              id: 'auth-signup',
              label: 'Create Account',
              icon: 'person_add',
              color: 'secondary',
              action_type: 'dialog',
              action_data: { dialog: 'auth-signup' },
              tooltip: 'Create your account to get started'
            }
          ]
        })

        // Scroll to show the message
        setTimeout(() => {
          const event = new CustomEvent('ai-trigger-scroll')
          window.dispatchEvent(event)
        }, 100)

        return
      }

      // Get trigger configuration
      const config = this.getTriggerConfig(triggerKey, context)

      // Open AI chat
      aiChatStore.openChat()

      // Add contextual message if chat is empty
      if (aiChatStore.messages.length === 0) {
        // Generate context-aware action buttons
        const actionButtons = await this.generateActionButtons(triggerKey, context)

        aiChatStore.addMessage({
          role: 'assistant',
          content: config.message,
          action_buttons: actionButtons
        })
      }

      // Automatically send contextual user message
      await this.sendAutomaticUserMessage(triggerKey, context)

      // Scroll to bottom to show the new message
      setTimeout(() => {
        const event = new CustomEvent('ai-trigger-scroll')
        window.dispatchEvent(event)
      }, 100)

      // Add a safety timeout to ensure loading state is cleared
      setTimeout(async () => {
        try {
          const { useAIChatStore } = await import('@/stores/aiChat')
          const aiChatStore = useAIChatStore()
          if (aiChatStore.isLoading) {
            console.log('🔧 Safety timeout: clearing stuck loading state')
            aiChatStore.setLoading(false)
          }
        } catch (error) {
          console.error('❌ Error in safety timeout:', error)
        }
      }, 35000) // 35 second safety timeout

      console.log('✅ AI chat triggered successfully')
    } catch (error) {
      console.error('❌ Error triggering AI chat:', error)

      // Ensure loading state is cleared if there was an error
      try {
        const { useAIChatStore } = await import('@/stores/aiChat')
        const aiChatStore = useAIChatStore()
        aiChatStore.setLoading(false)
      } catch (storeError) {
        console.error('❌ Error clearing loading state after trigger error:', storeError)
      }

      throw error
    }
  }

  /**
   * Send automatic contextual user message based on trigger
   */
  private async sendAutomaticUserMessage(triggerKey: string, context?: string): Promise<void> {
    try {
      // Import stores and services
      const { useAIChatStore } = await import('@/stores/aiChat')
      const { useAuthStore } = await import('@/stores/auth')
      const aiChatStore = useAIChatStore()
      const authStore = useAuthStore()

      // Generate contextual user message
      const userMessage = this.generateContextualUserMessage(triggerKey, context, authStore.profile)

      if (userMessage && userMessage.trim()) {
        console.log('🤖 Sending automatic user message:', userMessage.substring(0, 100) + '...')

        // Send the message through the AI chat store with timeout protection
        const messagePromise = aiChatStore.sendAIMessage(userMessage)

        // Add a timeout to ensure loading state doesn't get stuck
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('AI message timeout')), 30000) // 30 second timeout
        })

        try {
          await Promise.race([messagePromise, timeoutPromise])
        } catch (timeoutError) {
          console.error('⏰ AI message timed out, clearing loading state')
          aiChatStore.setLoading(false)
          throw timeoutError
        }
      } else {
        console.log('🔍 No automatic message to send for trigger:', triggerKey)
      }
    } catch (error) {
      console.error('❌ Error sending automatic user message:', error)

      // Ensure loading state is cleared if there was an error
      try {
        const { useAIChatStore } = await import('@/stores/aiChat')
        const aiChatStore = useAIChatStore()
        aiChatStore.setLoading(false)
      } catch (storeError) {
        console.error('❌ Error clearing loading state:', storeError)
      }

      // Don't throw error to avoid breaking the trigger flow
    }
  }

  /**
   * Generate contextual user message based on trigger and user profile
   */
  private generateContextualUserMessage(triggerKey: string, context?: string, profile?: any): string | null {
    const profileType = profile?.profile_type || 'user'
    const profileCompletion = profile?.profile_completion || 0
    const userIndustries = profile?.industries || []
    const userSkills = profile?.skills || []

    switch (triggerKey) {
      case 'connect_innovators':
        if (profileType === 'investor') {
          return `I would like to connect with innovators that fit my investment profile and criteria. I'm particularly interested in ${userIndustries.length > 0 ? userIndustries.join(', ') : 'various industries'} and looking for entrepreneurs who are seeking investment. Please analyze my profile and recommend the most compatible innovators with high potential for successful partnerships.`
        }
        return `I want to connect with innovators in my field. Please help me find relevant people to network with.`

      case 'discover_projects':
        if (profileType === 'investor') {
          return `I'm looking to discover innovative projects seeking investment that align with my portfolio strategy. My focus areas include ${userIndustries.length > 0 ? userIndustries.join(', ') : 'emerging technologies'} and I'm interested in projects that match my investment criteria. Please analyze available projects and provide personalized recommendations with compatibility scores.`
        }
        return `I want to discover interesting projects and opportunities on the platform. Please show me relevant projects.`

      case 'investment_opportunities':
        return `I'm seeking investment opportunities that match my investment thesis and portfolio strategy. Please analyze my investor profile and recommend opportunities with high potential returns and good alignment with my investment criteria. Include details about the opportunities and why they're good matches for my profile.`

      case 'mentorship_opportunities':
        if (profileType === 'mentor') {
          return `I'm looking for mentorship opportunities where I can share my expertise in ${userSkills.length > 0 ? userSkills.join(', ') : 'my field'}. Please find entrepreneurs and innovators who could benefit from my experience and knowledge. I'm particularly interested in helping with ${userIndustries.length > 0 ? userIndustries.join(', ') : 'various industries'}.`
        }
        return `I'm interested in mentorship opportunities. Please help me find relevant mentoring connections.`

      case 'find_mentees':
        return `I want to find entrepreneurs and innovators who are seeking mentorship in my areas of expertise: ${userSkills.length > 0 ? userSkills.join(', ') : 'my field'}. Please analyze my profile and recommend potential mentees who would benefit most from my guidance and experience.`

      case 'collaboration_opportunities':
        return `I'm seeking collaboration opportunities that match my skills and interests. My expertise includes ${userSkills.length > 0 ? userSkills.join(', ') : 'various areas'} and I'm interested in working on projects in ${userIndustries.length > 0 ? userIndustries.join(', ') : 'my field'}. Please find relevant collaboration opportunities and potential partners.`

      case 'find_connections':
      case 'networking':
        return `I want to expand my professional network and find relevant connections in the innovation ecosystem. Based on my profile (${profileType}) and interests in ${userIndustries.length > 0 ? userIndustries.join(', ') : 'various fields'}, please recommend people I should connect with and explain why they would be valuable connections.`

      case 'discover_opportunities':
        return `I'm looking to discover opportunities in the innovation ecosystem that align with my profile and goals. As a ${profileType}, I'm interested in ${userIndustries.length > 0 ? userIndustries.join(', ') : 'various opportunities'}. Please analyze available opportunities and provide personalized recommendations.`

      case 'ai_search':
        return `I want to use AI-powered search to find specific content on the platform. Please help me search through posts, profiles, events, and other content using natural language queries and intelligent text analysis.`

      // Time-based filters
      case 'recent_posts':
      case 'recent_content':
        return `Show me recent posts and content from the last few days. I want to stay updated with the latest discussions and developments in the community.`

      case 'trending_posts':
      case 'trending_content':
        return `Find trending and popular content that's getting a lot of engagement. Show me what's hot in the community right now.`

      case 'featured_posts':
        return `Show me featured and highlighted posts that have been specially curated or marked as important by the community.`

      // Category-based filters
      case 'innovation_content':
      case 'innovation_search':
        return `Find content related to innovation, startups, and entrepreneurship. I'm interested in innovative ideas, breakthrough technologies, and startup stories.`

      case 'collaboration_content':
      case 'collaboration_search':
        return `Find collaboration opportunities and partnership possibilities. I'm looking for ways to work together with others in the community.`

      case 'funding_content':
      case 'funding_search':
        return `Find content about funding, investment opportunities, and financial resources for startups and businesses.`

      case 'technology_content':
      case 'technology_search':
        return `Find technology-related content, including tech trends, tools, and technical discussions relevant to innovation.`

      // Profile-specific filters
      case 'find_mentors':
        return `Help me find experienced mentors in my field who can provide guidance and support for my professional development.`

      case 'find_investors':
        return `Find investors and funding partners who might be interested in supporting innovative projects and startups.`

      case 'find_entrepreneurs':
        return `Connect me with fellow entrepreneurs and founders who are building innovative companies and solutions.`

      case 'find_researchers':
        return `Find researchers and academics working on cutting-edge projects and innovations in relevant fields.`

      // Event-specific filters
      case 'find_workshops':
        return `Find hands-on workshops and training sessions where I can learn new skills and techniques.`

      case 'find_conferences':
        return `Find conferences, summits, and large networking events in my areas of interest.`

      case 'find_networking_events':
        return `Find networking events and meetups where I can connect with like-minded professionals.`

      case 'find_competitions':
        return `Find competitions, challenges, and contests where I can showcase my skills and innovations.`

      // Blog-specific filters
      case 'find_insights':
        return `Find industry insights, analysis, and thought leadership articles that can help me understand market trends.`

      case 'find_trends':
        return `Find articles about trending topics, emerging technologies, and market analysis in my field.`

      case 'find_success_stories':
        return `Find success stories, case studies, and examples of successful innovations and businesses.`

      case 'find_research':
        return `Find research articles, academic papers, and scientific studies relevant to innovation and technology.`

      // Marketplace-specific filters
      case 'find_products':
        return `Find products, tools, and physical items that could be useful for my work or projects.`

      case 'find_services':
        return `Find professional services and offerings that could help with my business or project needs.`

      case 'find_jobs':
        return `Find job opportunities, positions, and career openings in innovation and technology fields.`

      // Group-specific filters
      case 'find_fintech_groups':
        return `Find groups focused on financial technology, fintech innovations, and financial services.`

      case 'find_agritech_groups':
        return `Find groups working on agricultural technology, sustainable farming, and food innovation.`

      case 'find_healthtech_groups':
        return `Find groups focused on health technology, medical innovations, and healthcare solutions.`

      case 'find_edtech_groups':
        return `Find groups working on education technology, learning innovations, and educational tools.`

      case 'content_discovery':
      case 'discover_content':
        return `I want to discover relevant content, discussions, and insights that match my interests and professional focus. Based on my profile in ${userIndustries.length > 0 ? userIndustries.join(', ') : 'my field'}, please recommend content that would be valuable for my professional development and industry knowledge.`

      // Content creation triggers
      case 'content_ideas':
        return `I need help generating content ideas for my posts. Based on my profile as a ${profileType} in ${userIndustries.length > 0 ? userIndustries.join(', ') : 'my field'}, please suggest engaging topics that would be valuable for my audience. I'm looking for ideas that showcase my expertise and provide value to the community.`

      case 'writing_assistance':
        return `I need writing assistance for creating a post. Please help me structure my content effectively, improve my writing style, and make my message more engaging and clear. I want to ensure my post resonates with my audience and communicates my ideas effectively.`

      default:
        // For general triggers, don't send automatic message
        return null
    }
  }

  /**
   * Get trigger configuration for a specific key and context
   */
  private getTriggerConfig(triggerKey: string, context?: string): TriggerConfig {
    // Try to get specific configuration
    let config = this.triggerConfigs[triggerKey]
    
    if (!config) {
      // Fallback to general assistance
      config = this.triggerConfigs.general_assistance
    }
    
    // Customize message based on context if needed
    if (context) {
      config = {
        ...config,
        message: this.customizeMessageForContext(config.message, context)
      }
    }
    
    return config
  }

  /**
   * Customize message based on context
   */
  private customizeMessageForContext(message: string, context: string): string {
    // Add context-specific customization if needed
    if (context.includes('dashboard')) {
      return `From your dashboard: ${message}`
    }
    
    if (context.includes('community')) {
      return `In the community section: ${message}`
    }
    
    if (context.includes('profile')) {
      return `For your profile: ${message}`
    }
    
    return message
  }

  /**
   * Generate context-aware action buttons for triggers
   */
  private async generateActionButtons(triggerKey: string, context?: string): Promise<any[]> {
    try {
      // Import action service dynamically to avoid circular dependencies
      const { useAiActionService } = await import('./aiActionService')
      const actionService = useAiActionService()

      // Import auth store to get user context
      const { useAuthStore } = await import('@/stores/auth')
      const authStore = useAuthStore()

      const currentPage = this.getCurrentPage()
      const userContext = {
        is_authenticated: authStore.isAuthenticated,
        user_id: authStore.user?.id,
        profile_type: authStore.profile?.profile_type,
        profile_completion: authStore.profile?.profile_completion || 0,
        current_page: currentPage
      }

      // Generate contextual actions based on trigger and user state
      const contextualActions = actionService.generateContextualActions(userContext, currentPage)

      // Add trigger-specific actions
      const triggerSpecificActions = this.getTriggerSpecificActions(triggerKey, userContext)

      return [...contextualActions, ...triggerSpecificActions]
    } catch (error) {
      console.error('❌ Error generating action buttons:', error)
      return []
    }
  }

  /**
   * Get trigger-specific action buttons
   */
  private getTriggerSpecificActions(triggerKey: string, userContext: any): any[] {
    const actions: any[] = []

    switch (triggerKey) {
      case 'complete_profile':
        actions.push({
          id: 'go-dashboard-profile',
          label: 'Go to Dashboard',
          icon: 'dashboard',
          color: 'primary',
          action_type: 'navigation',
          action_data: { route: '/dashboard', params: { section: 'profile' } },
          tooltip: 'Complete your profile on the dashboard',
          requires_auth: true
        })
        break

      case 'find_connections':
        actions.push({
          id: 'browse-profiles',
          label: 'Browse Profiles',
          icon: 'people',
          color: 'secondary',
          action_type: 'navigation',
          action_data: { route: '/innovation-community', params: { tab: 'profiles' } },
          tooltip: 'Browse and connect with other innovators'
        })
        break

      case 'discover_content':
        actions.push({
          id: 'explore-feed',
          label: 'Explore Feed',
          icon: 'explore',
          color: 'blue',
          action_type: 'navigation',
          action_data: { route: '/innovation-community', params: { tab: 'feed' } },
          tooltip: 'Discover content in the community feed'
        })
        break

      case 'collaboration_opportunities':
        actions.push({
          id: 'find-collaborations',
          label: 'Find Projects',
          icon: 'handshake',
          color: 'teal',
          action_type: 'navigation',
          action_data: { route: '/innovation-community', params: { tab: 'marketplace' } },
          tooltip: 'Find collaboration opportunities'
        })
        break
    }

    return actions
  }

  /**
   * Check if a trigger requires authentication
   */
  private triggerRequiresAuth(triggerKey: string): boolean {
    // Triggers that require authentication
    const authRequiredTriggers = [
      'complete_profile',
      'optimize_profile',
      'find_connections',
      'connect_innovators',
      'discover_projects',
      'investment_opportunities',
      'mentorship_opportunities',
      'find_mentees',
      'collaboration_opportunities',
      'networking',
      'discover_opportunities',
      'ai_search',
      'content_discovery'
    ]

    return authRequiredTriggers.includes(triggerKey)
  }

  /**
   * Get current page from URL
   */
  private getCurrentPage(): string {
    const path = window.location.pathname
    if (path === '/') return 'home'
    if (path.includes('/dashboard')) return 'dashboard'
    if (path.includes('/virtual-community')) return 'virtual-community'
    if (path.includes('/about')) return 'about'
    return 'unknown'
  }

  /**
   * Get available triggers for a specific profile type
   */
  getTriggersForProfile(profileType: string): string[] {
    // Return relevant trigger keys based on profile type
    const baseTriggers = ['general_assistance', 'find_connections', 'discover_content']

    switch (profileType) {
      case 'innovator':
        return [...baseTriggers, 'collaboration_opportunities', 'discover_opportunities']
      case 'investor':
        return [...baseTriggers, 'discover_projects', 'investment_opportunities']
      case 'mentor':
        return [...baseTriggers, 'mentorship_opportunities', 'find_mentees']
      default:
        return baseTriggers
    }
  }
}

// Create singleton instance
let _aiChatTriggerService: AIChatTriggerService | null = null

export const useAiChatTriggerService = (): AIChatTriggerService => {
  if (!_aiChatTriggerService) {
    _aiChatTriggerService = new AIChatTriggerService()
  }
  return _aiChatTriggerService
}

// Default export for compatibility
export default useAiChatTriggerService()
