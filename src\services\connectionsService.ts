import { useConnectionService } from './connectionService'

/**
 * Connections Service Wrapper
 * 
 * This service provides a simplified interface for connection-related operations
 * used by the virtual community layout components.
 */
export function useConnectionsService() {
  const connectionService = useConnectionService()

  /**
   * Send a connection request to another user
   */
  async function sendConnectionRequest(userId: string): Promise<boolean> {
    try {
      return await connectionService.connectWithUser(userId, 'connect')
    } catch (error) {
      console.error('Failed to send connection request:', error)
      return false
    }
  }

  /**
   * Get suggested connections for the current user
   */
  async function getSuggestedConnections(): Promise<any[]> {
    try {
      // This would typically fetch suggested connections based on user profile,
      // mutual connections, interests, etc. For now, return empty array
      // as the actual implementation would require more complex logic
      return []
    } catch (error) {
      console.error('Failed to get suggested connections:', error)
      return []
    }
  }

  /**
   * Get user connections
   */
  async function getUserConnections(
    userId?: string,
    status: string = 'accepted',
    limit: number = 10,
    page: number = 1
  ): Promise<any[]> {
    try {
      return await connectionService.getUserConnections(userId, status, limit, page)
    } catch (error) {
      console.error('Failed to get user connections:', error)
      return []
    }
  }

  /**
   * Get connection requests
   */
  async function getConnectionRequests(
    limit: number = 10,
    page: number = 1
  ): Promise<any[]> {
    try {
      return await connectionService.getConnectionRequests(limit, page)
    } catch (error) {
      console.error('Failed to get connection requests:', error)
      return []
    }
  }

  return {
    sendConnectionRequest,
    getSuggestedConnections,
    getUserConnections,
    getConnectionRequests
  }
}
