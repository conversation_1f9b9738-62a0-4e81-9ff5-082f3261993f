/**
 * Contact Service
 * 
 * Centralized service for handling contact form submissions and newsletter subscriptions.
 * Replaces direct Supabase calls in components.
 */

import { supabase } from '@/lib/supabase'

export interface ContactFormData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  subject: string
  message: string
  subscribe?: boolean
}

export interface NewsletterSubscription {
  email: string
  source?: string
  subscribed_at?: string
}

export interface ContactSubmissionResponse {
  success: boolean
  data?: any
  error?: string
}

class ContactService {
  /**
   * Submit contact form data
   */
  async submitContactForm(formData: ContactFormData): Promise<ContactSubmissionResponse> {
    try {
      const { data, error } = await supabase
        .from('contact_form_submissions')
        .insert([
          {
            first_name: formData.firstName,
            last_name: formData.lastName,
            email: formData.email,
            phone: formData.phone || null,
            subject: formData.subject,
            message: formData.message,
            subscribe: formData.subscribe || false,
            submitted_at: new Date().toISOString()
          }
        ])
        .select()

      if (error) {
        console.error('Contact form submission error:', error)
        return {
          success: false,
          error: error.message || 'Failed to submit contact form'
        }
      }

      return {
        success: true,
        data: data?.[0]
      }
    } catch (error: any) {
      console.error('Contact form submission error:', error)
      return {
        success: false,
        error: error.message || 'An unexpected error occurred'
      }
    }
  }

  /**
   * Subscribe to newsletter
   */
  async subscribeToNewsletter(email: string, source = 'contact_page'): Promise<ContactSubmissionResponse> {
    try {
      // Check if email already exists
      const { data: existingSubscription, error: checkError } = await supabase
        .from('newsletter_subscriptions')
        .select('id, email')
        .eq('email', email)
        .single()

      if (checkError && checkError.code !== 'PGRST116') {
        // PGRST116 is "not found" error, which is expected for new subscriptions
        console.error('Newsletter subscription check error:', checkError)
        return {
          success: false,
          error: 'Failed to check subscription status'
        }
      }

      if (existingSubscription) {
        return {
          success: true,
          data: existingSubscription,
          error: 'Email is already subscribed to our newsletter'
        }
      }

      // Create new subscription
      const { data, error } = await supabase
        .from('newsletter_subscriptions')
        .insert([
          {
            email,
            source,
            subscribed_at: new Date().toISOString(),
            is_active: true
          }
        ])
        .select()

      if (error) {
        console.error('Newsletter subscription error:', error)
        return {
          success: false,
          error: error.message || 'Failed to subscribe to newsletter'
        }
      }

      return {
        success: true,
        data: data?.[0]
      }
    } catch (error: any) {
      console.error('Newsletter subscription error:', error)
      return {
        success: false,
        error: error.message || 'An unexpected error occurred'
      }
    }
  }

  /**
   * Get contact form submissions (admin only)
   */
  async getContactSubmissions(limit = 50, offset = 0): Promise<ContactSubmissionResponse> {
    try {
      const { data, error } = await supabase
        .from('contact_form_submissions')
        .select(`
          id,
          first_name,
          last_name,
          email,
          phone,
          subject,
          message,
          subscribe,
          submitted_at,
          created_at
        `)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      if (error) {
        console.error('Error fetching contact submissions:', error)
        return {
          success: false,
          error: error.message || 'Failed to fetch contact submissions'
        }
      }

      return {
        success: true,
        data
      }
    } catch (error: any) {
      console.error('Error fetching contact submissions:', error)
      return {
        success: false,
        error: error.message || 'An unexpected error occurred'
      }
    }
  }

  /**
   * Get newsletter subscriptions (admin only)
   */
  async getNewsletterSubscriptions(limit = 50, offset = 0): Promise<ContactSubmissionResponse> {
    try {
      const { data, error } = await supabase
        .from('newsletter_subscriptions')
        .select(`
          id,
          email,
          source,
          subscribed_at,
          is_active,
          created_at
        `)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      if (error) {
        console.error('Error fetching newsletter subscriptions:', error)
        return {
          success: false,
          error: error.message || 'Failed to fetch newsletter subscriptions'
        }
      }

      return {
        success: true,
        data
      }
    } catch (error: any) {
      console.error('Error fetching newsletter subscriptions:', error)
      return {
        success: false,
        error: error.message || 'An unexpected error occurred'
      }
    }
  }

  /**
   * Unsubscribe from newsletter
   */
  async unsubscribeFromNewsletter(email: string): Promise<ContactSubmissionResponse> {
    try {
      const { data, error } = await supabase
        .from('newsletter_subscriptions')
        .update({ 
          is_active: false,
          unsubscribed_at: new Date().toISOString()
        })
        .eq('email', email)
        .select()

      if (error) {
        console.error('Newsletter unsubscribe error:', error)
        return {
          success: false,
          error: error.message || 'Failed to unsubscribe from newsletter'
        }
      }

      return {
        success: true,
        data: data?.[0]
      }
    } catch (error: any) {
      console.error('Newsletter unsubscribe error:', error)
      return {
        success: false,
        error: error.message || 'An unexpected error occurred'
      }
    }
  }
}

// Export singleton instance
export const contactService = new ContactService()

// Export composable for Vue components
export function useContactService() {
  return contactService
}
