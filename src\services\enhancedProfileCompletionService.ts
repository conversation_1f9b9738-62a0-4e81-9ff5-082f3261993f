// Enhanced Profile Completion Service
// Provides sophisticated profile completion calculation with field importance weighting

import { BaseProfile } from '../types/profile'

// Field importance levels
export enum FieldImportance {
  CRITICAL = 'critical',     // Essential for basic functionality (weight: 1.0)
  HIGH = 'high',            // Important for good user experience (weight: 0.8)
  MEDIUM = 'medium',        // Useful for better matching (weight: 0.6)
  LOW = 'low',             // Nice to have (weight: 0.4)
  OPTIONAL = 'optional'     // Completely optional (weight: 0.2)
}

// Field categories for better organization
export enum FieldCategory {
  IDENTITY = 'identity',           // Basic identity information
  CONTACT = 'contact',            // Contact information
  PROFESSIONAL = 'professional',  // Professional/business information
  GOALS = 'goals',               // Goals and aspirations
  SOCIAL = 'social',             // Social media and networking
  ASSETS = 'assets',             // File uploads and media
  PREFERENCES = 'preferences'     // User preferences and settings
}

// Field configuration interface
interface FieldConfig {
  importance: FieldImportance
  category: FieldCategory
  weight: number
  description: string
  userGuidance: string
}

// Profile type specific field configurations
const FIELD_CONFIGURATIONS: Record<string, Record<string, FieldConfig>> = {
  innovator: {
    // Critical fields (weight: 1.0)
    first_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.IDENTITY,
      weight: 1.0,
      description: 'Your first name',
      userGuidance: 'Required for basic profile identification'
    },
    last_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.IDENTITY,
      weight: 1.0,
      description: 'Your last name',
      userGuidance: 'Required for basic profile identification'
    },
    email: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.CONTACT,
      weight: 1.0,
      description: 'Your email address',
      userGuidance: 'Required for account access and communication'
    },
    innovation_area: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.PROFESSIONAL,
      weight: 1.0,
      description: 'Your innovation focus area',
      userGuidance: 'Essential for connecting with relevant investors and mentors'
    },
    innovation_stage: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.PROFESSIONAL,
      weight: 1.0,
      description: 'Current stage of your innovation',
      userGuidance: 'Critical for matching with appropriate funding sources'
    },
    
    // High importance fields (weight: 0.8)
    bio: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.IDENTITY,
      weight: 0.8,
      description: 'Your professional bio',
      userGuidance: 'Helps others understand your background and expertise'
    },
    innovation_description: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.PROFESSIONAL,
      weight: 0.8,
      description: 'Description of your innovation',
      userGuidance: 'Key for attracting interest from investors and partners'
    },
    target_market: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.PROFESSIONAL,
      weight: 0.8,
      description: 'Your target market',
      userGuidance: 'Important for market validation and investor interest'
    },
    funding_needs: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.PROFESSIONAL,
      weight: 0.8,
      description: 'Whether you need funding',
      userGuidance: 'Essential for connecting with investors'
    },
    
    // Medium importance fields (weight: 0.6)
    contact_phone: {
      importance: FieldImportance.MEDIUM,
      category: FieldCategory.CONTACT,
      weight: 0.6,
      description: 'Your phone number',
      userGuidance: 'Useful for direct communication with partners'
    },
    city: {
      importance: FieldImportance.MEDIUM,
      category: FieldCategory.CONTACT,
      weight: 0.6,
      description: 'Your city',
      userGuidance: 'Helps with local networking and partnerships'
    },
    country: {
      importance: FieldImportance.MEDIUM,
      category: FieldCategory.CONTACT,
      weight: 0.6,
      description: 'Your country',
      userGuidance: 'Important for regional matching and compliance'
    },
    short_term_goals: {
      importance: FieldImportance.MEDIUM,
      category: FieldCategory.GOALS,
      weight: 0.6,
      description: 'Your short-term goals',
      userGuidance: 'Helps match with mentors and advisors'
    },
    challenges: {
      importance: FieldImportance.MEDIUM,
      category: FieldCategory.GOALS,
      weight: 0.6,
      description: 'Current challenges you face',
      userGuidance: 'Essential for finding relevant help and mentorship'
    },
    
    // Low importance fields (weight: 0.4)
    linkedin: {
      importance: FieldImportance.LOW,
      category: FieldCategory.SOCIAL,
      weight: 0.4,
      description: 'Your LinkedIn profile',
      userGuidance: 'Adds credibility and professional networking'
    },
    website: {
      importance: FieldImportance.LOW,
      category: FieldCategory.SOCIAL,
      weight: 0.4,
      description: 'Your website or portfolio',
      userGuidance: 'Showcases your work and builds credibility'
    },
    company_logo: {
      importance: FieldImportance.LOW,
      category: FieldCategory.ASSETS,
      weight: 0.4,
      description: 'Your company logo',
      userGuidance: 'Enhances professional appearance'
    },
    
    // Optional fields (weight: 0.2)
    twitter: {
      importance: FieldImportance.OPTIONAL,
      category: FieldCategory.SOCIAL,
      weight: 0.2,
      description: 'Your Twitter profile',
      userGuidance: 'Additional social media presence'
    },
    facebook: {
      importance: FieldImportance.OPTIONAL,
      category: FieldCategory.SOCIAL,
      weight: 0.2,
      description: 'Your Facebook profile',
      userGuidance: 'Additional social media presence'
    },
    cover_image: {
      importance: FieldImportance.OPTIONAL,
      category: FieldCategory.ASSETS,
      weight: 0.2,
      description: 'Profile cover image',
      userGuidance: 'Personalizes your profile appearance'
    }
  },
  
  investor: {
    // Critical fields
    first_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.IDENTITY,
      weight: 1.0,
      description: 'Your first name',
      userGuidance: 'Required for basic profile identification'
    },
    last_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.IDENTITY,
      weight: 1.0,
      description: 'Your last name',
      userGuidance: 'Required for basic profile identification'
    },
    email: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.CONTACT,
      weight: 1.0,
      description: 'Your email address',
      userGuidance: 'Required for account access and communication'
    },
    investment_focus: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.PROFESSIONAL,
      weight: 1.0,
      description: 'Your investment focus areas',
      userGuidance: 'Essential for matching with relevant startups'
    },
    investment_stage: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.PROFESSIONAL,
      weight: 1.0,
      description: 'Investment stages you target',
      userGuidance: 'Critical for startup-investor matching'
    },
    ticket_size: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.PROFESSIONAL,
      weight: 1.0,
      description: 'Your typical investment range',
      userGuidance: 'Essential for funding amount matching'
    },
    
    // High importance fields
    bio: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.IDENTITY,
      weight: 0.8,
      description: 'Your professional bio',
      userGuidance: 'Builds trust with entrepreneurs'
    },
    investment_criteria: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.PROFESSIONAL,
      weight: 0.8,
      description: 'Your investment criteria',
      userGuidance: 'Helps startups understand your requirements'
    },
    previous_investments: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.PROFESSIONAL,
      weight: 0.8,
      description: 'Your previous investments',
      userGuidance: 'Demonstrates experience and track record'
    },

    // Medium importance fields
    contact_phone: {
      importance: FieldImportance.MEDIUM,
      category: FieldCategory.CONTACT,
      weight: 0.6,
      description: 'Your phone number',
      userGuidance: 'Enables direct communication with entrepreneurs'
    },
    city: {
      importance: FieldImportance.MEDIUM,
      category: FieldCategory.CONTACT,
      weight: 0.6,
      description: 'Your city',
      userGuidance: 'Helps with local deal flow and networking'
    },
    country: {
      importance: FieldImportance.MEDIUM,
      category: FieldCategory.CONTACT,
      weight: 0.6,
      description: 'Your country',
      userGuidance: 'Important for regulatory and geographic matching'
    },

    // Low importance fields
    linkedin: {
      importance: FieldImportance.LOW,
      category: FieldCategory.SOCIAL,
      weight: 0.4,
      description: 'Your LinkedIn profile',
      userGuidance: 'Builds credibility with entrepreneurs'
    },
    company_logo: {
      importance: FieldImportance.LOW,
      category: FieldCategory.ASSETS,
      weight: 0.4,
      description: 'Your firm logo',
      userGuidance: 'Enhances professional appearance'
    },

    // Optional fields
    twitter: {
      importance: FieldImportance.OPTIONAL,
      category: FieldCategory.SOCIAL,
      weight: 0.2,
      description: 'Your Twitter profile',
      userGuidance: 'Additional social media presence'
    }
  },

  mentor: {
    // Critical fields
    first_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.IDENTITY,
      weight: 1.0,
      description: 'Your first name',
      userGuidance: 'Required for basic profile identification'
    },
    last_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.IDENTITY,
      weight: 1.0,
      description: 'Your last name',
      userGuidance: 'Required for basic profile identification'
    },
    email: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.CONTACT,
      weight: 1.0,
      description: 'Your email address',
      userGuidance: 'Required for account access and communication'
    },
    expertise_areas: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.PROFESSIONAL,
      weight: 1.0,
      description: 'Your areas of expertise',
      userGuidance: 'Essential for matching with relevant mentees'
    },

    // High importance fields
    bio: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.IDENTITY,
      weight: 0.8,
      description: 'Your professional bio',
      userGuidance: 'Helps mentees understand your background'
    },
    mentoring_experience: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.PROFESSIONAL,
      weight: 0.8,
      description: 'Your mentoring experience',
      userGuidance: 'Demonstrates your mentoring track record'
    }
  },

  professional: {
    // Critical fields
    first_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.IDENTITY,
      weight: 1.0,
      description: 'Your first name',
      userGuidance: 'Required for basic profile identification'
    },
    last_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.IDENTITY,
      weight: 1.0,
      description: 'Your last name',
      userGuidance: 'Required for basic profile identification'
    },
    email: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.CONTACT,
      weight: 1.0,
      description: 'Your email address',
      userGuidance: 'Required for account access and communication'
    },
    industry: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.PROFESSIONAL,
      weight: 1.0,
      description: 'Your industry',
      userGuidance: 'Essential for professional matching and networking'
    },
    job_title: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.PROFESSIONAL,
      weight: 1.0,
      description: 'Your job title',
      userGuidance: 'Critical for understanding your professional role'
    },

    // High importance fields
    bio: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.IDENTITY,
      weight: 0.8,
      description: 'Your professional bio',
      userGuidance: 'Helps others understand your background and expertise'
    },
    company: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.PROFESSIONAL,
      weight: 0.8,
      description: 'Your company',
      userGuidance: 'Important for professional credibility and networking'
    },
    years_of_experience: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.PROFESSIONAL,
      weight: 0.8,
      description: 'Years of experience',
      userGuidance: 'Helps others gauge your expertise level'
    }
  },

  industry_expert: {
    // Critical fields
    first_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.IDENTITY,
      weight: 1.0,
      description: 'Your first name',
      userGuidance: 'Required for basic profile identification'
    },
    last_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.IDENTITY,
      weight: 1.0,
      description: 'Your last name',
      userGuidance: 'Required for basic profile identification'
    },
    email: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.CONTACT,
      weight: 1.0,
      description: 'Your email address',
      userGuidance: 'Required for account access and communication'
    },
    industry: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.PROFESSIONAL,
      weight: 1.0,
      description: 'Your industry expertise',
      userGuidance: 'Essential for matching with relevant opportunities'
    },
    areas_of_expertise: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.PROFESSIONAL,
      weight: 1.0,
      description: 'Your areas of expertise',
      userGuidance: 'Critical for demonstrating your specialized knowledge'
    },

    // High importance fields
    bio: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.IDENTITY,
      weight: 0.8,
      description: 'Your professional bio',
      userGuidance: 'Establishes your credibility and expertise'
    },
    years_of_experience: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.PROFESSIONAL,
      weight: 0.8,
      description: 'Years of experience',
      userGuidance: 'Demonstrates depth of expertise'
    }
  },

  academic_student: {
    // Critical fields
    first_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.IDENTITY,
      weight: 1.0,
      description: 'Your first name',
      userGuidance: 'Required for basic profile identification'
    },
    last_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.IDENTITY,
      weight: 1.0,
      description: 'Your last name',
      userGuidance: 'Required for basic profile identification'
    },
    email: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.CONTACT,
      weight: 1.0,
      description: 'Your email address',
      userGuidance: 'Required for account access and communication'
    },
    field_of_study: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.PROFESSIONAL,
      weight: 1.0,
      description: 'Your field of study',
      userGuidance: 'Essential for academic and career matching'
    },
    institution: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.PROFESSIONAL,
      weight: 1.0,
      description: 'Your institution',
      userGuidance: 'Important for academic credibility'
    },

    // High importance fields
    bio: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.IDENTITY,
      weight: 0.8,
      description: 'Your academic bio',
      userGuidance: 'Helps others understand your academic interests'
    },
    degree_level: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.PROFESSIONAL,
      weight: 0.8,
      description: 'Your degree level',
      userGuidance: 'Important for academic level matching'
    }
  },

  academic_institution: {
    // Critical fields
    first_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.IDENTITY,
      weight: 1.0,
      description: 'Your first name',
      userGuidance: 'Required for basic profile identification'
    },
    last_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.IDENTITY,
      weight: 1.0,
      description: 'Your last name',
      userGuidance: 'Required for basic profile identification'
    },
    email: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.CONTACT,
      weight: 1.0,
      description: 'Your email address',
      userGuidance: 'Required for account access and communication'
    },
    institution_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.PROFESSIONAL,
      weight: 1.0,
      description: 'Institution name',
      userGuidance: 'Essential for institutional identification'
    },
    research_areas: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.PROFESSIONAL,
      weight: 1.0,
      description: 'Research areas',
      userGuidance: 'Critical for research collaboration matching'
    },

    // High importance fields
    bio: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.IDENTITY,
      weight: 0.8,
      description: 'Institution bio',
      userGuidance: 'Helps others understand your institutional focus'
    },
    institution_type: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.PROFESSIONAL,
      weight: 0.8,
      description: 'Institution type',
      userGuidance: 'Important for institutional categorization'
    }
  },

  organisation: {
    // Critical fields
    first_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.IDENTITY,
      weight: 1.0,
      description: 'Your first name',
      userGuidance: 'Required for basic profile identification'
    },
    last_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.IDENTITY,
      weight: 1.0,
      description: 'Your last name',
      userGuidance: 'Required for basic profile identification'
    },
    email: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.CONTACT,
      weight: 1.0,
      description: 'Your email address',
      userGuidance: 'Required for account access and communication'
    },
    organisation_name: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.PROFESSIONAL,
      weight: 1.0,
      description: 'Organisation name',
      userGuidance: 'Essential for organizational identification'
    },
    organisation_type: {
      importance: FieldImportance.CRITICAL,
      category: FieldCategory.PROFESSIONAL,
      weight: 1.0,
      description: 'Organisation type',
      userGuidance: 'Critical for organizational categorization and matching'
    },

    // High importance fields
    bio: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.IDENTITY,
      weight: 0.8,
      description: 'Organisation bio',
      userGuidance: 'Helps others understand your organizational mission'
    },
    industry: {
      importance: FieldImportance.HIGH,
      category: FieldCategory.PROFESSIONAL,
      weight: 0.8,
      description: 'Industry focus',
      userGuidance: 'Important for industry-specific partnerships'
    }
  }
}

// Default field configuration for unknown fields
const DEFAULT_FIELD_CONFIG: FieldConfig = {
  importance: FieldImportance.MEDIUM,
  category: FieldCategory.PREFERENCES,
  weight: 0.6,
  description: 'Profile field',
  userGuidance: 'Additional profile information'
}

export interface ProfileCompletionAnalysis {
  overallScore: number
  categoryScores: Record<FieldCategory, number>
  completedFields: number
  totalFields: number
  criticalFieldsCompleted: number
  criticalFieldsTotal: number
  nextRecommendedFields: Array<{
    fieldName: string
    config: FieldConfig
    impact: number
  }>
  completionLevel: 'basic' | 'good' | 'excellent' | 'complete'
  userGuidance: string[]
}

export const useEnhancedProfileCompletion = () => {
  
  // Get field configuration for a specific field and profile type
  const getFieldConfig = (profileType: string, fieldName: string): FieldConfig => {
    const profileConfig = FIELD_CONFIGURATIONS[profileType]
    if (profileConfig && profileConfig[fieldName]) {
      return profileConfig[fieldName]
    }
    return DEFAULT_FIELD_CONFIG
  }
  
  // Check if a field has a meaningful value
  const hasValue = (value: any): boolean => {
    if (value === null || value === undefined || value === '') {
      return false
    }
    if (Array.isArray(value)) {
      return value.length > 0
    }
    if (typeof value === 'object') {
      return Object.keys(value).length > 0
    }
    return true
  }
  
  // Calculate enhanced profile completion
  const calculateEnhancedCompletion = (
    profile: BaseProfile, 
    profileData: any = {}
  ): ProfileCompletionAnalysis => {
    if (!profile || !profile.profile_type) {
      return {
        overallScore: 0,
        categoryScores: {} as Record<FieldCategory, number>,
        completedFields: 0,
        totalFields: 0,
        criticalFieldsCompleted: 0,
        criticalFieldsTotal: 0,
        nextRecommendedFields: [],
        completionLevel: 'basic',
        userGuidance: ['Please select a profile type to get started']
      }
    }
    
    const profileType = profile.profile_type
    const profileConfig = FIELD_CONFIGURATIONS[profileType] || {}
    const allData = { ...profile, ...profileData }
    
    let totalWeightedScore = 0
    let totalPossibleWeight = 0
    let completedFields = 0
    let totalFields = 0
    let criticalFieldsCompleted = 0
    let criticalFieldsTotal = 0
    
    const categoryScores: Record<FieldCategory, { score: number; total: number }> = {
      [FieldCategory.IDENTITY]: { score: 0, total: 0 },
      [FieldCategory.CONTACT]: { score: 0, total: 0 },
      [FieldCategory.PROFESSIONAL]: { score: 0, total: 0 },
      [FieldCategory.GOALS]: { score: 0, total: 0 },
      [FieldCategory.SOCIAL]: { score: 0, total: 0 },
      [FieldCategory.ASSETS]: { score: 0, total: 0 },
      [FieldCategory.PREFERENCES]: { score: 0, total: 0 }
    }
    
    const incompleteFields: Array<{
      fieldName: string
      config: FieldConfig
      impact: number
    }> = []
    
    // Analyze each configured field
    Object.entries(profileConfig).forEach(([fieldName, config]) => {
      totalFields++
      totalPossibleWeight += config.weight
      categoryScores[config.category].total += config.weight
      
      if (config.importance === FieldImportance.CRITICAL) {
        criticalFieldsTotal++
      }
      
      if (hasValue(allData[fieldName])) {
        completedFields++
        totalWeightedScore += config.weight
        categoryScores[config.category].score += config.weight
        
        if (config.importance === FieldImportance.CRITICAL) {
          criticalFieldsCompleted++
        }
      } else {
        // Calculate impact of completing this field
        const impact = (config.weight / totalPossibleWeight) * 100
        incompleteFields.push({
          fieldName,
          config,
          impact
        })
      }
    })
    
    // Calculate overall score
    const overallScore = totalPossibleWeight > 0 
      ? Math.round((totalWeightedScore / totalPossibleWeight) * 100)
      : 0
    
    // Calculate category scores
    const finalCategoryScores: Record<FieldCategory, number> = {}
    Object.entries(categoryScores).forEach(([category, { score, total }]) => {
      finalCategoryScores[category as FieldCategory] = total > 0 
        ? Math.round((score / total) * 100)
        : 0
    })
    
    // Sort incomplete fields by impact (highest first)
    const nextRecommendedFields = incompleteFields
      .sort((a, b) => b.impact - a.impact)
      .slice(0, 5) // Top 5 recommendations
    
    // Determine completion level
    let completionLevel: 'basic' | 'good' | 'excellent' | 'complete'
    if (overallScore >= 95) completionLevel = 'complete'
    else if (overallScore >= 80) completionLevel = 'excellent'
    else if (overallScore >= 60) completionLevel = 'good'
    else completionLevel = 'basic'
    
    // Generate user guidance
    const userGuidance: string[] = []
    
    if (criticalFieldsCompleted < criticalFieldsTotal) {
      userGuidance.push(`Complete ${criticalFieldsTotal - criticalFieldsCompleted} critical fields to improve your profile visibility`)
    }
    
    if (nextRecommendedFields.length > 0) {
      const topField = nextRecommendedFields[0]
      userGuidance.push(`Next: Add your ${topField.config.description.toLowerCase()} for ${topField.impact.toFixed(1)}% improvement`)
    }
    
    if (completionLevel === 'basic') {
      userGuidance.push('Focus on completing critical fields first for better matching')
    } else if (completionLevel === 'good') {
      userGuidance.push('Great progress! Add professional details to attract more connections')
    } else if (completionLevel === 'excellent') {
      userGuidance.push('Excellent profile! Consider adding social media links for better networking')
    }
    
    return {
      overallScore,
      categoryScores: finalCategoryScores,
      completedFields,
      totalFields,
      criticalFieldsCompleted,
      criticalFieldsTotal,
      nextRecommendedFields,
      completionLevel,
      userGuidance
    }
  }
  
  return {
    calculateEnhancedCompletion,
    getFieldConfig,
    FieldImportance,
    FieldCategory
  }
}
