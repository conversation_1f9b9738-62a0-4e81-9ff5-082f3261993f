import { Router } from 'vue-router'
import { nextTick } from 'vue'

/**
 * Unified Navigation Service
 * 
 * Provides reliable, responsive navigation with proper error handling
 * and loading states to prevent unresponsive button clicks.
 */
class NavigationService {
  private router: Router | null = null
  private isNavigating = false
  private navigationQueue: Array<() => Promise<void>> = []

  /**
   * Initialize the navigation service with router instance
   */
  init(router: Router) {
    this.router = router
  }

  /**
   * Navigate to a route with proper error handling and loading state
   */
  async navigateTo(
    destination: string | { name: string; params?: any; query?: any },
    options: {
      immediate?: boolean
      closeMenus?: boolean
      scrollToTop?: boolean
      timeout?: number
    } = {}
  ): Promise<boolean> {
    if (!this.router) {
      console.error('NavigationService: Router not initialized')
      return false
    }

    // Prevent multiple simultaneous navigations
    if (this.isNavigating && !options.immediate) {
      console.log('NavigationService: Navigation already in progress, queueing...')
      return new Promise((resolve) => {
        this.navigationQueue.push(async () => {
          const result = await this.navigateTo(destination, { ...options, immediate: true })
          resolve(result)
        })
      })
    }

    this.isNavigating = true

    try {
      // Close menus if requested (without timeout)
      if (options.closeMenus) {
        await this.closeMenus()
      }

      // Use nextTick to ensure DOM updates are complete
      await nextTick()

      // Perform navigation with timeout protection
      const navigationPromise = typeof destination === 'string' 
        ? this.router.push(destination)
        : this.router.push(destination)

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Navigation timeout')), options.timeout || 5000)
      })

      await Promise.race([navigationPromise, timeoutPromise])

      // Scroll to top if requested
      if (options.scrollToTop !== false) {
        await nextTick()
        window.scrollTo({ top: 0, behavior: 'smooth' })
      }

      console.log('NavigationService: Navigation successful to:', destination)
      return true

    } catch (error: any) {
      console.error('NavigationService: Navigation failed:', error)
      
      // Try fallback navigation for specific routes
      if (typeof destination === 'string') {
        return await this.attemptFallbackNavigation(destination)
      }
      
      return false
    } finally {
      this.isNavigating = false
      
      // Process queued navigations
      if (this.navigationQueue.length > 0) {
        const nextNavigation = this.navigationQueue.shift()
        if (nextNavigation) {
          setTimeout(() => nextNavigation(), 100)
        }
      }
    }
  }

  /**
   * Navigate with menu closing (common pattern)
   */
  async navigateWithMenuClose(
    destination: string | { name: string; params?: any; query?: any }
  ): Promise<boolean> {
    return this.navigateTo(destination, { closeMenus: true })
  }

  /**
   * Navigate immediately without queueing
   */
  async navigateImmediate(
    destination: string | { name: string; params?: any; query?: any }
  ): Promise<boolean> {
    return this.navigateTo(destination, { immediate: true })
  }

  /**
   * Close all open menus and overlays
   */
  private async closeMenus(): Promise<void> {
    try {
      // Close mega menus
      const megaMenuStore = await import('../stores/megaMenu').then(m => m.useMegaMenuStore())
      if (megaMenuStore.isAnyMenuOpen) {
        megaMenuStore.closeMenu()
      }

      // Close any open dialogs
      const dialogs = document.querySelectorAll('.q-dialog')
      dialogs.forEach(dialog => {
        const backdrop = dialog.querySelector('.q-dialog__backdrop')
        if (backdrop) {
          (backdrop as HTMLElement).click()
        }
      })

      // Close mobile drawers
      const drawers = document.querySelectorAll('.q-drawer--mobile.q-drawer--on-top')
      drawers.forEach(drawer => {
        const backdrop = drawer.querySelector('.q-drawer__backdrop')
        if (backdrop) {
          (backdrop as HTMLElement).click()
        }
      })

      await nextTick()
    } catch (error) {
      console.warn('NavigationService: Error closing menus:', error)
    }
  }

  /**
   * Attempt fallback navigation for problematic routes
   */
  private async attemptFallbackNavigation(path: string): Promise<boolean> {
    if (!this.router) return false

    try {
      // Handle virtual community routes
      if (path.includes('/virtual-community') || path.includes('/innovation-community')) {
        const tabMatch = path.match(/\?tab=([^&]+)/)
        const tab = tabMatch ? tabMatch[1] : 'feed'

        await this.router.push({
          name: 'innovation-community',
          query: { tab }
        })
        return true
      }

      // Handle hub facilities routes
      if (path.includes('/hub-facilities')) {
        await this.router.push({ name: 'hub-facilities' })
        return true
      }

      // Handle dashboard routes
      if (path.includes('/dashboard')) {
        await this.router.push({ name: 'dashboard' })
        return true
      }

      return false
    } catch (error) {
      console.error('NavigationService: Fallback navigation failed:', error)
      return false
    }
  }

  /**
   * Check if navigation is currently in progress
   */
  get isNavigationInProgress(): boolean {
    return this.isNavigating
  }

  /**
   * Get the current route path
   */
  get currentPath(): string {
    return this.router?.currentRoute.value.path || ''
  }
}

// Create singleton instance
export const navigationService = new NavigationService()

// Export composable for easy use in components
export function useNavigation() {
  return {
    navigateTo: navigationService.navigateTo.bind(navigationService),
    navigateWithMenuClose: navigationService.navigateWithMenuClose.bind(navigationService),
    navigateImmediate: navigationService.navigateImmediate.bind(navigationService),
    isNavigating: navigationService.isNavigationInProgress,
    currentPath: navigationService.currentPath
  }
}
