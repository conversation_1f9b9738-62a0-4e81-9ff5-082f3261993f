// Profile Analytics Service
// Provides comprehensive analytics and insights for user profiles

import { supabase } from '../lib/supabase'
import { ref, computed } from 'vue'

export interface ProfileMetrics {
  profileViews: number
  profileViewsThisWeek: number
  profileViewsThisMonth: number
  connectionRequests: number
  connectionsAccepted: number
  connectionRate: number
  profileCompletionScore: number
  profileRanking: number
  totalProfiles: number
  engagementScore: number
  lastUpdated: string
}

export interface ProfileViewData {
  date: string
  views: number
  uniqueViews: number
  viewerTypes: Record<string, number>
}

export interface ProfileEngagementData {
  connectionRequests: number
  messagesSent: number
  messagesReceived: number
  profileShares: number
  mentorshipRequests: number
  collaborationInquiries: number
}

export interface ProfilePerformanceInsights {
  topPerformingFields: Array<{
    field: string
    impact: number
    description: string
  }>
  improvementSuggestions: Array<{
    suggestion: string
    impact: 'high' | 'medium' | 'low'
    category: string
  }>
  benchmarkComparison: {
    profileType: string
    averageViews: number
    averageConnections: number
    averageCompletion: number
    userPerformance: 'above' | 'average' | 'below'
  }
}

export interface ProfileAnalyticsDashboard {
  metrics: ProfileMetrics
  viewsData: ProfileViewData[]
  engagementData: ProfileEngagementData
  insights: ProfilePerformanceInsights
  trends: {
    viewsTrend: 'up' | 'down' | 'stable'
    connectionsTrend: 'up' | 'down' | 'stable'
    engagementTrend: 'up' | 'down' | 'stable'
  }
}

export const useProfileAnalytics = () => {
  const loading = ref(false)
  const error = ref<string | null>(null)
  const analytics = ref<ProfileAnalyticsDashboard | null>(null)

  // Get profile metrics
  const getProfileMetrics = async (userId: string): Promise<ProfileMetrics> => {
    try {
      // Get profile views from analytics table (we'll create this)
      const { data: viewsData, error: viewsError } = await supabase
        .from('profile_analytics')
        .select('*')
        .eq('profile_user_id', userId)
        .order('created_at', { ascending: false })

      if (viewsError) throw viewsError

      // Calculate metrics
      const now = new Date()
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

      const totalViews = viewsData?.length || 0
      const viewsThisWeek = viewsData?.filter(v => 
        new Date(v.created_at) >= oneWeekAgo
      ).length || 0
      const viewsThisMonth = viewsData?.filter(v => 
        new Date(v.created_at) >= oneMonthAgo
      ).length || 0

      // Get connection data
      const { data: connectionsData, error: connectionsError } = await supabase
        .from('connections')
        .select('status, created_at')
        .or(`user_id.eq.${userId},connected_user_id.eq.${userId}`)

      if (connectionsError) throw connectionsError

      const connectionRequests = connectionsData?.filter(c => c.status === 'pending').length || 0
      const connectionsAccepted = connectionsData?.filter(c => c.status === 'accepted').length || 0
      const connectionRate = connectionRequests > 0 ? (connectionsAccepted / connectionRequests) * 100 : 0

      // Get profile completion
      const { data: profileData, error: profileError } = await supabase
        .from('personal_details')
        .select('profile_completion')
        .eq('user_id', userId)
        .single()

      if (profileError) throw profileError

      // Calculate engagement score (simplified algorithm)
      const engagementScore = Math.min(100, 
        (totalViews * 0.3) + 
        (connectionsAccepted * 2) + 
        (profileData.profile_completion * 0.5)
      )

      return {
        profileViews: totalViews,
        profileViewsThisWeek: viewsThisWeek,
        profileViewsThisMonth: viewsThisMonth,
        connectionRequests,
        connectionsAccepted,
        connectionRate: Math.round(connectionRate),
        profileCompletionScore: profileData.profile_completion || 0,
        profileRanking: 0, // We'll calculate this separately
        totalProfiles: 0, // We'll calculate this separately
        engagementScore: Math.round(engagementScore),
        lastUpdated: new Date().toISOString()
      }
    } catch (err) {
      console.error('Error getting profile metrics:', err)
      throw err
    }
  }

  // Get profile views data for charts
  const getProfileViewsData = async (userId: string, days: number = 30): Promise<ProfileViewData[]> => {
    try {
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)

      const { data, error } = await supabase
        .from('profile_analytics')
        .select('created_at, viewer_user_id, viewer_profile_type')
        .eq('profile_user_id', userId)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: true })

      if (error) throw error

      // Group by date
      const viewsByDate: Record<string, { views: number; uniqueViews: Set<string>; viewerTypes: Record<string, number> }> = {}

      data?.forEach(view => {
        const date = new Date(view.created_at).toISOString().split('T')[0]
        
        if (!viewsByDate[date]) {
          viewsByDate[date] = {
            views: 0,
            uniqueViews: new Set(),
            viewerTypes: {}
          }
        }

        viewsByDate[date].views++
        if (view.viewer_user_id) {
          viewsByDate[date].uniqueViews.add(view.viewer_user_id)
        }
        
        const viewerType = view.viewer_profile_type || 'unknown'
        viewsByDate[date].viewerTypes[viewerType] = (viewsByDate[date].viewerTypes[viewerType] || 0) + 1
      })

      // Convert to array format
      return Object.entries(viewsByDate).map(([date, data]) => ({
        date,
        views: data.views,
        uniqueViews: data.uniqueViews.size,
        viewerTypes: data.viewerTypes
      }))
    } catch (err) {
      console.error('Error getting profile views data:', err)
      throw err
    }
  }

  // Get engagement data
  const getEngagementData = async (userId: string): Promise<ProfileEngagementData> => {
    try {
      // This would integrate with various engagement tracking systems
      // For now, we'll return mock data structure
      return {
        connectionRequests: 0,
        messagesSent: 0,
        messagesReceived: 0,
        profileShares: 0,
        mentorshipRequests: 0,
        collaborationInquiries: 0
      }
    } catch (err) {
      console.error('Error getting engagement data:', err)
      throw err
    }
  }

  // Generate performance insights
  const generateInsights = async (userId: string, profileType: string): Promise<ProfilePerformanceInsights> => {
    try {
      // Get user's profile data for analysis
      const { data: profileData, error } = await supabase
        .from('personal_details')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error) throw error

      // Analyze top performing fields (simplified)
      const topPerformingFields = [
        {
          field: 'Professional Bio',
          impact: 85,
          description: 'Complete bio increases profile views by 85%'
        },
        {
          field: 'Profile Picture',
          impact: 70,
          description: 'Profile picture increases connection rate by 70%'
        },
        {
          field: 'Contact Information',
          impact: 60,
          description: 'Complete contact info improves engagement by 60%'
        }
      ]

      // Generate improvement suggestions
      const improvementSuggestions = []
      
      if (!profileData.bio || profileData.bio.length < 100) {
        improvementSuggestions.push({
          suggestion: 'Expand your professional bio to at least 100 characters',
          impact: 'high' as const,
          category: 'Profile Content'
        })
      }

      if (!profileData.linkedin) {
        improvementSuggestions.push({
          suggestion: 'Add your LinkedIn profile to increase credibility',
          impact: 'medium' as const,
          category: 'Social Media'
        })
      }

      // Get benchmark data (simplified)
      const benchmarkComparison = {
        profileType,
        averageViews: 25,
        averageConnections: 8,
        averageCompletion: 65,
        userPerformance: 'average' as const
      }

      return {
        topPerformingFields,
        improvementSuggestions,
        benchmarkComparison
      }
    } catch (err) {
      console.error('Error generating insights:', err)
      throw err
    }
  }

  // Track profile view
  const trackProfileView = async (profileUserId: string, viewerUserId?: string, viewerProfileType?: string) => {
    try {
      // Don't track self-views
      if (profileUserId === viewerUserId) return

      const { error } = await supabase
        .from('profile_analytics')
        .insert({
          profile_user_id: profileUserId,
          viewer_user_id: viewerUserId,
          viewer_profile_type: viewerProfileType,
          event_type: 'profile_view',
          created_at: new Date().toISOString()
        })

      if (error) throw error
    } catch (err) {
      console.error('Error tracking profile view:', err)
      // Don't throw error for tracking failures
    }
  }

  // Load complete analytics dashboard
  const loadAnalyticsDashboard = async (userId: string, profileType: string) => {
    loading.value = true
    error.value = null

    try {
      const [metrics, viewsData, engagementData, insights] = await Promise.all([
        getProfileMetrics(userId),
        getProfileViewsData(userId),
        getEngagementData(userId),
        generateInsights(userId, profileType)
      ])

      // Calculate trends (simplified)
      const trends = {
        viewsTrend: 'stable' as const,
        connectionsTrend: 'stable' as const,
        engagementTrend: 'stable' as const
      }

      analytics.value = {
        metrics,
        viewsData,
        engagementData,
        insights,
        trends
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to load analytics'
      console.error('Error loading analytics dashboard:', err)
    } finally {
      loading.value = false
    }
  }

  return {
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    analytics: computed(() => analytics.value),
    loadAnalyticsDashboard,
    trackProfileView,
    getProfileMetrics,
    getProfileViewsData,
    generateInsights
  }
}
