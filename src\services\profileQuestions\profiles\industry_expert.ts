// Industry Expert Profile Questions
import { ProfileType } from '../types';
import { commonOptions } from '../common';
import { bioSection, socialMediaSection, businessContactSection, locationSection, goalsSection, fileUploadsSection } from '../sections/index';

// Industry Expert profile questions
export const industryExpertProfile: ProfileType = {
  type: 'industry_expert',
  displayName: 'Industry Expert',
  sections: [
    // Bio & Basic Information (common across all profiles)
    bioSection,

    // Social Media (common across all profiles)
    socialMediaSection,

    // Business Contact Information (common across all profiles)
    businessContactSection,

    // Location (common across all profiles)
    locationSection,

    // Goals and Interests (common across all profiles for matchmaking)
    goalsSection,

    // File Uploads (company assets)
    fileUploadsSection,

    // Industry Expert-specific Information
    {
      title: 'Professional Information',
      icon: 'work',
      description: 'Tell us about your professional background',
      questions: [
        {
          id: 'industry',
          name: 'industry',
          label: 'Industry',
          type: 'select',
          required: true,
          options: 'industryOptions',
          hint: 'Your primary industry'
        },
        {
          id: 'job_title',
          name: 'job_title',
          label: 'Job Title',
          type: 'text',
          required: true,
          hint: 'Your current job title'
        },
        {
          id: 'company',
          name: 'company',
          label: 'Company/Organization',
          type: 'text',
          required: true,
          hint: 'Where you currently work'
        },
        {
          id: 'years_in_industry',
          name: 'years_in_industry',
          label: 'Years of Experience',
          type: 'number',
          required: true,
          hint: 'Years of experience in your industry'
        },
        {
          id: 'bio',
          name: 'bio',
          label: 'Professional Bio',
          type: 'text',
          fullWidth: true,
          required: true,
          hint: 'Brief professional biography highlighting your expertise'
        }
      ]
    },
    {
      title: 'Expertise & Specialization',
      icon: 'psychology',
      description: 'Tell us about your areas of expertise',
      questions: [
        {
          id: 'areas_of_expertise',
          name: 'areas_of_expertise',
          label: 'Areas of Expertise',
          type: 'multi-select',
          required: true,
          options: 'areasOfExpertiseOptions',
          fullWidth: true,
          hint: 'Select your primary areas of expertise'
        },
        {
          id: 'industry_specialization',
          name: 'industry_specialization',
          label: 'Specialties',
          type: 'multi-select',
          required: true,
          options: 'specialtyOptions',
          fullWidth: true,
          hint: 'Select your specialized areas of knowledge'
        },
        {
          id: 'certifications',
          name: 'certifications',
          label: 'Certifications & Qualifications',
          type: 'multi-select-free',
          fullWidth: true,
          hint: 'List relevant certifications or qualifications'
        },
        {
          id: 'skills',
          name: 'skills',
          label: 'Key Skills',
          type: 'multi-select',
          options: 'skillsOptions',
          fullWidth: true,
          hint: 'Select your key professional skills'
        },
        {
          id: 'languages',
          name: 'languages',
          label: 'Languages',
          type: 'multi-select',
          options: 'languageOptions',
          hint: 'Languages you speak professionally'
        }
      ]
    },
    {
      title: 'Contributions & Achievements',
      icon: 'emoji_events',
      description: 'Tell us about your professional contributions',
      questions: [
        {
          id: 'publications',
          name: 'publications',
          label: 'Publications',
          type: 'multi-select-free',
          fullWidth: true,
          hint: 'List your relevant publications'
        },
        {
          id: 'speaking_engagements',
          name: 'speaking_engagements',
          label: 'Speaking Engagements',
          type: 'multi-select-free',
          fullWidth: true,
          hint: 'List notable speaking engagements'
        },
        {
          id: 'notable_projects',
          name: 'notable_projects',
          label: 'Notable Projects',
          type: 'text',
          fullWidth: true,
          hint: 'Describe notable projects you\'ve worked on'
        },
        {
          id: 'industry_impact',
          name: 'industry_impact',
          label: 'Industry Impact',
          type: 'text',
          fullWidth: true,
          hint: 'Describe your impact on your industry'
        },
        {
          id: 'awards',
          name: 'awards',
          label: 'Awards & Recognition',
          type: 'multi-select-free',
          fullWidth: true,
          hint: 'List any awards or recognition received'
        }
      ]
    },
    {
      title: 'Collaboration Preferences',
      icon: 'handshake',
      description: 'Tell us about your collaboration interests',
      questions: [
        {
          id: 'collaboration_types',
          name: 'collaboration_types',
          label: 'Collaboration Types',
          type: 'multi-select',
          required: true,
          options: 'collaborationTypeOptions',
          fullWidth: true,
          hint: 'Select the types of collaborations you\'re interested in'
        },
        {
          id: 'preferred_stages',
          name: 'preferred_stages',
          label: 'Preferred Innovation Stages',
          type: 'multi-select',
          options: 'innovationStageOptions',
          fullWidth: true,
          hint: 'Select the innovation stages you prefer to work with'
        },
        {
          id: 'availability',
          name: 'availability',
          label: 'Availability',
          type: 'text',
          fullWidth: true,
          hint: 'Describe your availability for collaborations'
        },
        {
          id: 'consulting_available',
          name: 'consulting_available',
          label: 'Available for consulting',
          type: 'boolean',
          hint: 'Are you available for consulting work?'
        },
        {
          id: 'mentoring_available',
          name: 'mentoring_available',
          label: 'Available for mentoring',
          type: 'boolean',
          hint: 'Are you available for mentoring?'
        },
        {
          id: 'international_collaborations',
          name: 'international_collaborations',
          label: 'Open to international collaborations',
          type: 'boolean',
          hint: 'Are you open to international collaborations?'
        }
      ]
    },
    {
      title: 'Contact Information',
      icon: 'contact_mail',
      description: 'Tell us how to contact you',
      questions: [
        {
          id: 'contact_email',
          name: 'contact_email',
          label: 'Contact Email',
          type: 'text',
          required: true,
          hint: 'Email address for professional inquiries'
        },
        {
          id: 'contact_phone',
          name: 'contact_phone',
          label: 'Contact Phone',
          type: 'text',
          hint: 'Phone number for professional inquiries'
        }
      ]
    },
    {
      title: 'Location Information',
      icon: 'location_on',
      description: 'Tell us where you are based',
      questions: [
        {
          id: 'country',
          name: 'country',
          label: 'Country',
          type: 'select',
          required: true,
          options: 'countryOptions',
          hint: 'Country where you are based'
        },
        {
          id: 'city',
          name: 'city',
          label: 'City',
          type: 'text',
          required: true,
          hint: 'City where you are based'
        },
        {
          id: 'address',
          name: 'address',
          label: 'Address',
          type: 'text',
          fullWidth: true,
          hint: 'Your professional address'
        }
      ]
    },
    {
      title: 'Online Presence',
      icon: 'public',
      description: 'Tell us about your online presence',
      questions: [
        {
          id: 'website',
          name: 'website',
          label: 'Website/Portfolio',
          type: 'text',
          fullWidth: true,
          hint: 'Your personal or professional website'
        },
        {
          id: 'linkedin',
          name: 'linkedin',
          label: 'LinkedIn Profile',
          type: 'text',
          hint: 'Your LinkedIn profile URL'
        },
        {
          id: 'twitter',
          name: 'twitter',
          label: 'Twitter/X Profile',
          type: 'text',
          hint: 'Your Twitter/X profile URL'
        },
        {
          id: 'other_social',
          name: 'other_social',
          label: 'Other Social Media',
          type: 'text',
          fullWidth: true,
          hint: 'Other relevant social media profiles'
        }
      ]
    }
  ],
  options: {
    ...commonOptions,
    specialtyOptions: [
      'Digital Transformation', 'Artificial Intelligence', 'Machine Learning',
      'Blockchain', 'IoT', 'Cloud Computing', 'Cybersecurity',
      'Data Science', 'UX/UI Design', 'Mobile Development',
      'Web Development', 'DevOps', 'Agile Methodologies',
      'Project Management', 'Product Management', 'Business Analysis',
      'Market Research', 'Financial Analysis', 'Investment Banking',
      'Venture Capital', 'Private Equity', 'Mergers & Acquisitions',
      'Regulatory Compliance', 'Risk Management', 'Quality Assurance',
      'Supply Chain Management', 'Logistics', 'Manufacturing',
      'Healthcare IT', 'Telemedicine', 'Biotechnology',
      'Pharmaceuticals', 'Medical Devices', 'Clinical Research',
      'Renewable Energy', 'Sustainability', 'Environmental Science',
      'Agricultural Technology', 'Food Science', 'Retail Technology',
      'E-commerce', 'Digital Marketing', 'Content Strategy',
      'Public Relations', 'Brand Management', 'Customer Experience',
      'Sales Strategy', 'Business Development', 'Strategic Partnerships',
      'Organizational Development', 'Change Management', 'Leadership Development',
      'Talent Acquisition', 'Employee Engagement', 'Diversity & Inclusion',
      'Legal Affairs', 'Intellectual Property', 'Contract Negotiation',
      'Government Relations', 'Public Policy', 'International Trade',
      'Education Technology', 'Curriculum Development', 'Instructional Design',
      'Other'
    ],
    collaborationTypeOptions: [
      'Advisory', 'Consulting', 'Mentoring', 'Speaking',
      'Training', 'Research Collaboration', 'Project Collaboration',
      'Board Membership', 'Investment Opportunities', 'Strategic Partnerships',
      'Other'
    ],
    skillsOptions: [
      'Strategic Planning', 'Business Development', 'Project Management',
      'Team Leadership', 'Financial Analysis', 'Market Research',
      'Product Development', 'Process Improvement', 'Change Management',
      'Stakeholder Management', 'Negotiation', 'Public Speaking',
      'Technical Writing', 'Data Analysis', 'Problem Solving',
      'Decision Making', 'Critical Thinking', 'Innovation',
      'Creativity', 'Communication', 'Collaboration',
      'Adaptability', 'Resilience', 'Time Management',
      'Other'
    ],
    areasOfExpertiseOptions: [
      'Business Strategy', 'Product Development', 'Marketing',
      'Sales', 'Finance', 'Legal', 'Technology', 'Operations',
      'Human Resources', 'Leadership', 'Fundraising', 'Scaling',
      'International Expansion', 'Innovation Management', 'Digital Transformation',
      'Entrepreneurship', 'Startup Development', 'Market Entry', 'Growth Strategy',
      'Product-Market Fit', 'Customer Experience', 'Supply Chain Management',
      'Sustainability', 'Social Impact', 'Regulatory Compliance', 'Other'
    ]
  }
};
