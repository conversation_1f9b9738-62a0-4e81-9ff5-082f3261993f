// Innovator Profile Questions
import { ProfileType } from '../types';
import { commonOptions } from '../common';
import { bioSection, socialMediaSection, businessContactSection, locationSection, goalsSection, fileUploadsSection } from '../sections/index';

// Innovator profile questions
export const innovatorProfile: ProfileType = {
  type: 'innovator',
  displayName: 'Innovator',
  sections: [
    // Bio & Basic Information (common across all profiles)
    bioSection,

    // Basic Innovation Information
    {
      title: 'Innovation Details',
      icon: 'lightbulb',
      description: 'Tell us about your innovation',
      questions: [
        {
          id: 'innovation_area',
          name: 'innovation_area',
          label: 'Innovation Area',
          type: 'select',
          required: true,
          options: 'innovationAreaOptions',
          hint: 'Select the primary area of your innovation'
        },
        {
          id: 'innovation_stage',
          name: 'innovation_stage',
          label: 'Innovation Stage',
          type: 'select',
          required: true,
          options: 'innovationStageOptions',
          hint: 'What stage is your innovation currently at?'
        },
        {
          id: 'innovation_description',
          name: 'innovation_description',
          label: 'Innovation Description',
          type: 'textarea',
          required: true,
          fullWidth: true,
          hint: 'Briefly describe your innovation'
        },
        {
          id: 'team_size',
          name: 'team_size',
          label: 'Team Size',
          type: 'number',
          required: true,
          hint: 'How many people are working on this innovation?'
        },
        {
          id: 'team_description',
          name: 'team_description',
          label: 'Team Description',
          type: 'textarea',
          hint: 'Tell us about your team'
        }
      ]
    },
    {
      title: 'Funding & Prototype',
      icon: 'attach_money',
      description: 'Tell us about your funding needs and prototype status',
      questions: [
        {
          id: 'funding_needs',
          name: 'funding_needs',
          label: 'Are you seeking funding?',
          type: 'toggle',
          required: true
        },
        {
          id: 'funding_amount',
          name: 'funding_amount',
          label: 'Funding Amount',
          type: 'number',
          condition: {
            field: 'funding_needs',
            value: true
          },
          hint: 'How much funding are you looking for?'
        },
        {
          id: 'funding_stage',
          name: 'funding_stage',
          label: 'Funding Stage',
          type: 'select',
          condition: {
            field: 'funding_needs',
            value: true
          },
          options: 'fundingStageOptions',
          hint: 'What funding stage are you at?'
        },
        {
          id: 'has_prototype',
          name: 'has_prototype',
          label: 'Do you have a prototype?',
          type: 'toggle',
          required: true
        },
        {
          id: 'prototype_description',
          name: 'prototype_description',
          label: 'Prototype Description',
          type: 'textarea',
          fullWidth: true,
          condition: {
            field: 'has_prototype',
            value: true
          },
          hint: 'Describe your prototype'
        }
      ]
    },
    {
      title: 'Current Challenges',
      icon: 'flag',
      description: 'Tell us about the challenges you are facing',
      questions: [
        {
          id: 'challenges',
          name: 'challenges',
          label: 'Current Challenges',
          type: 'multi-select',
          options: 'challengesOptions',
          fullWidth: true,
          hint: 'What challenges are you currently facing?'
        }
      ]
    },
    // Social Media Section (common across all profiles)
    socialMediaSection,

    // Business Contact Information (separate from personal contact details)
    businessContactSection,

    // Location Information (common across all profiles)
    locationSection,

    // Goals and Interests (common across all profiles for matchmaking)
    goalsSection,

    // File Uploads (company assets)
    fileUploadsSection,

    // Additional Innovation-specific Information
    {
      title: 'Market & Competition',
      icon: 'trending_up',
      description: 'Tell us about your market and competition',
      questions: [
        {
          id: 'target_market',
          name: 'target_market',
          label: 'Target Market',
          type: 'textarea',
          required: true,
          fullWidth: true,
          hint: 'Describe your target market and customer segments'
        },
        {
          id: 'market_size',
          name: 'market_size',
          label: 'Market Size',
          type: 'select',
          options: 'marketSizeOptions',
          hint: 'What is the estimated size of your target market?'
        },
        {
          id: 'competitors',
          name: 'competitors',
          label: 'Competitors',
          type: 'textarea',
          fullWidth: true,
          hint: 'Who are your main competitors?'
        },
        {
          id: 'competitive_advantage',
          name: 'competitive_advantage',
          label: 'Competitive Advantage',
          type: 'textarea',
          fullWidth: true,
          hint: 'What is your competitive advantage?'
        },
        {
          id: 'go_to_market',
          name: 'go_to_market',
          label: 'Go-to-Market Strategy',
          type: 'textarea',
          fullWidth: true,
          hint: 'Describe your go-to-market strategy'
        }
      ]
    },

    {
      title: 'Traction & Metrics',
      icon: 'insights',
      description: 'Tell us about your traction and key metrics',
      questions: [
        {
          id: 'current_traction',
          name: 'current_traction',
          label: 'Current Traction',
          type: 'textarea',
          fullWidth: true,
          hint: 'Describe your current traction (users, customers, revenue, etc.)'
        },
        {
          id: 'revenue_model',
          name: 'revenue_model',
          label: 'Revenue Model',
          type: 'select',
          options: 'revenueModelOptions',
          hint: 'What is your revenue model?'
        },
        {
          id: 'current_revenue',
          name: 'current_revenue',
          label: 'Current Annual Revenue',
          type: 'select',
          options: 'revenueOptions',
          hint: 'What is your current annual revenue?'
        },
        {
          id: 'key_metrics',
          name: 'key_metrics',
          label: 'Key Metrics',
          type: 'textarea',
          fullWidth: true,
          hint: 'What key metrics do you track?'
        },
        {
          id: 'growth_rate',
          name: 'growth_rate',
          label: 'Growth Rate',
          type: 'text',
          hint: 'What is your current growth rate? (e.g., 20% MoM)'
        }
      ]
    },

    {
      title: 'Intellectual Property',
      icon: 'gavel',
      description: 'Tell us about your intellectual property',
      questions: [
        {
          id: 'has_ip',
          name: 'has_ip',
          label: 'Do you have intellectual property?',
          type: 'toggle',
          hint: 'Patents, trademarks, copyrights, trade secrets, etc.'
        },
        {
          id: 'ip_description',
          name: 'ip_description',
          label: 'IP Description',
          type: 'textarea',
          fullWidth: true,
          condition: {
            field: 'has_ip',
            value: true
          },
          hint: 'Describe your intellectual property'
        },
        {
          id: 'ip_status',
          name: 'ip_status',
          label: 'IP Status',
          type: 'select',
          options: 'ipStatusOptions',
          condition: {
            field: 'has_ip',
            value: true
          },
          hint: 'What is the status of your intellectual property?'
        }
      ]
    }
  ],
  options: {
    ...commonOptions,
    // Innovation-specific options
    innovationCategoryOptions: [
      'Product Innovation', 'Service Innovation', 'Process Innovation',
      'Business Model Innovation', 'Social Innovation', 'Technological Innovation',
      'Sustainable Innovation', 'Disruptive Innovation', 'Incremental Innovation',
      'Other'
    ],
    revenueOptions: [
      'Pre-revenue', '$1 - $10,000', '$10,001 - $50,000',
      '$50,001 - $100,000', '$100,001 - $500,000', '$500,001 - $1M',
      '$1M - $5M', '$5M+'
    ],
    expertiseNeededOptions: [
      'Technical Expertise', 'Business Development', 'Marketing',
      'Sales', 'Finance', 'Legal', 'Product Development',
      'UX/UI Design', 'Operations', 'Mentorship', 'Other'
    ],
    resourcesNeededOptions: [
      'Funding', 'Office Space', 'Equipment', 'Manufacturing',
      'Distribution Channels', 'Market Access', 'Regulatory Support',
      'Technical Infrastructure', 'Other'
    ],
    // New options for enhanced sections
    marketSizeOptions: [
      '$100K - $500K', '$500K - $1M', '$1M - $10M', '$10M - $50M',
      '$50M - $100M', '$100M - $500M', '$500M - $1B', '$1B - $10B', 'Over $10B'
    ],
    revenueModelOptions: [
      'Subscription', 'Freemium', 'Licensing', 'Transaction Fees',
      'Advertising', 'Data Monetization', 'Hardware Sales', 'Software Sales',
      'Service Fees', 'Marketplace/Commission', 'Hybrid', 'Other'
    ],
    ipStatusOptions: [
      'Patent Pending', 'Patent Granted', 'Trademark Registered',
      'Copyright Registered', 'Trade Secret', 'In Preparation',
      'Not Protected Yet', 'Not Applicable'
    ]
  }
}
