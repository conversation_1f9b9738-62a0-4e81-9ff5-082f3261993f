// Mentor Profile Questions
import { ProfileType } from '../types';
import { commonOptions } from '../common';
import { bioSection, socialMediaSection, contactSection, locationSection, goalsSection, fileUploadsSection } from '../sections/index';

// Mentor profile questions
export const mentorProfile: ProfileType = {
  type: 'mentor',
  displayName: 'Mentor',
  sections: [
    // Bio & Basic Information (common across all profiles)
    bioSection,

    // Expertise and Experience
    {
      title: 'Expertise & Experience',
      icon: 'school',
      description: 'Tell us about your expertise and experience',
      questions: [
        {
          id: 'expertise_areas',
          name: 'expertise_areas',
          label: 'Areas of Expertise',
          type: 'multi-select',
          required: true,
          options: 'areasOfExpertiseOptions',
          fullWidth: true,
          hint: 'What areas do you have expertise in?'
        },
        {
          id: 'years_of_experience',
          name: 'years_of_experience',
          label: 'Years of Experience',
          type: 'number',
          required: true,
          hint: 'How many years of experience do you have?'
        },
        {
          id: 'industry_experience',
          name: 'industry_experience',
          label: 'Industry Experience',
          type: 'multi-select',
          options: 'industryOptions',
          fullWidth: true,
          hint: 'Which industries have you worked in?'
        },
        {
          id: 'notable_awards',
          name: 'notable_awards',
          label: 'Notable Awards',
          type: 'multi-select',
          options: 'awardsOptions',
          fullWidth: true,
          hint: 'Select awards and recognitions you have received'
        }
      ]
    },
    {
      title: 'Mentorship Details',
      icon: 'people',
      description: 'Tell us about your mentorship approach',
      questions: [
        {
          id: 'mentorship_style',
          name: 'mentorship_style',
          label: 'Mentorship Style',
          type: 'select',
          required: true,
          options: 'mentorshipStyleOptions',
          hint: 'What is your preferred mentorship style?'
        },
        {
          id: 'availability',
          name: 'availability',
          label: 'Availability',
          type: 'select',
          required: true,
          options: 'availabilityOptions',
          hint: 'How often are you available for mentorship?'
        },
        {
          id: 'previous_mentees',
          name: 'previous_mentees',
          label: 'Previous Mentees',
          type: 'number',
          hint: 'How many people have you mentored?'
        },
        {
          id: 'mentoring_experience',
          name: 'mentoring_experience',
          label: 'Mentorship Highlights',
          type: 'textarea',
          fullWidth: true,
          hint: 'Share highlights from your mentorship experience'
        }
      ]
    },
    {
      title: 'Professional Background',
      icon: 'work',
      description: 'Tell us about your professional background',
      questions: [
        {
          id: 'mentor_current_role',
          name: 'mentor_current_role',
          label: 'Current Role',
          type: 'text',
          hint: 'What is your current professional role?'
        },
        {
          id: 'company',
          name: 'company',
          label: 'Company',
          type: 'text',
          hint: 'What company do you work for?'
        },
        {
          id: 'education',
          name: 'education',
          label: 'Education',
          type: 'multi-select',
          options: 'educationOptions',
          fullWidth: true,
          hint: 'Select your educational qualifications'
        }
      ]
    },

    // Mentorship Success Stories
    {
      title: 'Success Stories',
      icon: 'emoji_events',
      description: 'Share your mentorship success stories',
      questions: [
        {
          id: 'success_stories',
          name: 'success_stories',
          label: 'Success Stories',
          type: 'textarea',
          fullWidth: true,
          hint: 'Share some of your mentorship success stories'
        },
        {
          id: 'mentee_achievements',
          name: 'mentee_achievements',
          label: 'Mentee Achievements',
          type: 'textarea',
          fullWidth: true,
          hint: 'What achievements have your mentees accomplished?'
        },
        {
          id: 'testimonials',
          name: 'testimonials',
          label: 'Testimonials',
          type: 'textarea',
          fullWidth: true,
          hint: 'Share testimonials from your mentees'
        }
      ]
    },

    // Mentorship Methodology
    {
      title: 'Mentorship Methodology',
      icon: 'psychology',
      description: 'Tell us about your mentorship methodology',
      questions: [
        {
          id: 'mentorship_philosophy',
          name: 'mentorship_philosophy',
          label: 'Mentorship Philosophy',
          type: 'textarea',
          fullWidth: true,
          hint: 'Describe your mentorship philosophy'
        },
        {
          id: 'mentorship_methods',
          name: 'mentorship_methods',
          label: 'Mentorship Methods',
          type: 'multi-select',
          options: 'mentorshipMethodsOptions',
          fullWidth: true,
          hint: 'What methods do you use in your mentorship?'
        },
        {
          id: 'mentorship_tools',
          name: 'mentorship_tools',
          label: 'Mentorship Tools',
          type: 'multi-select',
          options: 'mentorshipToolsOptions',
          fullWidth: true,
          hint: 'What tools do you use in your mentorship?'
        },
        {
          id: 'mentorship_duration',
          name: 'mentorship_duration',
          label: 'Preferred Mentorship Duration',
          type: 'select',
          options: 'mentorshipDurationOptions',
          hint: 'What is your preferred mentorship duration?'
        }
      ]
    },

    // Mentee Preferences
    {
      title: 'Mentee Preferences',
      icon: 'person_search',
      description: 'Tell us about your preferred mentees',
      questions: [
        {
          id: 'preferred_mentee_stage',
          name: 'preferred_mentee_stage',
          label: 'Preferred Mentee Stage',
          type: 'multi-select',
          options: 'menteeStageOptions',
          fullWidth: true,
          hint: 'What stage of mentees do you prefer to work with?'
        },
        {
          id: 'preferred_mentee_background',
          name: 'preferred_mentee_background',
          label: 'Preferred Mentee Background',
          type: 'multi-select',
          options: 'menteeBackgroundOptions',
          fullWidth: true,
          hint: 'What background do you prefer your mentees to have?'
        },
        {
          id: 'expectations',
          name: 'expectations',
          label: 'Mentee Expectations',
          type: 'textarea',
          fullWidth: true,
          hint: 'What do you expect from your mentees?'
        }
      ]
    },

    // Social Media Section (common across all profiles)
    socialMediaSection,

    // Contact Information (common across all profiles)
    contactSection,

    // Location Information (common across all profiles)
    locationSection,

    // Goals and Interests (common across all profiles for matchmaking)
    goalsSection,

    // File Uploads (company assets)
    fileUploadsSection
  ],
  options: {
    ...commonOptions,
    // Expertise and Experience Options
    areasOfExpertiseOptions: [
      // Business & Strategy
      'Business Strategy & Planning', 'Strategic Management',
      'Business Model Innovation', 'Corporate Strategy',
      'Business Development', 'Strategic Partnerships',

      // Leadership & Management
      'Executive Leadership', 'Change Management',
      'Organizational Development', 'Team Leadership',
      'Performance Management', 'Crisis Management',

      // Finance & Investment
      'Financial Management', 'Investment Strategy',
      'Financial Planning', 'Risk Management',
      'Venture Capital', 'Private Equity',

      // Technology & Innovation
      'Digital Transformation', 'Technology Strategy',
      'Innovation Management', 'Product Development',
      'Artificial Intelligence', 'Blockchain Technology',

      // Marketing & Sales
      'Marketing Strategy', 'Digital Marketing',
      'Brand Management', 'Sales Strategy',
      'Customer Experience', 'Market Research',

      // Operations & Process
      'Operations Management', 'Process Optimization',
      'Supply Chain Management', 'Quality Management',
      'Project Management', 'Lean Management',

      // Industry Specific
      'Healthcare', 'Financial Services', 'Technology',
      'Manufacturing', 'Retail', 'Education', 'Agriculture',

      // Entrepreneurship
      'Startup Development', 'Business Planning',
      'Fundraising', 'Growth Strategy',
      'Market Entry', 'Scale-up Strategy'
    ],

    // Awards and Education Options
    awardsOptions: [
      'Industry Excellence Award', 'Leadership Award', 'Innovation Award',
      'Entrepreneurship Award', 'Community Service Award', 'Teaching Excellence Award',
      'Research Award', 'Professional Achievement Award', 'Lifetime Achievement Award',
      'Rising Star Award', 'Best Mentor Award', 'Thought Leadership Award',
      'Technical Excellence Award', 'Business Impact Award', 'Social Impact Award',
      'Sustainability Award', 'Diversity & Inclusion Award', 'Customer Excellence Award',
      'Academic Achievement Award', 'Government Recognition', 'International Recognition',
      'Industry Certification', 'Patent Award', 'Publication Award',
      'Other'
    ],

    educationOptions: [
      'High School Diploma', 'Technical Certificate', 'Associate Degree',
      'Bachelor of Arts (BA)', 'Bachelor of Science (BS)', 'Bachelor of Business Administration (BBA)',
      'Bachelor of Engineering (BEng)', 'Master of Arts (MA)', 'Master of Science (MS)',
      'Master of Business Administration (MBA)', 'Master of Engineering (MEng)',
      'Doctor of Philosophy (PhD)', 'Doctor of Business Administration (DBA)',
      'Doctor of Education (EdD)', 'Medical Doctor (MD)', 'Juris Doctor (JD)',
      'Professional Certification', 'Executive Education', 'Vocational Training',
      'Online Course Certification', 'Industry-Specific Training', 'Leadership Development Program',
      'Other'
    ],

    // Mentorship Style and Availability Options
    mentorshipStyleOptions: commonOptions.mentorshipStyleOptions,
    availabilityOptions: commonOptions.availabilityOptions,

    // Mentorship Methodology Options
    mentorshipMethodsOptions: [
      'Goal Setting', 'Action Planning', 'Feedback Sessions',
      'Role Playing', 'Shadowing', 'Case Studies',
      'Skill Development', 'Knowledge Transfer', 'Networking',
      'Career Planning', 'Problem Solving', 'Reflection Exercises',
      'Accountability Check-ins', 'Storytelling', 'Other'
    ],

    mentorshipToolsOptions: [
      'Video Conferencing', 'Email', 'Messaging Apps',
      'Project Management Tools', 'Shared Documents',
      'Learning Management Systems', 'Assessment Tools',
      'Goal Tracking Software', 'Feedback Platforms',
      'Skill Development Resources', 'Other'
    ],

    mentorshipDurationOptions: [
      'Less than 1 month', '1-3 months', '3-6 months',
      '6-12 months', '1-2 years', 'More than 2 years',
      'Ongoing/No fixed duration'
    ],

    // Mentee Preferences Options
    menteeStageOptions: [
      'Early Career', 'Mid-Career', 'Senior Level',
      'Career Transition', 'Entrepreneur/Founder',
      'Student', 'Graduate', 'Executive', 'Other'
    ],

    menteeBackgroundOptions: [
      'Technical', 'Business', 'Creative', 'Academic',
      'Healthcare', 'Finance', 'Legal', 'Marketing',
      'Sales', 'Operations', 'Human Resources',
      'Engineering', 'Design', 'Research', 'Other'
    ]
  }
}
