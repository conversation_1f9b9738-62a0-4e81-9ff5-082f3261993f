// Organisation Profile Questions
import { ProfileType } from '../types';
import { commonOptions } from '../common';
import { bioSection, socialMediaSection, businessContactSection, locationSection, goalsSection, fileUploadsSection } from '../sections/index';

// Organisation profile questions
export const organisationProfile: ProfileType = {
  type: 'organisation',
  displayName: 'Organisation',
  sections: [
    // Bio (common across all profiles)
    bioSection,
    {
      title: 'Organisation Information',
      icon: 'business',
      description: 'Tell us about your organisation',
      questions: [
        {
          id: 'organisation_name',
          name: 'organisation_name',
          label: 'Organisation Name',
          type: 'text',
          required: true,
          fullWidth: true,
          hint: 'Full name of your organisation'
        },
        {
          id: 'organisation_type',
          name: 'organisation_type',
          label: 'Organisation Type',
          type: 'select',
          required: true,
          options: 'organisationTypeOptions',
          hint: 'Type of organisation'
        },
        {
          id: 'your_position',
          name: 'your_position',
          label: 'Your Position in the Organisation',
          type: 'select',
          required: true,
          options: 'organizationPositionOptions',
          hint: 'Your role or position in the organisation'
        },
        {
          id: 'founding_year',
          name: 'founding_year',
          label: 'Year Founded',
          type: 'number',
          hint: 'Year the organisation was established'
        },
        {
          id: 'industry',
          name: 'industry',
          label: 'Industry',
          type: 'select',
          required: true,
          options: 'industryOptions',
          fullWidth: true,
          hint: 'Primary industry of your organisation'
        },
        {
          id: 'organisation_description',
          name: 'organisation_description',
          label: 'Organisation Description',
          type: 'text',
          required: true,
          fullWidth: true,
          hint: 'Brief description of your organisation'
        },
        {
          id: 'mission_statement',
          name: 'mission_statement',
          label: 'Mission Statement',
          type: 'text',
          fullWidth: true,
          hint: 'Your organisation\'s mission statement'
        },
        {
          id: 'vision_statement',
          name: 'vision_statement',
          label: 'Vision Statement',
          type: 'text',
          fullWidth: true,
          hint: 'Your organisation\'s vision statement'
        }
      ]
    },
    {
      title: 'Organisation Details',
      icon: 'info',
      description: 'Tell us more about your organisation',
      questions: [
        {
          id: 'organisation_size',
          name: 'organisation_size',
          label: 'Organisation Size',
          type: 'select',
          required: true,
          options: 'organisationSizeOptions',
          hint: 'Number of employees in your organisation'
        },
        {
          id: 'organisation_stage',
          name: 'organisation_stage',
          label: 'Organisation Stage',
          type: 'select',
          required: true,
          options: 'organisationStageOptions',
          hint: 'Current stage of your organisation'
        },
        {
          id: 'target_markets',
          name: 'target_markets',
          label: 'Target Markets',
          type: 'multi-select',
          options: 'marketOptions',
          fullWidth: true,
          required: true,
          hint: 'What markets does your organisation serve?'
        },
        {
          id: 'products_services',
          name: 'products_services',
          label: 'Products/Services',
          type: 'text',
          fullWidth: true,
          required: true,
          hint: 'Describe your main products or services'
        },
        {
          id: 'key_differentiators',
          name: 'key_differentiators',
          label: 'Key Differentiators',
          type: 'text',
          fullWidth: true,
          hint: 'What makes your organisation unique?'
        },
        {
          id: 'achievements',
          name: 'achievements',
          label: 'Key Achievements',
          type: 'text',
          fullWidth: true,
          hint: 'Notable achievements or milestones'
        }
      ]
    },
    {
      title: 'Location & Contact',
      icon: 'location_on',
      description: 'Tell us where your organisation is located',
      questions: [
        {
          id: 'address',
          name: 'address',
          label: 'Headquarters Address',
          type: 'text',
          fullWidth: true,
          required: true,
          hint: 'Physical address of the organisation headquarters'
        },
        {
          id: 'city',
          name: 'city',
          label: 'City',
          type: 'text',
          required: true,
          hint: 'City where the organisation is headquartered'
        },
        {
          id: 'country',
          name: 'country',
          label: 'Country',
          type: 'select',
          required: true,
          options: 'countryOptions',
          hint: 'Country where the organisation is headquartered'
        },
        {
          id: 'has_multiple_locations',
          name: 'has_multiple_locations',
          label: 'Has Multiple Locations',
          type: 'boolean',
          hint: 'Does your organisation have multiple locations?'
        },

        {
          id: 'number_of_branches',
          name: 'number_of_branches',
          label: 'Number of Branches',
          type: 'select',
          options: 'branchesCountOptions',
          hint: 'How many branches does your organization have?'
        }
      ]
    },
    {
      title: 'Contact Information',
      icon: 'contact_mail',
      description: 'Tell us how to contact your organisation',
      questions: [
        {
          id: 'contact_name',
          name: 'contact_name',
          label: 'Primary Contact Name',
          type: 'text',
          required: true,
          hint: 'Name of the primary contact person'
        },
        {
          id: 'contact_position',
          name: 'contact_position',
          label: 'Contact Position',
          type: 'select',
          required: true,
          options: 'organizationPositionOptions',
          hint: 'Position of the primary contact person in the organization'
        },
        {
          id: 'contact_function',
          name: 'contact_function',
          label: 'Contact Function',
          type: 'text',
          hint: 'Specific function or role of the contact person in the organization'
        },
        {
          id: 'contact_email',
          name: 'contact_email',
          label: 'Contact Email',
          type: 'text',
          required: true,
          hint: 'Email address for business inquiries'
        },
        {
          id: 'contact_phone',
          name: 'contact_phone',
          label: 'Contact Phone',
          type: 'text',
          hint: 'Phone number for business inquiries'
        },
        {
          id: 'general_email',
          name: 'general_email',
          label: 'General Email',
          type: 'text',
          hint: 'General email address for the organisation'
        },
        {
          id: 'general_phone',
          name: 'general_phone',
          label: 'General Phone',
          type: 'text',
          hint: 'General phone number for the organisation'
        }
      ]
    },
    {
      title: 'Innovation & R&D',
      icon: 'lightbulb',
      description: 'Tell us about your innovation activities',
      questions: [
        {
          id: 'has_rnd',
          name: 'has_rnd',
          label: 'Has R&D Department/Activities',
          type: 'boolean',
          hint: 'Does your organisation have R&D activities?'
        },
        {
          id: 'innovation_focus',
          name: 'innovation_focus',
          label: 'Innovation Focus Areas',
          type: 'multi-select',
          options: 'innovationFocusOptions',
          fullWidth: true,
          hint: 'What areas does your innovation focus on?'
        },
        {
          id: 'rnd_budget',
          name: 'rnd_budget',
          label: 'R&D Budget (% of Revenue)',
          type: 'number',
          hint: 'Approximate percentage of revenue allocated to R&D'
        },
        {
          id: 'innovation_strategy',
          name: 'innovation_strategy',
          label: 'Innovation Strategy',
          type: 'text',
          fullWidth: true,
          hint: 'Describe your organisation\'s approach to innovation'
        },
        {
          id: 'patents',
          name: 'patents',
          label: 'Patents/IP',
          type: 'text',
          fullWidth: true,
          hint: 'Describe any patents or intellectual property'
        },
        {
          id: 'innovation_achievements',
          name: 'innovation_achievements',
          label: 'Innovation Achievements',
          type: 'text',
          fullWidth: true,
          hint: 'Notable innovation achievements or breakthroughs'
        }
      ]
    },
    {
      title: 'Collaboration Interests',
      icon: 'handshake',
      description: 'Tell us about your collaboration interests',
      questions: [
        {
          id: 'collaboration_types',
          name: 'collaboration_types',
          label: 'Collaboration Types',
          type: 'multi-select',
          options: 'orgCollaborationTypeOptions',
          fullWidth: true,
          required: true,
          hint: 'What types of collaborations is your organisation interested in?'
        },
        {
          id: 'preferred_partners',
          name: 'preferred_partners',
          label: 'Preferred Partner Types',
          type: 'multi-select',
          options: 'orgPartnerTypeOptions',
          fullWidth: true,
          required: true,
          hint: 'What types of partners is your organisation looking for?'
        },
        {
          id: 'resources_offered',
          name: 'resources_offered',
          label: 'Resources Offered',
          type: 'multi-select',
          options: 'resourcesOfferedOptions',
          fullWidth: true,
          hint: 'What resources can your organisation offer to partners?'
        },
        {
          id: 'collaboration_goals',
          name: 'collaboration_goals',
          label: 'Collaboration Goals',
          type: 'text',
          fullWidth: true,
          required: true,
          hint: 'What are your organisation\'s goals for collaboration?'
        },
        {
          id: 'previous_collaborations',
          name: 'previous_collaborations',
          label: 'Previous Collaborations',
          type: 'text',
          fullWidth: true,
          hint: 'Describe any notable previous collaborations'
        },
        {
          id: 'international_collaborations',
          name: 'international_collaborations',
          label: 'Open to International Collaborations',
          type: 'boolean',
          hint: 'Is your organisation open to international collaborations?'
        }
      ]
    },
    {
      title: 'References',
      icon: 'people',
      description: 'Provide references who can verify your organization',
      questions: [
        {
          id: 'reference1_name',
          name: 'reference1_name',
          label: 'Reference 1: Name',
          type: 'text',
          required: true,
          hint: 'Name of the first reference person'
        },
        {
          id: 'reference1_position',
          name: 'reference1_position',
          label: 'Reference 1: Position',
          type: 'text',
          required: true,
          hint: 'Position of the first reference person'
        },
        {
          id: 'reference1_email',
          name: 'reference1_email',
          label: 'Reference 1: Email',
          type: 'text',
          required: true,
          hint: 'Email address of the first reference person'
        },
        {
          id: 'reference1_phone',
          name: 'reference1_phone',
          label: 'Reference 1: Phone',
          type: 'text',
          hint: 'Phone number of the first reference person'
        },
        {
          id: 'reference2_name',
          name: 'reference2_name',
          label: 'Reference 2: Name',
          type: 'text',
          required: true,
          hint: 'Name of the second reference person'
        },
        {
          id: 'reference2_position',
          name: 'reference2_position',
          label: 'Reference 2: Position',
          type: 'text',
          required: true,
          hint: 'Position of the second reference person'
        },
        {
          id: 'reference2_email',
          name: 'reference2_email',
          label: 'Reference 2: Email',
          type: 'text',
          required: true,
          hint: 'Email address of the second reference person'
        },
        {
          id: 'reference2_phone',
          name: 'reference2_phone',
          label: 'Reference 2: Phone',
          type: 'text',
          hint: 'Phone number of the second reference person'
        }
      ]
    },
    {
      title: 'Online Presence',
      icon: 'public',
      description: 'Tell us about your online presence',
      questions: [
        {
          id: 'website',
          name: 'website',
          label: 'Organisation Website',
          type: 'text',
          fullWidth: true,
          required: true,
          hint: 'URL of your organisation\'s official website'
        },
        {
          id: 'linkedin',
          name: 'linkedin',
          label: 'LinkedIn',
          type: 'text',
          hint: 'LinkedIn company page URL'
        },
        {
          id: 'twitter',
          name: 'twitter',
          label: 'Twitter/X',
          type: 'text',
          hint: 'Twitter/X profile URL'
        },
        {
          id: 'facebook',
          name: 'facebook',
          label: 'Facebook',
          type: 'text',
          hint: 'Facebook page URL'
        },
        {
          id: 'youtube',
          name: 'youtube',
          label: 'YouTube',
          type: 'text',
          hint: 'YouTube channel URL'
        },
        {
          id: 'instagram',
          name: 'instagram',
          label: 'Instagram',
          type: 'text',
          hint: 'Instagram profile URL'
        },
        {
          id: 'other_social',
          name: 'other_social',
          label: 'Other Social Media',
          type: 'text',
          fullWidth: true,
          hint: 'Other relevant social media profiles'
        }
      ]
    },

    // File Uploads (company assets)
    fileUploadsSection
  ],
  options: {
    ...commonOptions,
    organisationTypeOptions: [
      'Corporation', 'Small Business', 'Startup',
      'Non-profit', 'Government Agency', 'NGO',
      'Social Enterprise', 'Cooperative', 'Foundation',
      'Research Institution', 'Industry Association', 'Other'
    ],
    organisationSizeOptions: [
      'Micro (1-9 employees)', 'Small (10-49 employees)',
      'Medium (50-249 employees)', 'Large (250+ employees)'
    ],
    organisationStageOptions: [
      'Early Stage', 'Growth Stage', 'Mature', 'Restructuring'
    ],
    marketOptions: [
      'Consumer', 'Business', 'Government',
      'Non-profit', 'Education', 'Healthcare',
      'Financial Services', 'Manufacturing', 'Technology',
      'Retail', 'Energy', 'Transportation',
      'Agriculture', 'Construction', 'Other'
    ],
    innovationFocusOptions: [
      'Product Innovation', 'Service Innovation', 'Process Innovation',
      'Business Model Innovation', 'Technological Innovation', 'Social Innovation',
      'Sustainable Innovation', 'Disruptive Innovation', 'Incremental Innovation',
      'Other'
    ],
    orgCollaborationTypeOptions: [
      'Strategic Partnership', 'Joint Venture', 'Research Collaboration',
      'Technology Transfer', 'Licensing', 'Supply Chain Integration',
      'Distribution Partnership', 'Co-development', 'Investment',
      'Mentorship', 'Knowledge Exchange', 'Other'
    ],
    orgPartnerTypeOptions: [
      'Startups', 'SMEs', 'Large Corporations',
      'Research Institutions', 'Universities', 'Government Agencies',
      'Non-profits', 'Industry Associations', 'Investors',
      'Incubators/Accelerators', 'Individual Experts', 'Other'
    ],
    resourcesOfferedOptions: [
      'Funding', 'Expertise', 'Technology',
      'Market Access', 'Facilities', 'Equipment',
      'Distribution Channels', 'Mentorship', 'Training',
      'Research Capabilities', 'Network Access', 'Other'
    ],
    branchesCountOptions: [
      '1 (Single location)', '2-5', '6-10', '11-20',
      '21-50', '51-100', 'More than 100'
    ]
  }
};
