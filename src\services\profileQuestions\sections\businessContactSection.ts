// Business Contact Information Section
// This section is for business/organization contact information
// Used for innovator, investor, and organisation profiles

import { ProfileSection } from '../types';

export const businessContactSection: ProfileSection = {
  title: 'Business Contact Information',
  icon: 'business',
  description: 'Share your business contact information',
  questions: [
    {
      id: 'contact_email',
      name: 'contact_email',
      label: 'Business Email',
      type: 'text',
      required: true,
      hint: 'Your business email for contacts (if different from personal email)'
    },
    {
      id: 'contact_phone',
      name: 'contact_phone',
      label: 'Business Phone',
      type: 'text',
      hint: 'Your business phone number with country code (if different from personal phone)'
    }
  ]
};
