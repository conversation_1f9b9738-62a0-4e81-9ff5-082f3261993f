// Common Contact Information Section
// This section can be included in all profile types

import { ProfileSection } from '../types';

export const contactSection: ProfileSection = {
  title: 'Contact Information',
  icon: 'contact_mail',
  description: 'Share your contact information',
  questions: [
    {
      id: 'contact_email',
      name: 'contact_email',
      label: 'Contact Email',
      type: 'text',
      required: true,
      hint: 'Your preferred email for contacts (if different from account email)'
    },
    {
      id: 'contact_phone',
      name: 'contact_phone',
      label: 'Contact Phone',
      type: 'text',
      hint: 'Your phone number with country code'
    }
  ]
};
