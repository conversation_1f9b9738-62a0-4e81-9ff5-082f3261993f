// File Uploads Section
// This section can be included in relevant profile types for uploading company assets

import { ProfileSection } from '../types';

export const fileUploadsSection: ProfileSection = {
  title: 'Company Assets',
  icon: 'cloud_upload',
  description: 'Upload your company logo, cover image, and profile documents',
  questions: [
    {
      id: 'company_logo',
      name: 'company_logo',
      label: 'Company Logo',
      type: 'file_upload',
      fileType: 'image',
      hint: 'Upload your company or organization logo (JPG, PNG, GIF - max 5MB)',
      maxFileSize: 5242880, // 5MB
      storageFolder: 'profiles/logos'
    },
    {
      id: 'cover_image',
      name: 'cover_image',
      label: 'Cover Image',
      type: 'file_upload',
      fileType: 'image',
      hint: 'Upload a cover image for your profile (JPG, PNG, GIF - max 5MB)',
      maxFileSize: 5242880, // 5MB
      storageFolder: 'profiles/covers'
    },
    {
      id: 'company_profile_document',
      name: 'company_profile_document',
      label: 'Company Profile Document',
      type: 'file_upload',
      fileType: 'document',
      hint: 'Upload a company profile document (PDF, DOC, DOCX - max 10MB)',
      maxFileSize: 10485760, // 10MB
      storageFolder: 'profiles/documents'
    }
  ]
};
