// Common Goals and Interests Section
// This section can be included in all profile types for matchmaking purposes

import { ProfileSection } from '../types';

export const goalsSection: ProfileSection = {
  title: 'Goals & Interests',
  icon: 'flag',
  description: 'Share your goals and interests for better matchmaking',
  questions: [
    {
      id: 'short_term_goals',
      name: 'short_term_goals',
      label: 'Short-term Goals',
      type: 'multi-select',
      options: 'shortTermGoalsOptions',
      fullWidth: true,
      hint: 'What are your short-term goals (next 12 months)?'
    },
    {
      id: 'long_term_goals',
      name: 'long_term_goals',
      label: 'Long-term Goals',
      type: 'multi-select',
      options: 'longTermGoalsOptions',
      fullWidth: true,
      hint: 'What are your long-term goals (beyond 12 months)?'
    },
    {
      id: 'looking_for',
      name: 'looking_for',
      label: 'Looking For',
      type: 'multi-select',
      options: 'lookingForOptions',
      fullWidth: true,
      hint: 'What are you looking for on this platform?'
    },
    {
      id: 'collaboration_interests',
      name: 'collaboration_interests',
      label: 'Collaboration Interests',
      type: 'multi-select',
      options: 'collaborationInterestsOptions',
      fullWidth: true,
      hint: 'What types of collaborations are you interested in?'
    },
    {
      id: 'sdg_alignment',
      name: 'sdg_alignment',
      label: 'SDG Alignment',
      type: 'multi-select',
      options: 'sdgOptions',
      fullWidth: true,
      hint: 'Which UN Sustainable Development Goals align with your work?'
    },
    {
      id: 'additional_interests',
      name: 'additional_interests',
      label: 'Additional Interests',
      type: 'textarea',
      fullWidth: true,
      hint: 'Any other interests or goals not covered above?'
    }
  ]
};
