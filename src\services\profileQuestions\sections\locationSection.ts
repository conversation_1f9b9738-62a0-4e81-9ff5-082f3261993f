// Common Location Section
// This section can be included in all profile types

import { ProfileSection } from '../types';

export const locationSection: ProfileSection = {
  title: 'Location',
  icon: 'location_on',
  description: 'Share your location information',
  questions: [
    {
      id: 'country',
      name: 'country',
      label: 'Country',
      type: 'select',
      required: true,
      options: 'countryOptions',
      hint: 'Country where you are located'
    },
    {
      id: 'city',
      name: 'city',
      label: 'City',
      type: 'text',
      required: true,
      hint: 'City where you are located'
    },
    {
      id: 'address',
      name: 'address',
      label: 'Address',
      type: 'text',
      fullWidth: true,
      hint: 'Your physical address (optional)'
    }
  ]
};
