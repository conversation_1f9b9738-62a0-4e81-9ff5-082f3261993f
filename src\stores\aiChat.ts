/**
 * AI Chat Store - Clean Implementation
 *
 * Global state management for AI chat functionality
 */

import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { useAuthStore } from './auth'
import { getAiChatService, type ChatMessage, type AIChatResponse, type AIActionButton } from '../services/aiChatService'
import { supabase } from '@/lib/supabase'

// Enhanced chat message interface
interface EnhancedChatMessage extends ChatMessage {
  id: string
  timestamp: Date
  action_buttons?: AIActionButton[]
}

export const useAIChatStore = defineStore('aiChat', () => {
  // State
  const isOpen = ref(false)
  const isLoading = ref(false)
  const hasUnreadMessage = ref(false)
  const messages = ref<EnhancedChatMessage[]>([])
  const currentRoute = ref<string>('unknown')
  const currentConversationId = ref<string | null>(null)
  const isLoadingHistory = ref(false)

  // Computed
  const authStore = useAuthStore()

  const messageHistory = computed(() =>
    messages.value.map(msg => ({
      role: msg.role,
      content: msg.content
    }))
  )

  const lastMessage = computed(() =>
    messages.value.length > 0 ? messages.value[messages.value.length - 1] : null
  )

  // Watch for authentication changes to load/clear conversation history
  watch(() => authStore.isAuthenticated, async (isAuthenticated, wasAuthenticated) => {
    if (isAuthenticated && !wasAuthenticated) {
      // User just logged in - load their conversation history
      console.log('👤 User logged in, loading conversation history...')
      await loadConversationHistory()
    } else if (!isAuthenticated && wasAuthenticated) {
      // User logged out - clear conversation and reset
      console.log('👤 User logged out, clearing conversation...')
      currentConversationId.value = null
      clearMessages()
    }
  })

  // Actions
  const setCurrentRoute = (routeName: string) => {
    currentRoute.value = routeName
  }

  const toggleChat = () => {
    isOpen.value = !isOpen.value
    if (isOpen.value) {
      hasUnreadMessage.value = false
      initializeChat()
    }
  }

  const openChat = () => {
    isOpen.value = true
    hasUnreadMessage.value = false
    initializeChat()
  }

  const closeChat = () => {
    isOpen.value = false
  }

  const addMessage = (message: Omit<EnhancedChatMessage, 'id' | 'timestamp'>): EnhancedChatMessage => {
    const newMessage: EnhancedChatMessage = {
      ...message,
      id: crypto.randomUUID(),
      timestamp: new Date()
    }

    messages.value.push(newMessage)

    // Mark as unread if chat is closed and it's an assistant message
    if (!isOpen.value && message.role === 'assistant') {
      hasUnreadMessage.value = true
    }

    console.log('💬 Added message:', newMessage.role, newMessage.content.substring(0, 50) + '...')

    // Auto-save conversation for authenticated users
    if (authStore.isAuthenticated) {
      // Debounce save to avoid too many database calls
      setTimeout(() => saveConversation(), 1000)
    }

    return newMessage
  }

  const updateMessage = (messageId: string, updates: Partial<EnhancedChatMessage>) => {
    const messageIndex = messages.value.findIndex(msg => msg.id === messageId)
    if (messageIndex !== -1) {
      messages.value[messageIndex] = {
        ...messages.value[messageIndex],
        ...updates
      }
    }
  }

  const clearMessages = () => {
    messages.value = []
  }

  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  // Send AI message
  const sendAIMessage = async (messageText: string): Promise<EnhancedChatMessage> => {
    if (!messageText.trim()) {
      throw new Error('Message cannot be empty')
    }

    console.log('🚀 Sending AI message:', messageText.substring(0, 50) + '...')

    setLoading(true)

    try {
      // Add user message
      const userMessage = addMessage({
        role: 'user',
        content: messageText.trim()
      })
      // Get conversation history for context
      const conversationHistory = messageHistory.value.slice(-6) // Last 6 messages

      // Call AI service
      const response: AIChatResponse = await getAiChatService().sendMessage(
        messageText,
        conversationHistory,
        currentRoute.value
      )

      console.log('📨 AI service response:', {
        success: response.success,
        hasMessage: !!response.message,
        messageLength: response.message?.length || 0,
        error: response.error
      })

      if (!response.success || !response.message) {
        throw new Error(response.error || 'AI service returned no response')
      }

      // Add AI response message with action buttons
      const aiMessage = addMessage({
        role: 'assistant',
        content: response.message,
        action_buttons: response.action_buttons
      })

      console.log('✅ AI message added to chat')
      return aiMessage

    } catch (error: any) {
      console.error('💥 Error sending AI message:', error)

      // Add error message
      const errorMessage = addMessage({
        role: 'assistant',
        content: `I apologize, but I'm experiencing technical difficulties right now. 

**Error:** ${error.message}

Please try again in a moment. If the problem persists, you can:
- Refresh the page and try again
- Contact our support team for assistance

I'm here to help once the issue is resolved!`
      })

      throw error
    } finally {
      setLoading(false)

      // Trigger scroll to bottom
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('ai-trigger-scroll'))
      }, 100)
    }
  }

  // Load conversation history for authenticated users
  const loadConversationHistory = async () => {
    if (!authStore.isAuthenticated || isLoadingHistory.value) return

    try {
      isLoadingHistory.value = true
      console.log('📚 Loading conversation history...')

      const { data: conversations, error } = await supabase
        .from('ai_conversations')
        .select('id, messages, created_at, updated_at')
        .eq('user_id', authStore.user?.id)
        .order('updated_at', { ascending: false })
        .limit(1)

      if (error) {
        console.error('Error loading conversation history:', error)
        return
      }

      if (conversations && conversations.length > 0) {
        const latestConversation = conversations[0]
        currentConversationId.value = latestConversation.id

        // Load messages from the latest conversation
        if (latestConversation.messages && Array.isArray(latestConversation.messages)) {
          const historicalMessages = latestConversation.messages.map((msg: any, index: number) => ({
            id: `history-${index}`,
            role: msg.role,
            content: msg.content,
            timestamp: new Date(msg.timestamp || latestConversation.created_at),
            action_buttons: msg.action_buttons || []
          }))

          messages.value = historicalMessages
          console.log(`📚 Loaded ${historicalMessages.length} messages from conversation history`)
        }
      }
    } catch (error) {
      console.error('Failed to load conversation history:', error)
    } finally {
      isLoadingHistory.value = false
    }
  }

  // Save conversation to database
  const saveConversation = async () => {
    if (!authStore.isAuthenticated || messages.value.length === 0) return

    try {
      const conversationData = {
        user_id: authStore.user?.id,
        messages: messages.value.map(msg => ({
          role: msg.role,
          content: msg.content,
          timestamp: msg.timestamp.toISOString(),
          action_buttons: msg.action_buttons || []
        })),
        updated_at: new Date().toISOString()
      }

      if (currentConversationId.value) {
        // Update existing conversation
        const { error } = await supabase
          .from('ai_conversations')
          .update(conversationData)
          .eq('id', currentConversationId.value)

        if (error) {
          console.error('Error updating conversation:', error)
        }
      } else {
        // Create new conversation
        const { data, error } = await supabase
          .from('ai_conversations')
          .insert(conversationData)
          .select('id')
          .single()

        if (error) {
          console.error('Error creating conversation:', error)
        } else if (data) {
          currentConversationId.value = data.id
          console.log('💾 Created new conversation:', data.id)
        }
      }
    } catch (error) {
      console.error('Failed to save conversation:', error)
    }
  }

  // Initialize with welcome message if no messages exist
  const initializeChat = async () => {
    if (authStore.isAuthenticated) {
      // Load conversation history for authenticated users
      await loadConversationHistory()
    }

    // Add welcome message if no messages exist
    if (messages.value.length === 0) {
      const welcomeMessage = authStore.isAuthenticated
        ? `Hello! I'm SmileFactory, your AI Assistant. I can help you navigate the platform, find opportunities, and connect with the right people. What would you like to know?`
        : `Welcome to SmileFactory! I'm your AI Assistant. I can help you learn about our innovation ecosystem and guide you through getting started. How can I assist you today?`

      addMessage({
        role: 'assistant',
        content: welcomeMessage
      })
    }
  }

  return {
    // State
    isOpen,
    isLoading,
    hasUnreadMessage,
    messages,
    currentConversationId,
    isLoadingHistory,

    // Computed
    messageHistory,
    lastMessage,

    // Actions
    setCurrentRoute,
    toggleChat,
    openChat,
    closeChat,
    addMessage,
    updateMessage,
    clearMessages,
    setLoading,
    initializeChat,
    sendAIMessage,
    loadConversationHistory,
    saveConversation
  }
})
