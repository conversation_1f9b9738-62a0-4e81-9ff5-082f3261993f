import type { Ref } from 'vue'
import { ref } from 'vue'
import { useRoute } from 'vue-router'

interface MetaInfo {
  title: string
  description: string
  keywords: string[]
  ogTitle: string
  ogDescription: string
  ogImage: string
  ogType: string
  ogUrl: string
  ogSiteName: string
  twitterCard: string
  twitterTitle: string
  twitterDescription: string
  twitterImage: string
  twitterSite: string
  canonicalUrl: string
  author: string
  language: string
  robots: string
}

const defaultMeta: MetaInfo = {
  title: 'Smile Factory - Fostering Innovation in Zimbabwe',
  description: 'Join Smile Factory, Zimbabwe\'s premier ecosystem for innovators, investors, and entrepreneurs. Connect, collaborate, and grow with our state-of-the-art physical and virtual community.',
  keywords: ['Smile Factory', 'Zimbabwe Innovation', 'Tech Hub', 'Startup Incubator', 'Business Innovation', 'Entrepreneurship', 'Zimbabwe Tech', 'Innovation Hub'],
  ogTitle: 'Smile Factory - Fostering Innovation in Zimbabwe',
  ogDescription: 'Join Smile Factory, Zimbabwe\'s premier ecosystem for innovators, investors, and entrepreneurs. Connect, collaborate, and grow with our state-of-the-art physical and virtual community.',
  ogImage: '/smile-factory-logo.svg',
  ogType: 'website',
  ogUrl: 'https://smilefactory.co.zw',
  ogSiteName: 'Smile Factory',
  twitterCard: 'summary_large_image',
  twitterTitle: 'Smile Factory - Fostering Innovation in Zimbabwe',
  twitterDescription: 'Join Smile Factory, Zimbabwe\'s premier ecosystem for innovators, investors, and entrepreneurs. Connect, collaborate, and grow with our state-of-the-art physical and virtual community.',
  twitterImage: '/smile-factory-logo.svg',
  twitterSite: '@SmileFactory',
  canonicalUrl: 'https://smilefactory.co.zw',
  author: 'Smile Factory',
  language: 'English',
  robots: 'index, follow'
}

export const seoState = ref<MetaInfo>(defaultMeta)

export function updateMetaTags(meta: Partial<MetaInfo>) {
  Object.assign(seoState.value, { ...defaultMeta, ...meta })

  // Update document title
  document.title = seoState.value.title

  // Update basic meta tags
  updateMetaTag('description', seoState.value.description)
  updateMetaTag('keywords', seoState.value.keywords?.join(', ') || '')
  updateMetaTag('author', seoState.value.author)
  updateMetaTag('language', seoState.value.language)
  updateMetaTag('robots', seoState.value.robots)

  // Update Open Graph tags
  updateMetaTag('og:title', seoState.value.ogTitle)
  updateMetaTag('og:description', seoState.value.ogDescription)
  updateMetaTag('og:image', seoState.value.ogImage)
  updateMetaTag('og:type', seoState.value.ogType)
  updateMetaTag('og:url', seoState.value.ogUrl || seoState.value.canonicalUrl)
  updateMetaTag('og:site_name', seoState.value.ogSiteName)

  // Update Twitter Card tags
  updateMetaTag('twitter:card', seoState.value.twitterCard)
  updateMetaTag('twitter:title', seoState.value.twitterTitle)
  updateMetaTag('twitter:description', seoState.value.twitterDescription)
  updateMetaTag('twitter:image', seoState.value.twitterImage)
  updateMetaTag('twitter:site', seoState.value.twitterSite)

  // Update canonical URL
  updateCanonicalUrl(seoState.value.canonicalUrl)
}

function updateMetaTag(name: string, content: string) {
  let element = document.querySelector(`meta[name="${name}"]`) ||
                document.querySelector(`meta[property="${name}"]`)

  if (!element) {
    element = document.createElement('meta')
    element.setAttribute(name.startsWith('og:') ? 'property' : 'name', name)
    document.head.appendChild(element)
  }

  element.setAttribute('content', content)
}

function updateCanonicalUrl(url: string) {
  let canonical = document.querySelector('link[rel="canonical"]')

  if (!canonical) {
    canonical = document.createElement('link')
    canonical.setAttribute('rel', 'canonical')
    document.head.appendChild(canonical)
  }

  canonical.setAttribute('href', url)
}

// Track page view for SEO analytics
function trackPageView() {
  if (window.gtag) {
    window.gtag('event', 'page_view', {
      page_title: document.title,
      page_location: window.location.href,
      page_path: window.location.pathname
    })
  }
}

export function useSEO() {
  const route = useRoute()
  const siteUrl = import.meta.env.VITE_SITE_URL || 'https://smilefactory.co.zw'

  return {
    updateMeta: (meta: Partial<MetaInfo>) => {
      const canonicalUrl = `${siteUrl}${route.path}`

      updateMetaTags({
        ...meta,
        canonicalUrl,
        ogUrl: canonicalUrl
      })

      // Track page view for analytics
      trackPageView()
    }
  }
}

// Export default meta for use in other components
export const getDefaultMeta = () => defaultMeta
