<template>
  <q-page class="legal-page">
    <div class="legal-container">
      <!-- Header Section -->
      <div class="legal-header-wrapper">
        <div class="container">
          <div class="legal-header-card">
            <div class="legal-title-section">
              <h1 class="legal-title">Cookie Preferences</h1>
              <div class="title-divider"></div>
            </div>
            <div class="legal-meta-section">
              <p class="legal-subtitle">
                Last updated: {{ lastUpdated }}
              </p>
              <p class="legal-intro">
                Manage your cookie preferences and control how we use cookies on SmileFactory. You can change these settings at any time.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Content Section -->
      <div class="legal-content">
        <div class="container">
          <!-- Current Settings -->
          <div class="current-settings">
            <h2>Current Cookie Settings</h2>
            <p>Your current cookie preferences are shown below. Changes will take effect immediately.</p>
          </div>

          <!-- <PERSON>ie Categories -->
          <div class="cookie-categories">
            <!-- Essential Cookies -->
            <div class="cookie-category">
              <div class="category-header">
                <div class="category-info">
                  <h3>Essential Cookies</h3>
                  <p>These cookies are necessary for the website to function and cannot be switched off. They are usually only set in response to actions made by you which amount to a request for services.</p>
                  <div class="category-details">
                    <strong>What we use these for:</strong>
                    <ul>
                      <li>Authentication and session management</li>
                      <li>Security and fraud prevention</li>
                      <li>Basic website functionality</li>
                      <li>Remembering your privacy preferences</li>
                    </ul>
                  </div>
                </div>
                <div class="category-toggle">
                  <q-toggle
                    v-model="cookiePreferences.essential"
                    disable
                    color="primary"
                    size="lg"
                  />
                  <div class="toggle-label">Always Active</div>
                </div>
              </div>
            </div>

            <!-- Performance Cookies -->
            <div class="cookie-category">
              <div class="category-header">
                <div class="category-info">
                  <h3>Performance Cookies</h3>
                  <p>These cookies allow us to count visits and traffic sources so we can measure and improve the performance of our site.</p>
                  <div class="category-details">
                    <strong>What we use these for:</strong>
                    <ul>
                      <li>Google Analytics for website analytics</li>
                      <li>Page load time monitoring</li>
                      <li>Error tracking and debugging</li>
                      <li>Feature usage statistics</li>
                    </ul>
                  </div>
                </div>
                <div class="category-toggle">
                  <q-toggle
                    v-model="cookiePreferences.performance"
                    color="primary"
                    size="lg"
                    @update:model-value="updatePreference('performance', $event)"
                  />
                  <div class="toggle-label">{{ cookiePreferences.performance ? 'Enabled' : 'Disabled' }}</div>
                </div>
              </div>
            </div>

            <!-- Functional Cookies -->
            <div class="cookie-category">
              <div class="category-header">
                <div class="category-info">
                  <h3>Functional Cookies</h3>
                  <p>These cookies enable the website to provide enhanced functionality and personalization based on your interactions.</p>
                  <div class="category-details">
                    <strong>What we use these for:</strong>
                    <ul>
                      <li>Language and region preferences</li>
                      <li>Customized content display</li>
                      <li>Saved search filters and preferences</li>
                      <li>User interface customizations</li>
                    </ul>
                  </div>
                </div>
                <div class="category-toggle">
                  <q-toggle
                    v-model="cookiePreferences.functional"
                    color="primary"
                    size="lg"
                    @update:model-value="updatePreference('functional', $event)"
                  />
                  <div class="toggle-label">{{ cookiePreferences.functional ? 'Enabled' : 'Disabled' }}</div>
                </div>
              </div>
            </div>

            <!-- Marketing Cookies -->
            <div class="cookie-category">
              <div class="category-header">
                <div class="category-info">
                  <h3>Marketing Cookies</h3>
                  <p>These cookies are used to deliver advertisements more relevant to you and your interests.</p>
                  <div class="category-details">
                    <strong>What we use these for:</strong>
                    <ul>
                      <li>Personalized content recommendations</li>
                      <li>Marketing campaign effectiveness</li>
                      <li>Social media integration</li>
                      <li>Targeted advertising (when applicable)</li>
                    </ul>
                  </div>
                </div>
                <div class="category-toggle">
                  <q-toggle
                    v-model="cookiePreferences.marketing"
                    color="primary"
                    size="lg"
                    @update:model-value="updatePreference('marketing', $event)"
                  />
                  <div class="toggle-label">{{ cookiePreferences.marketing ? 'Enabled' : 'Disabled' }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="action-buttons">
            <q-btn
              flat
              color="grey-7"
              label="Accept All"
              @click="acceptAll"
              class="action-btn"
            />
            <q-btn
              flat
              color="grey-7"
              label="Reject All"
              @click="rejectAll"
              class="action-btn"
            />
            <q-btn
              unelevated
              color="primary"
              label="Save Preferences"
              @click="savePreferences"
              class="action-btn"
            />
          </div>

          <!-- Additional Information -->
          <div class="additional-info">
            <h2>Additional Information</h2>
            
            <div class="info-section">
              <h3>How to Manage Cookies in Your Browser</h3>
              <p>You can also control cookies through your browser settings:</p>
              <ul>
                <li><strong>Chrome:</strong> Settings > Privacy and security > Cookies and other site data</li>
                <li><strong>Firefox:</strong> Settings > Privacy & Security > Cookies and Site Data</li>
                <li><strong>Safari:</strong> Preferences > Privacy > Manage Website Data</li>
                <li><strong>Edge:</strong> Settings > Cookies and site permissions > Cookies and site data</li>
              </ul>
            </div>

            <div class="info-section">
              <h3>Third-Party Cookies</h3>
              <p>Some cookies are set by third-party services that appear on our pages:</p>
              <ul>
                <li><strong>Google Analytics:</strong> Used for website analytics and performance monitoring</li>
                <li><strong>Social Media:</strong> Integration with social media platforms for sharing content</li>
              </ul>
            </div>

            <div class="info-section">
              <h3>Contact Us</h3>
              <p>If you have questions about our cookie policy or need help managing your preferences:</p>
              <ul>
                <li>Email: <a href="mailto:<EMAIL>"><EMAIL></a></li>
                <li>View our full <router-link to="/legal/gdpr-compliance">GDPR Compliance & Cookie Policy</router-link></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useSEO } from '@/utils/seo'
import { useQuasar } from 'quasar'

// SEO Meta
const { updateMeta } = useSEO()
updateMeta({
  title: 'Cookie Preferences - SmileFactory',
  description: 'Manage your cookie preferences and control how we use cookies on SmileFactory platform.',
  keywords: 'cookie preferences, privacy settings, cookie management, SmileFactory'
})

// Composables
const $q = useQuasar()

// Data
const lastUpdated = ref('January 15, 2025')

// Cookie preferences
const cookiePreferences = ref({
  essential: true,  // Always true, cannot be disabled
  performance: false,
  functional: false,
  marketing: false
})

// Constants
const COOKIE_CONSENT_KEY = 'zb-cookie-consent'
const COOKIE_PREFERENCES_KEY = 'zb-cookie-preferences'

// Methods
function loadCurrentPreferences() {
  const savedPreferences = localStorage.getItem(COOKIE_PREFERENCES_KEY)
  if (savedPreferences) {
    try {
      const preferences = JSON.parse(savedPreferences)
      cookiePreferences.value = { ...cookiePreferences.value, ...preferences }
    } catch (error) {
      console.error('Error parsing cookie preferences:', error)
    }
  }
}

function updatePreference(category: string, value: boolean) {
  (cookiePreferences.value as any)[category] = value
  savePreferences()
}

function acceptAll() {
  cookiePreferences.value = {
    essential: true,
    performance: true,
    functional: true,
    marketing: true
  }
  savePreferences()
  
  $q.notify({
    message: 'All cookies accepted',
    type: 'positive',
    position: 'top',
    timeout: 2000
  })
}

function rejectAll() {
  cookiePreferences.value = {
    essential: true,
    performance: false,
    functional: false,
    marketing: false
  }
  savePreferences()
  
  $q.notify({
    message: 'Only essential cookies will be used',
    type: 'info',
    position: 'top',
    timeout: 2000
  })
}

function savePreferences() {
  // Save consent timestamp
  localStorage.setItem(COOKIE_CONSENT_KEY, new Date().toISOString())
  
  // Save preferences
  localStorage.setItem(COOKIE_PREFERENCES_KEY, JSON.stringify(cookiePreferences.value))
  
  // Apply cookie settings
  applyCookieSettings()
  
  // Emit event for other components to listen to
  window.dispatchEvent(new CustomEvent('cookieConsentUpdated', {
    detail: cookiePreferences.value
  }))
  
  $q.notify({
    message: 'Cookie preferences saved',
    type: 'positive',
    position: 'top',
    timeout: 2000
  })
}

function applyCookieSettings() {
  // Apply Google Analytics based on performance cookies
  if (cookiePreferences.value.performance) {
    enableGoogleAnalytics()
  } else {
    disableGoogleAnalytics()
  }
  
  // Apply other cookie-dependent features
  if (cookiePreferences.value.functional) {
    enableFunctionalCookies()
  }
  
  if (cookiePreferences.value.marketing) {
    enableMarketingCookies()
  }
}

function enableGoogleAnalytics() {
  // Enable Google Analytics if it exists
  if (window.gtag) {
    window.gtag('consent', 'update', {
      'analytics_storage': 'granted'
    })
  }
}

function disableGoogleAnalytics() {
  // Disable Google Analytics if it exists
  if (window.gtag) {
    window.gtag('consent', 'update', {
      'analytics_storage': 'denied'
    })
  }
}

function enableFunctionalCookies() {
  // Enable functional features that depend on cookies
  console.log('Functional cookies enabled')
}

function enableMarketingCookies() {
  // Enable marketing features that depend on cookies
  console.log('Marketing cookies enabled')
}

// Lifecycle
onMounted(() => {
  loadCurrentPreferences()
})
</script>

<style scoped>
.legal-page {
  background: #f5f5f5;
  min-height: 100vh;
}

.legal-container {
  max-width: 1200px;
  margin: 0 auto;
}

.legal-header-wrapper {
  padding: 40px 20px;
  background: transparent;
}

.legal-header-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  max-width: 1000px;
  margin: 0 auto;
}

.legal-title-section {
  text-align: center;
  margin-bottom: 32px;
}

.legal-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: #1f2937;
  line-height: 1.2;
}

.title-divider {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #245926, #4ade80);
  margin: 0 auto;
  border-radius: 2px;
}

.legal-meta-section {
  text-align: center;
}

.legal-subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin-bottom: 20px;
  font-weight: 500;
}

.legal-intro {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #374151;
  max-width: 700px;
  margin: 0 auto;
}

.legal-content {
  padding: 60px 20px;
}

.container {
  max-width: 900px;
  margin: 0 auto;
}

.current-settings {
  background: white;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.current-settings h2 {
  color: #245926;
  font-size: 1.8rem;
  margin-bottom: 16px;
}

.current-settings p {
  color: #6b7280;
  font-size: 1.1rem;
  margin: 0;
}

.cookie-categories {
  display: grid;
  gap: 24px;
  margin-bottom: 40px;
}

.cookie-category {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 32px;
}

.category-info {
  flex: 1;
}

.category-info h3 {
  color: #374151;
  font-size: 1.4rem;
  margin: 0 0 12px 0;
}

.category-info > p {
  color: #6b7280;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 16px 0;
}

.category-details {
  margin-top: 16px;
}

.category-details strong {
  color: #374151;
  font-size: 0.95rem;
}

.category-details ul {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.category-details li {
  color: #6b7280;
  font-size: 0.9rem;
  margin-bottom: 4px;
  line-height: 1.4;
}

.category-toggle {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.toggle-label {
  font-size: 0.85rem;
  color: #6b7280;
  text-align: center;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.action-btn {
  min-width: 140px;
  padding: 12px 24px;
}

.additional-info {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.additional-info h2 {
  color: #245926;
  font-size: 1.8rem;
  margin-bottom: 24px;
}

.info-section {
  margin-bottom: 32px;
}

.info-section:last-child {
  margin-bottom: 0;
}

.info-section h3 {
  color: #374151;
  font-size: 1.3rem;
  margin: 0 0 12px 0;
}

.info-section p {
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 12px 0;
}

.info-section ul {
  margin: 12px 0;
  padding-left: 24px;
}

.info-section li {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 8px;
}

.info-section a {
  color: #6366f1;
  text-decoration: none;
}

.info-section a:hover {
  text-decoration: underline;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .legal-title {
    font-size: 2.2rem;
  }
  
  .legal-header {
    padding: 40px 20px;
  }
  
  .legal-content {
    padding: 40px 16px;
  }
  
  .current-settings,
  .cookie-category,
  .additional-info {
    padding: 24px;
  }
  
  .category-header {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }
  
  .category-toggle {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .action-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .action-btn {
    width: 100%;
    max-width: 200px;
  }
}
</style>
