<template>
  <q-page class="legal-page">
    <div class="legal-container">
      <!-- Header Section -->
      <div class="legal-header-wrapper">
        <div class="container">
          <div class="legal-header-card">
            <div class="legal-title-section">
              <h1 class="legal-title">Frequently Asked Questions</h1>
              <div class="title-divider"></div>
            </div>
            <div class="legal-meta-section">
              <p class="legal-subtitle">
                Last updated: {{ lastUpdated }}
              </p>
              <p class="legal-intro">
                Find answers to common questions about SmileFactory, our platform features, and how to make the most of your experience.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Content Section -->
      <div class="legal-content">
        <div class="container">
          <!-- Search FAQ -->
          <div class="faq-search">
            <q-input
              v-model="searchQuery"
              placeholder="Search frequently asked questions..."
              outlined
              clearable
              class="search-input"
            >
              <template v-slot:prepend>
                <q-icon name="search" />
              </template>
            </q-input>
          </div>

          <!-- FAQ Categories -->
          <div class="faq-categories">
            <q-btn-toggle
              v-model="activeCategory"
              toggle-color="primary"
              :options="categoryOptions"
              class="category-toggle"
            />
          </div>

          <!-- FAQ Content -->
          <div class="faq-content">
            <div v-for="category in filteredCategories" :key="category.id" class="faq-category">
              <h2 class="category-title">{{ category.title }}</h2>
              <p class="category-description">{{ category.description }}</p>
              
              <q-list class="faq-list">
                <q-expansion-item
                  v-for="(faq, index) in category.faqs"
                  :key="index"
                  :label="faq.question"
                  class="faq-item"
                  header-class="faq-question"
                  expand-separator
                  :default-opened="false"
                >
                  <q-card class="faq-answer-card">
                    <q-card-section>
                      <div class="faq-answer" v-html="faq.answer"></div>
                      <div v-if="faq.relatedLinks" class="related-links">
                        <h5>Related Links:</h5>
                        <ul>
                          <li v-for="link in faq.relatedLinks" :key="link.url">
                            <router-link :to="link.url">{{ link.text }}</router-link>
                          </li>
                        </ul>
                      </div>
                    </q-card-section>
                  </q-card>
                </q-expansion-item>
              </q-list>
            </div>
          </div>


        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useSEO } from '@/utils/seo'
import { useQuasar } from 'quasar'

// SEO Meta
const { updateMeta } = useSEO()
updateMeta({
  title: 'FAQ - Frequently Asked Questions | SmileFactory',
  description: 'Find answers to common questions about SmileFactory platform, mentorship, events, community features, and technical support.',
  keywords: 'FAQ, frequently asked questions, help, support, SmileFactory, platform guide'
})

// Composables
const $q = useQuasar()

// Data
const lastUpdated = ref('January 15, 2025')
const searchQuery = ref('')
const activeCategory = ref('all')

const categoryOptions = [
  { label: 'All', value: 'all' },
  { label: 'Getting Started', value: 'getting-started' },
  { label: 'Platform Features', value: 'platform' },
  { label: 'Mentorship', value: 'mentorship' },
  { label: 'Events', value: 'events' },
  { label: 'Community', value: 'community' },
  { label: 'Account & Privacy', value: 'account' },
  { label: 'Technical Support', value: 'technical' }
]

const faqCategories = ref([
  {
    id: 'getting-started',
    title: 'Getting Started',
    description: 'Learn the basics of using SmileFactory',
    faqs: [
      {
        question: 'What is SmileFactory?',
        answer: 'SmileFactory is a comprehensive platform that connects entrepreneurs, innovators, mentors, and investors in Zimbabwe. We provide tools for networking, collaboration, mentorship, event management, and business development to foster sustainable growth in the innovation ecosystem.'
      },
      {
        question: 'How do I create an account?',
        answer: 'Creating an account is simple:<ol><li>Click the "Sign Up" button on our homepage</li><li>Choose your registration method (email, Google, or LinkedIn)</li><li>Complete your profile information</li><li>Verify your email address</li><li>Start exploring the platform!</li></ol>',
        relatedLinks: [
          { text: 'Sign Up Now', url: '/sign-in' },
          { text: 'Privacy Policy', url: '/legal/privacy-policy' }
        ]
      },
      {
        question: 'What types of profiles can I create?',
        answer: 'We support 8 different profile types:<ul><li><strong>Innovator:</strong> For creative problem-solvers and inventors</li><li><strong>Entrepreneur:</strong> For business founders and startup creators</li><li><strong>Student:</strong> For learners and academic researchers</li><li><strong>Mentor:</strong> For experienced professionals offering guidance</li><li><strong>Investor:</strong> For funding providers and financial backers</li><li><strong>Researcher:</strong> For academic and industry researchers</li><li><strong>Professional:</strong> For industry experts and consultants</li><li><strong>Organization:</strong> For companies and institutions</li></ul>'
      },
      {
        question: 'Is SmileFactory free to use?',
        answer: 'Yes! SmileFactory is completely free to use. All core features including profile creation, networking, event participation, and community engagement are available at no cost. We believe in making innovation accessible to everyone.'
      }
    ]
  },
  {
    id: 'platform',
    title: 'Platform Features',
    description: 'Understanding our platform capabilities and tools',
    faqs: [
      {
        question: 'What features are available on the platform?',
        answer: 'Our platform offers comprehensive features:<ul><li><strong>Community Feed:</strong> Share updates, insights, and connect with others</li><li><strong>Profile Directory:</strong> Discover and connect with community members</li><li><strong>Events:</strong> Create, manage, and attend innovation events</li><li><strong>Blog:</strong> Share and read industry insights and articles</li><li><strong>Marketplace:</strong> Find opportunities, services, and collaborations</li><li><strong>Mentorship:</strong> Connect mentors with mentees</li><li><strong>AI-Powered Search:</strong> Find exactly what you need with intelligent search</li></ul>'
      },
      {
        question: 'How does the AI search feature work?',
        answer: 'Our AI-powered search uses advanced text-to-SQL technology to understand your queries in natural language. Simply describe what you\'re looking for (e.g., "Find blockchain mentors in Harare" or "Show me upcoming fintech events") and our AI will find relevant results from our database.'
      },
      {
        question: 'Can I customize my profile visibility?',
        answer: 'Yes! You have full control over your profile visibility:<ul><li>Choose which information to display publicly</li><li>Set your availability for mentorship or collaboration</li><li>Control who can contact you directly</li><li>Manage your notification preferences</li></ul>You can update these settings anytime in your profile preferences.',
        relatedLinks: [
          { text: 'Privacy Settings', url: '/dashboard/profile' },
          { text: 'Privacy Policy', url: '/legal/privacy-policy' }
        ]
      }
    ]
  },
  {
    id: 'mentorship',
    title: 'Mentorship',
    description: 'Everything about our mentorship program',
    faqs: [
      {
        question: 'How does mentorship work on the platform?',
        answer: 'Our mentorship system connects experienced professionals with those seeking guidance:<ul><li><strong>For Mentees:</strong> Browse mentor profiles, send mentorship requests, and participate in mentoring sessions</li><li><strong>For Mentors:</strong> Create your mentor profile, receive and manage mentorship requests, and host mentoring events</li><li><strong>Matching:</strong> Our AI helps match mentors and mentees based on expertise, interests, and goals</li></ul>'
      },
      {
        question: 'What are the requirements to become a mentor?',
        answer: 'To become a mentor, you should have:<ul><li>Relevant professional experience in your field</li><li>A desire to help others grow and succeed</li><li>Time to commit to mentoring relationships</li><li>A complete profile showcasing your expertise</li></ul>There are no formal qualifications required - we value practical experience and willingness to help others.'
      },
      {
        question: 'How do I find a mentor?',
        answer: 'Finding a mentor is easy:<ol><li>Use our AI search to find mentors by expertise, industry, or location</li><li>Browse the mentor directory in the Profiles section</li><li>Review mentor profiles and their areas of expertise</li><li>Send a mentorship request with your goals and expectations</li><li>Wait for the mentor to respond and schedule your first session</li></ol>'
      },
      {
        question: 'Is mentorship free?',
        answer: 'Yes, mentorship connections through our platform are free. However, individual mentors may choose to offer paid services or premium mentoring programs. This is arranged directly between mentors and mentees outside our platform.'
      }
    ]
  },
  {
    id: 'events',
    title: 'Events',
    description: 'Creating, managing, and attending events',
    faqs: [
      {
        question: 'How do I create an event?',
        answer: 'Creating an event is simple:<ol><li>Click the "Create" button in the virtual community</li><li>Select "Event" from the post type options</li><li>Fill in event details (title, description, date, location)</li><li>Add relevant tags and categories</li><li>Set registration requirements if needed</li><li>Publish your event</li></ol>Your event will appear in the events feed and calendar.'
      },
      {
        question: 'Can I host virtual events?',
        answer: 'Absolutely! We support both in-person and virtual events. When creating an event, you can:<ul><li>Specify if it\'s virtual, in-person, or hybrid</li><li>Add video conference links for virtual events</li><li>Include dial-in information and access codes</li><li>Share presentation materials and resources</li></ul>'
      },
      {
        question: 'How do I register for events?',
        answer: 'Event registration is straightforward:<ol><li>Browse events in the Events tab or community feed</li><li>Click on an event that interests you</li><li>Review the event details and requirements</li><li>Click "Register" or "RSVP"</li><li>You\'ll receive confirmation and event updates via email</li></ol>'
      },
      {
        question: 'Can I cancel my event registration?',
        answer: 'Yes, you can cancel your registration:<ul><li>Go to your dashboard and find "My Events"</li><li>Locate the event you want to cancel</li><li>Click "Cancel Registration"</li><li>The event organizer will be notified of your cancellation</li></ul>Please cancel as early as possible to help organizers plan accordingly.'
      }
    ]
  },
  {
    id: 'community',
    title: 'Community',
    description: 'Engaging with the SmileFactory community',
    faqs: [
      {
        question: 'How do I connect with other members?',
        answer: 'There are several ways to connect:<ul><li><strong>Direct Messages:</strong> Send private messages to other members</li><li><strong>Community Posts:</strong> Engage with posts in the community feed</li><li><strong>Event Participation:</strong> Meet people at events and networking sessions</li><li><strong>Collaboration Requests:</strong> Reach out for business partnerships</li><li><strong>Mentorship:</strong> Connect through our mentorship program</li></ul>'
      },
      {
        question: 'What types of content can I share?',
        answer: 'You can share various types of content:<ul><li><strong>General Posts:</strong> Updates, insights, and thoughts</li><li><strong>Blog Articles:</strong> In-depth content and expertise sharing</li><li><strong>Events:</strong> Workshops, meetups, and conferences</li><li><strong>Opportunities:</strong> Job postings, partnerships, and collaborations</li><li><strong>Marketplace Listings:</strong> Services, products, and resources</li></ul>All content should be professional and relevant to innovation and entrepreneurship.'
      },
      {
        question: 'Are there community guidelines I should follow?',
        answer: 'Yes, we have community guidelines to ensure a positive experience for everyone:<ul><li>Be respectful and professional in all interactions</li><li>Share relevant, high-quality content</li><li>Respect intellectual property and give proper attribution</li><li>No spam, self-promotion, or irrelevant content</li><li>Report inappropriate behavior or content</li><li>Follow our Terms and Conditions</li></ul>',
        relatedLinks: [
          { text: 'Terms and Conditions', url: '/legal/terms-conditions' },
          { text: 'Community Guidelines', url: '/legal/community-guidelines' }
        ]
      }
    ]
  },
  {
    id: 'account',
    title: 'Account & Privacy',
    description: 'Managing your account and privacy settings',
    faqs: [
      {
        question: 'How do I update my profile information?',
        answer: 'To update your profile:<ol><li>Go to your Dashboard</li><li>Click on "Profile" or "Edit Profile"</li><li>Update the information you want to change</li><li>Save your changes</li></ol>Your updated information will be reflected across the platform immediately.'
      },
      {
        question: 'How do I change my password?',
        answer: 'To change your password:<ol><li>Go to your account settings</li><li>Click on "Security" or "Password"</li><li>Enter your current password</li><li>Enter your new password twice</li><li>Click "Update Password"</li></ol>We recommend using a strong, unique password for your account security.'
      },
      {
        question: 'How do I delete my account?',
        answer: 'If you wish to delete your account:<ol><li>Contact our support <NAME_EMAIL></li><li>Request account deletion</li><li>We\'ll process your request within 30 days</li><li>All your personal data will be permanently removed</li></ol>Please note that this action cannot be undone.',
        relatedLinks: [
          { text: 'Privacy Policy', url: '/legal/privacy-policy' },
          { text: 'GDPR Rights', url: '/legal/gdpr-compliance' }
        ]
      },
      {
        question: 'What data do you collect about me?',
        answer: 'We collect only the data necessary to provide our services:<ul><li>Profile information you provide</li><li>Usage data to improve the platform</li><li>Communication data for support purposes</li><li>Cookies for functionality and analytics</li></ul>For detailed information, please review our Privacy Policy.',
        relatedLinks: [
          { text: 'Privacy Policy', url: '/legal/privacy-policy' },
          { text: 'GDPR Compliance', url: '/legal/gdpr-compliance' }
        ]
      }
    ]
  },
  {
    id: 'technical',
    title: 'Technical Support',
    description: 'Troubleshooting and technical assistance',
    faqs: [
      {
        question: 'The platform is running slowly. What should I do?',
        answer: 'If you\'re experiencing slow performance:<ul><li>Check your internet connection</li><li>Clear your browser cache and cookies</li><li>Try using a different browser</li><li>Disable browser extensions temporarily</li><li>Restart your browser</li></ul>If the problem persists, please contact our technical support team.'
      },
      {
        question: 'I\'m not receiving email notifications. How do I fix this?',
        answer: 'To resolve email notification issues:<ol><li>Check your spam/junk folder</li><li>Add our email addresses to your safe sender list</li><li>Verify your email address in your profile settings</li><li>Check your notification preferences in settings</li><li>Contact support if the issue continues</li></ol>'
      },
      {
        question: 'Which browsers are supported?',
        answer: 'SmileFactory works best on modern browsers:<ul><li><strong>Recommended:</strong> Chrome, Firefox, Safari, Edge (latest versions)</li><li><strong>Mobile:</strong> Chrome Mobile, Safari Mobile</li><li><strong>Minimum Requirements:</strong> JavaScript enabled, cookies enabled</li></ul>For the best experience, please keep your browser updated to the latest version.'
      },
      {
        question: 'How do I report a bug or technical issue?',
        answer: 'To report technical issues:<ol><li>Email <NAME_EMAIL></li><li>Include a detailed description of the problem</li><li>Mention your browser and operating system</li><li>Include screenshots if helpful</li><li>Describe the steps that led to the issue</li></ol>Our technical team will investigate and respond promptly.'
      }
    ]
  }
])

// Computed
const filteredCategories = computed(() => {
  let categories = faqCategories.value

  // Filter by active category
  if (activeCategory.value !== 'all') {
    categories = categories.filter(cat => cat.id === activeCategory.value)
  }

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    categories = categories.map(category => ({
      ...category,
      faqs: category.faqs.filter(faq => 
        faq.question.toLowerCase().includes(query) ||
        faq.answer.toLowerCase().includes(query)
      )
    })).filter(category => category.faqs.length > 0)
  }

  return categories
})

// Methods
// FAQ filtering and search functionality handled by computed properties
</script>

<style scoped>
.legal-page {
  background: #f5f5f5;
  min-height: 100vh;
}

.legal-container {
  max-width: 1200px;
  margin: 0 auto;
}

.legal-header-wrapper {
  padding: 40px 20px;
  background: transparent;
}

.legal-header-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  max-width: 1000px;
  margin: 0 auto;
}

.legal-title-section {
  text-align: center;
  margin-bottom: 32px;
}

.legal-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: #1f2937;
  line-height: 1.2;
}

.title-divider {
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #245926, #4ade80);
  margin: 0 auto;
  border-radius: 2px;
}

.legal-meta-section {
  text-align: center;
}

.legal-subtitle {
  font-size: 1rem;
  color: #6b7280;
  margin-bottom: 20px;
  font-weight: 500;
}

.legal-intro {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #374151;
  max-width: 700px;
  margin: 0 auto;
}

.legal-content {
  padding: 60px 20px;
}

.container {
  max-width: 900px;
  margin: 0 auto;
}

.faq-search {
  margin-bottom: 32px;
}

.search-input {
  max-width: 500px;
  margin: 0 auto;
}

.faq-categories {
  margin-bottom: 40px;
  text-align: center;
}

.category-toggle {
  flex-wrap: wrap;
  gap: 8px;
}

.faq-content {
  display: grid;
  gap: 40px;
}

.faq-category {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.category-title {
  color: #245926;
  font-size: 2rem;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.category-description {
  color: #6b7280;
  font-size: 1.1rem;
  margin-bottom: 24px;
}

.faq-list {
  margin-top: 24px;
}

.faq-item {
  margin-bottom: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.faq-question {
  font-weight: 600;
  color: #374151;
  padding: 16px 20px;
  background: #f9fafb;
}

.faq-answer-card {
  box-shadow: none;
  border: none;
}

.faq-answer {
  color: #4b5563;
  line-height: 1.7;
}

.faq-answer ul,
.faq-answer ol {
  margin: 16px 0;
  padding-left: 24px;
}

.faq-answer li {
  margin-bottom: 8px;
}

.faq-answer strong {
  color: #374151;
}

.related-links {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.related-links h5 {
  color: #374151;
  margin: 0 0 12px 0;
  font-size: 1rem;
}

.related-links ul {
  margin: 0;
  padding-left: 20px;
}

.related-links li {
  margin-bottom: 6px;
}

.related-links a {
  color: #6366f1;
  text-decoration: none;
}

.related-links a:hover {
  text-decoration: underline;
}



/* Mobile Responsiveness */
@media (max-width: 768px) {
  .legal-title {
    font-size: 2.2rem;
  }
  
  .legal-header {
    padding: 40px 20px;
  }
  
  .legal-content {
    padding: 40px 16px;
  }
  
  .faq-category {
    padding: 24px;
  }
  
  .category-toggle {
    justify-content: center;
  }
}
</style>
