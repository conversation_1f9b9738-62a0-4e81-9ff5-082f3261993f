<template>
  <q-page>
    <div class="about-hero">
      <div class="container q-mx-auto q-px-md">
        <div class="row justify-center">
          <div class="col-10 col-sm-10 col-md-8">
            <div class="text-h2 text-weight-light q-mb-md text-center" style="color: #0D8A3E">About Smilefactory</div>
            <p class="text-body1 text-center q-mb-lg" style="max-width: 800px; margin: 0 auto;">
              Empowering innovators, connecting ideas, and fostering sustainable growth in Zimbabwe and beyond.
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="about-section q-py-xl">
      <div class="container q-mx-auto q-px-md">
        <div class="row justify-center">
          <div class="col-10 col-sm-10 col-md-8">
            <div class="row q-col-gutter-lg">
              <div class="col-12 col-md-6">
                <h2 class="text-h4 text-primary text-weight-light q-mb-md">Our Mission</h2>
                <p class="text-body1 q-mb-md">
                  Smile-Factory is dedicated to fostering innovation and entrepreneurship in Zimbabwe by providing a collaborative ecosystem where ideas can flourish and transform into sustainable businesses.
                </p>
                <p class="text-body1 q-mb-md">
                  We aim to bridge the gap between innovative ideas and successful implementation by connecting entrepreneurs with the resources, mentorship, and funding they need to succeed.
                </p>
              </div>
              <div class="col-12 col-md-6">
                <q-img
                  src="https://images.unsplash.com/photo-1531482615713-2afd69097998?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80"
                  style="height: 300px; border-radius: 8px; object-fit: cover;"
                  @error="handleImageError"
                >
                  <template v-slot:error>
                    <div class="text-center full-height flex flex-center bg-grey-3">
                      <div class="text-grey-7">Loading image...</div>
                    </div>
                  </template>
                </q-img>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="values-section q-py-xl bg-grey-2">
      <div class="container q-mx-auto q-px-md">
        <div class="row justify-center">
          <div class="col-10 col-sm-10 col-md-8">
            <h2 class="text-h4 text-primary text-weight-light q-mb-lg text-center">Our Core Values</h2>
            <div class="row q-col-gutter-lg">
              <div class="col-12 col-md-4">
                <q-card class="value-card">
                  <q-card-section>
                    <div class="text-h5 text-primary q-mb-md">Innovation</div>
                    <p class="text-body1">
                      We believe in the power of innovative thinking to solve complex problems and create sustainable solutions.
                    </p>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col-12 col-md-4">
                <q-card class="value-card">
                  <q-card-section>
                    <div class="text-h5 text-primary q-mb-md">Collaboration</div>
                    <p class="text-body1">
                      We foster a collaborative environment where diverse perspectives come together to create impactful solutions.
                    </p>
                  </q-card-section>
                </q-card>
              </div>
              <div class="col-12 col-md-4">
                <q-card class="value-card">
                  <q-card-section>
                    <div class="text-h5 text-primary q-mb-md">Sustainability</div>
                    <p class="text-body1">
                      We are committed to promoting sustainable business practices that benefit communities and the environment.
                    </p>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>



    <div class="faq-section q-py-xl bg-grey-1">
      <div class="container q-mx-auto q-px-md">
        <div class="row justify-center">
          <div class="col-10 col-sm-10 col-md-8">
            <h2 class="text-h4 text-primary text-weight-light q-mb-lg text-center">Frequently Asked Questions</h2>
            <p class="text-body1 text-center q-mb-lg">Find answers to common questions about SmileFactory, our platform features, and how to make the most of your experience.</p>

            <!-- FAQ Categories -->
            <div class="faq-categories q-mb-lg">
              <q-btn-toggle
                v-model="selectedCategory"
                toggle-color="primary"
                :options="categoryOptions"
                class="full-width"
                spread
                no-caps
                @update:model-value="filterFAQs"
              />
            </div>
            <q-list>
              <q-expansion-item
                v-for="(faq, index) in filteredFAQs"
                :key="index"
                :label="faq.question"
                class="q-mb-md faq-item"
                header-class="text-weight-medium text-grey-8"
                expand-separator
                icon="help_outline"
              >
                <q-card flat bordered>
                  <q-card-section>
                    <div class="text-body1 text-grey-7" v-html="faq.answer"></div>
                    <div v-if="faq.relatedLinks" class="q-mt-md">
                      <q-separator class="q-mb-md" />
                      <div class="text-caption text-grey-6 q-mb-sm">Related Links:</div>
                      <div class="row q-gutter-sm">
                        <q-btn
                          v-for="link in faq.relatedLinks"
                          :key="link.url"
                          :to="link.url"
                          color="primary"
                          outline
                          size="sm"
                          no-caps
                          :label="link.text"
                        />
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </q-expansion-item>
            </q-list>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// FAQ Categories and filtering
const selectedCategory = ref('all')
const categoryOptions = ref([
  { label: 'All', value: 'all' },
  { label: 'Getting Started', value: 'getting-started' },
  { label: 'Platform Features', value: 'platform' },
  { label: 'Community', value: 'community' }
])

// Comprehensive FAQ data
const faqCategories = ref([
  {
    id: 'getting-started',
    title: 'Getting Started',
    faqs: [
      {
        question: 'What is SmileFactory?',
        answer: 'SmileFactory is a comprehensive platform that connects entrepreneurs, innovators, mentors, and investors in Zimbabwe. We provide tools for networking, collaboration, mentorship, event management, and business development to foster sustainable growth in the innovation ecosystem.'
      },
      {
        question: 'How do I create an account?',
        answer: 'Creating an account is simple:<ol><li>Click the "Sign Up" button on our homepage</li><li>Choose your registration method (email, Google, or LinkedIn)</li><li>Complete your profile information</li><li>Verify your email address</li><li>Start exploring the platform!</li></ol>',
        relatedLinks: [
          { text: 'Sign Up Now', url: '/sign-in' },
          { text: 'Privacy Policy', url: '/legal/privacy-policy' }
        ]
      },
      {
        question: 'Is SmileFactory free to use?',
        answer: 'Yes! SmileFactory is completely free to use. All core features including profile creation, networking, event participation, and community engagement are available at no cost. We believe in making innovation accessible to everyone.'
      }
    ]
  },
  {
    id: 'platform',
    title: 'Platform Features',
    faqs: [
      {
        question: 'What features are available on the platform?',
        answer: 'Our platform offers comprehensive features:<ul><li><strong>Community Feed:</strong> Share updates, insights, and connect with others</li><li><strong>Profile Directory:</strong> Discover and connect with community members</li><li><strong>Events:</strong> Create, manage, and attend innovation events</li><li><strong>Blog:</strong> Share and read industry insights and articles</li><li><strong>Marketplace:</strong> Find opportunities, services, and collaborations</li><li><strong>Mentorship:</strong> Connect mentors with mentees</li><li><strong>AI-Powered Search:</strong> Find exactly what you need with intelligent search</li></ul>'
      },
      {
        question: 'How does the AI search feature work?',
        answer: 'Our AI-powered search uses advanced text-to-SQL technology to understand your queries in natural language. Simply describe what you\'re looking for (e.g., "Find blockchain mentors in Harare" or "Show me upcoming fintech events") and our AI will find relevant results from our database.'
      }
    ]
  },
  {
    id: 'community',
    title: 'Community',
    faqs: [
      {
        question: 'How do I connect with other members?',
        answer: 'There are several ways to connect:<ul><li><strong>Direct Messages:</strong> Send private messages to other members</li><li><strong>Community Posts:</strong> Engage with posts in the community feed</li><li><strong>Event Participation:</strong> Meet people at events and networking sessions</li><li><strong>Collaboration Requests:</strong> Reach out for business partnerships</li><li><strong>Mentorship:</strong> Connect through our mentorship program</li></ul>'
      },
      {
        question: 'How can I find mentors in my field?',
        answer: 'Finding the right mentor is easy:<ol><li>Use our AI search to find mentors by expertise, location, or industry</li><li>Browse the mentor directory in the Profiles section</li><li>Attend mentorship events and workshops</li><li>Check mentor availability and specializations</li><li>Send a personalized mentorship request</li></ol>',
        relatedLinks: [
          { text: 'Find Mentors', url: '/innovation-community/profiles' }
        ]
      },
      {
        question: 'Can I host my own events?',
        answer: 'Absolutely! Event hosting is encouraged:<ul><li>Create events through your dashboard</li><li>Choose from various event types (workshops, networking, pitch sessions)</li><li>Set capacity, location, and requirements</li><li>Promote your event to the community</li><li>Manage attendees and follow up after the event</li></ul>'
      }
    ]
  }
])

// Computed property for filtered FAQs
const filteredFAQs = ref([])

// Filter FAQs based on selected category
function filterFAQs() {
  if (selectedCategory.value === 'all') {
    filteredFAQs.value = faqCategories.value.flatMap(category => category.faqs)
  } else {
    const category = faqCategories.value.find(cat => cat.id === selectedCategory.value)
    filteredFAQs.value = category ? category.faqs : []
  }
}

// Initialize with all FAQs
filterFAQs()

// Handle image loading errors
const handleImageError = (event: Event) => {
  console.log('Image failed to load:', event)
  // The template already handles the error display
}
</script>

<style scoped>
.about-hero {
  background: linear-gradient(135deg, #f8fffe 0%, #e8f5f0 100%);
  padding: 80px 0 60px 0;
  position: relative;
}

.about-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80') center/cover;
  opacity: 0.3;
  z-index: 0;
}

.about-hero .container {
  position: relative;
  z-index: 1;
}

.about-section {
  background: #ffffff;
}

.values-section {
  background: #f8f9fa;
}

.faq-section {
  background: #f8f9fa;
}

.value-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.value-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .about-hero {
    padding: 60px 0 40px 0;
  }

  .about-hero .text-h2 {
    font-size: 2rem;
  }
}

/* FAQ styling */
.faq-categories {
  display: flex;
  justify-content: center;
}

.faq-item {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background: white;
  transition: all 0.3s ease;
}

.faq-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.q-expansion-item__content {
  background: #fafafa;
}

@media (max-width: 768px) {
  .faq-categories .q-btn-toggle {
    flex-direction: column;
  }

  .faq-categories .q-btn {
    margin-bottom: 8px;
  }
}

.q-expansion-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>
