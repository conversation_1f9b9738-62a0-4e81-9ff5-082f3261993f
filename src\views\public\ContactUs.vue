<template>
  <q-page>
    <!-- Hero Section with Animated Background -->
    <div class="contact-hero">
      <div class="container q-mx-auto q-px-md">
        <div class="row justify-center">
          <div class="col-10 col-sm-10 col-md-8">
            <div class="text-h2 text-weight-light q-mb-md text-center" style="color: #0D8A3E">Contact Us</div>
            <p class="text-body1 text-center q-mb-lg" style="max-width: 800px; margin: 0 auto;">
              Have questions or want to learn more about ZbInnovation? We'd love to hear from you.
            </p>
          </div>
        </div>
      </div>

      <!-- Animated elements -->
      <div class="floating-circles">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
        <div class="circle circle-4"></div>
      </div>
    </div>

    <!-- Contact Cards Section -->
    <div class="contact-cards-section q-py-xl">
      <div class="container q-mx-auto q-px-md">
        <div class="row justify-center">
          <div class="col-10 col-sm-10 col-md-8">
            <div class="row q-col-gutter-lg">
              <div class="col-12 col-sm-6 col-md-3">
                <q-card class="contact-method-card text-center">
                  <q-card-section>
                    <div class="contact-icon-wrapper">
                      <q-icon name="phone" size="48px" color="primary" />
                    </div>
                    <div class="text-h6 q-mt-md">Call Us</div>
                    <p class="q-mb-none">
                      <a :href="'tel:' + phone" class="text-primary">{{ phone }}</a>
                    </p>
                    <p class="text-caption">Mon-Fri: 8AM-6PM</p>
                  </q-card-section>
                </q-card>
              </div>

              <div class="col-12 col-sm-6 col-md-3">
                <q-card class="contact-method-card text-center">
                  <q-card-section>
                    <div class="contact-icon-wrapper">
                      <q-icon name="email" size="48px" color="primary" />
                    </div>
                    <div class="text-h6 q-mt-md">Email Us</div>
                    <p class="q-mb-none">
                      <a :href="'mailto:' + email" class="text-primary">{{ email }}</a>
                    </p>
                    <p class="text-caption">We respond within 24 hours</p>
                  </q-card-section>
                </q-card>
              </div>

              <div class="col-12 col-sm-6 col-md-3">
                <q-card class="contact-method-card text-center">
                  <q-card-section>
                    <div class="contact-icon-wrapper">
                      <q-icon name="location_on" size="48px" color="primary" />
                    </div>
                    <div class="text-h6 q-mt-md">Visit Us</div>
                    <p class="q-mb-none">4th floor ZB Center</p>
                    <p class="text-caption">Corner First & Kwame, Harare</p>
                  </q-card-section>
                </q-card>
              </div>

              <div class="col-12 col-sm-6 col-md-3">
                <q-card class="contact-method-card text-center">
                  <q-card-section>
                    <div class="contact-icon-wrapper">
                      <q-icon name="call" size="48px" color="primary" />
                    </div>
                    <div class="text-h6 q-mt-md">Toll Free</div>
                    <p class="q-mb-none">Econet: {{ econetTollfree }}</p>
                    <p class="q-mb-none">Telone: {{ teloneTollfree }}</p>
                    <p class="q-mb-none">Netone: {{ netoneTollfree }}</p>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Contact Section -->
    <div class="contact-section q-py-xl">
      <div class="container q-mx-auto q-px-md">
        <div class="row justify-center">
          <div class="col-10 col-sm-10 col-md-8">
            <div class="row q-col-gutter-xl">
              <!-- Contact Form -->
              <div class="col-12 col-lg-6">
                <q-card class="contact-form-card">
                  <q-card-section class="bg-primary text-white">
                    <div class="text-h5">Send Us a Message</div>
                    <div class="text-subtitle2">We'll get back to you as soon as possible</div>
                  </q-card-section>

                  <q-card-section class="q-pt-lg">
                    <q-form @submit="onSubmit" class="q-gutter-md">
                      <div class="row q-col-gutter-md">
                        <div class="col-12 col-sm-6">
                          <q-input
                            filled
                            v-model="form.firstName"
                            label="First Name *"
                            :rules="[val => !!val || 'First name is required']"
                          >
                            <template v-slot:prepend>
                              <q-icon name="person" />
                            </template>
                          </q-input>
                        </div>
                        <div class="col-12 col-sm-6">
                          <q-input
                            filled
                            v-model="form.lastName"
                            label="Last Name *"
                            :rules="[val => !!val || 'Last name is required']"
                          >
                            <template v-slot:prepend>
                              <q-icon name="person" />
                            </template>
                          </q-input>
                        </div>
                      </div>

                      <q-input
                        filled
                        v-model="form.email"
                        label="Email *"
                        type="email"
                        :rules="[
                          val => !!val || 'Email is required',
                          val => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) || 'Please enter a valid email'
                        ]"
                      >
                        <template v-slot:prepend>
                          <q-icon name="email" />
                        </template>
                      </q-input>

                      <q-input
                        filled
                        v-model="form.phone"
                        label="Phone Number"
                        type="tel"
                      >
                        <template v-slot:prepend>
                          <q-icon name="phone" />
                        </template>
                      </q-input>

                      <q-select
                        filled
                        v-model="form.subject"
                        :options="subjectOptions"
                        label="Subject *"
                        :rules="[val => !!val || 'Subject is required']"
                      >
                        <template v-slot:prepend>
                          <q-icon name="subject" />
                        </template>
                      </q-select>

                      <q-input
                        filled
                        v-model="form.message"
                        label="Message *"
                        type="textarea"
                        rows="4"
                        :rules="[val => !!val || 'Message is required']"
                      >
                        <template v-slot:prepend>
                          <q-icon name="message" />
                        </template>
                      </q-input>

                      <div class="row justify-between items-center">
                        <q-checkbox v-model="form.subscribe" label="Subscribe to newsletter" />
                        <q-btn
                          label="Send Message"
                          type="submit"
                          color="primary"
                          :loading="submitting"
                          icon-right="send"
                          class="q-px-md"
                        />
                      </div>
                    </q-form>
                  </q-card-section>
                </q-card>
              </div>

              <!-- Map and Info -->
              <div class="col-12 col-lg-6">
                <q-card class="map-card q-mb-md">
                  <q-card-section class="q-pa-none">
                    <div class="map-container">
                      <div
                        style="width: 100%; height: 300px; background-color: #f5f5f5; display: flex; align-items: center; justify-content: center; position: relative;"
                      >
                        <q-icon name="location_on" size="64px" color="primary" style="position: absolute; z-index: 1;" />
                        <iframe
                          src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3798.1323018492!2d31.0358!3d-17.8252!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1931a4f706172335%3A0x6afb487b82a6a55!2sZB%20Centre%2C%20First%20St%2C%20Harare!5e0!3m2!1sen!2szw!4v1651234567890!5m2!1sen!2szw"
                          width="100%"
                          height="100%"
                          style="border:0;"
                          allowfullscreen=""
                          loading="lazy"
                          referrerpolicy="no-referrer-when-downgrade"
                        ></iframe>
                      </div>
                      <div class="map-overlay">
                        <div class="text-h6 text-white">Find Us</div>
                        <div class="text-subtitle2 text-white">4th floor ZB Center, Corner First & Kwame, Harare</div>
                      </div>
                    </div>
                  </q-card-section>
                </q-card>

                <q-card class="hours-card">
                  <q-card-section>
                    <div class="row items-center q-mb-md">
                      <q-icon name="schedule" size="28px" color="primary" class="q-mr-sm" />
                      <div class="text-h6">Business Hours</div>
                    </div>

                    <q-list>
                      <q-item>
                        <q-item-section>
                          <q-item-label>Monday - Friday</q-item-label>
                        </q-item-section>
                        <q-item-section side>
                          <q-item-label>8:00 AM - 6:00 PM</q-item-label>
                        </q-item-section>
                      </q-item>

                      <q-item>
                        <q-item-section>
                          <q-item-label>Saturday</q-item-label>
                        </q-item-section>
                        <q-item-section side>
                          <q-item-label>9:00 AM - 1:00 PM</q-item-label>
                        </q-item-section>
                      </q-item>

                      <q-item>
                        <q-item-section>
                          <q-item-label>Sunday</q-item-label>
                        </q-item-section>
                        <q-item-section side>
                          <q-item-label>Closed</q-item-label>
                        </q-item-section>
                      </q-item>
                    </q-list>
                  </q-card-section>
                </q-card>

                <q-card class="social-card q-mt-md">
                  <q-card-section>
                    <div class="text-h6 q-mb-md">Connect With Us</div>
                    <div class="social-links row q-col-gutter-sm justify-center">
                      <div class="col-auto">
                        <a :href="socialLinks.twitter" target="_blank" class="social-link">
                          <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M22.46 6c-.77.35-1.6.58-2.46.69c.88-.53 1.56-1.37 1.88-2.38c-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29c0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15c0 1.49.75 2.81 1.91 3.56c-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07a4.28 4.28 0 0 0 4 2.98a8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21C16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56c.84-.6 1.56-1.36 2.14-2.23z'/%3E%3C/svg%3E" size="24px" />
                        </a>
                      </div>
                      <div class="col-auto">
                        <a :href="socialLinks.instagram" target="_blank" class="social-link">
                          <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M7.8 2h8.4C19.4 2 22 4.6 22 7.8v8.4a5.8 5.8 0 0 1-5.8 5.8H7.8C4.6 22 2 19.4 2 16.2V7.8A5.8 5.8 0 0 1 7.8 2m-.2 2A3.6 3.6 0 0 0 4 7.6v8.8C4 18.39 5.61 20 7.6 20h8.8a3.6 3.6 0 0 0 3.6-3.6V7.6C20 5.61 18.39 4 16.4 4H7.6m9.65 1.5a1.25 1.25 0 0 1 1.25 1.25A1.25 1.25 0 0 1 17.25 8 1.25 1.25 0 0 1 16 6.75a1.25 1.25 0 0 1 1.25-1.25M12 7a5 5 0 0 1 5 5 5 5 0 0 1-5 5 5 5 0 0 1-5-5 5 5 0 0 1 5-5m0 2a3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3 3 3 0 0 0-3-3z'/%3E%3C/svg%3E" size="24px" />
                        </a>
                      </div>
                      <div class="col-auto">
                        <a :href="socialLinks.linkedin" target="_blank" class="social-link">
                          <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M19 3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h14m-.5 15.5v-5.3a3.26 3.26 0 0 0-3.26-3.26c-.85 0-1.84.52-2.32 1.3v-1.11h-2.79v8.37h2.79v-4.93c0-.77.62-1.4 1.39-1.4a1.4 1.4 0 0 1 1.4 1.4v4.93h2.79M6.88 8.56a1.68 1.68 0 0 0 1.68-1.68c0-.93-.75-1.69-1.68-1.69a1.69 1.69 0 0 0-1.69 1.69c0 .93.76 1.68 1.69 1.68m1.39 9.94v-8.37H5.5v8.37h2.77z'/%3E%3C/svg%3E" size="24px" />
                        </a>
                      </div>
                      <div class="col-auto">
                        <a :href="socialLinks.youtube" target="_blank" class="social-link">
                          <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M10 15l5.19-3L10 9v6m11.56-7.83c.13.47.22 1.1.28 1.9c.07.8.1 1.49.1 2.09L22 12c0 2.19-.16 3.8-.44 4.83c-.25.9-.83 1.48-1.73 1.73c-.47.13-1.33.22-2.65.28c-1.3.07-2.49.1-3.59.1L12 19c-4.19 0-6.8-.16-7.83-.44c-.9-.25-1.48-.83-1.73-1.73c-.13-.47-.22-1.1-.28-1.9c-.07-.8-.1-1.49-.1-2.09L2 12c0-2.19.16-3.8.44-4.83c.25-.9.83-1.48 1.73-1.73c.47-.13 1.33-.22 2.65-.28c1.3-.07 2.49-.1 3.59-.1L12 5c4.19 0 6.8.16 7.83.44c.9.25 1.48.83 1.73 1.73z'/%3E%3C/svg%3E" size="24px" />
                        </a>
                      </div>
                      <div class="col-auto">
                        <a :href="socialLinks.tiktok" target="_blank" class="social-link">
                          <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M16.6 5.82s.51.5 0 0A4.278 4.278 0 0 1 15.54 3h-3.09v12.4a2.592 2.592 0 0 1-2.59 2.5c-1.42 0-2.6-1.16-2.6-2.6c0-1.72 1.66-3.01 3.37-2.48V9.66c-3.45-.46-6.47 2.22-6.47 5.64c0 3.33 2.76 5.7 5.69 5.7c3.14 0 5.69-2.55 5.69-5.7V9.01a7.35 7.35 0 0 0 4.3 1.38V7.3s-1.88.09-3.24-1.48z'/%3E%3C/svg%3E" size="24px" />
                        </a>
                      </div>
                      <div class="col-auto">
                        <a :href="socialLinks.whatsapp" target="_blank" class="social-link">
                          <q-icon name="img:data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M19.05 4.91A9.816 9.816 0 0 0 12.04 2c-5.46 0-9.91 4.45-9.91 9.91c0 1.75.46 3.45 1.32 4.95L2.05 22l5.25-1.38c1.45.79 3.08 1.21 4.74 1.21c5.46 0 9.91-4.45 9.91-9.91c0-2.65-1.03-5.14-2.9-7.01zM12.04 20.15A8.27 8.27 0 0 1 7.8 18.9l-.3-.18l-3.12.82l.83-3.04l-.2-.31a8.266 8.266 0 0 1-1.26-4.38c0-4.54 3.7-8.24 8.24-8.24a8.24 8.24 0 0 1 5.85 2.42a8.183 8.183 0 0 1 2.41 5.83c.02 4.54-3.68 8.23-8.22 8.23zm4.52-6.16c-.25-.12-1.47-.72-1.69-.81c-.23-.08-.39-.12-.56.12c-.17.25-.64.81-.78.97c-.14.17-.29.19-.54.06c-.25-.12-1.05-.39-1.99-1.23c-.74-.66-1.23-1.47-1.38-1.72c-.14-.25-.02-.38.11-.51c.11-.11.25-.29.37-.43s.17-.25.25-.41c.08-.17.04-.31-.02-.43s-.56-1.34-.76-1.84c-.2-.48-.4-.42-.56-.43h-.48c-.17 0-.43.06-.66.31c-.22.25-.86.84-.86 2.05c0 1.21.88 2.37 1 2.54c.12.17 1.75 2.67 4.23 3.74c.59.26 1.05.41 1.41.53c.59.19 1.13.16 1.56.1c.48-.07 1.47-.6 1.67-1.18c.21-.58.21-1.07.14-1.18s-.22-.16-.47-.28z'/%3E%3C/svg%3E" size="24px" />
                        </a>
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>



    <!-- Newsletter Section -->
    <div class="newsletter-section q-py-xl">
      <div class="container q-mx-auto q-px-md">
        <div class="row justify-center">
          <div class="col-10 col-sm-10 col-md-8">
            <q-card class="newsletter-card">
              <q-card-section class="text-center">
                <q-icon name="email" size="48px" color="primary" />
                <h3 class="text-h5 text-weight-light q-mt-md">Subscribe to Our Newsletter</h3>
                <p class="q-mb-lg">Stay updated with the latest news, events, and opportunities from ZbInnovation</p>

                <div class="row q-col-gutter-md">
                  <div class="col-12 col-sm-8">
                    <q-input filled v-model="newsletterEmail" label="Your Email" type="email" />
                  </div>
                  <div class="col-12 col-sm-4">
                    <q-btn color="primary" label="Subscribe" class="full-width" @click="subscribeToNewsletter" />
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useQuasar } from 'quasar';
import { useContactService } from '@/services/contactService';

const $q = useQuasar();
const contactService = useContactService();
const submitting = ref(false);
const newsletterEmail = ref('');

// Contact information from footer
const email = ref('<EMAIL>');
const phone = ref('+263 8677 002 005');
const econetTollfree = ref('08080 555');
const teloneTollfree = ref('08004 555');
const netoneTollfree = ref('08010 555');

// Social media links from footer
const socialLinks = {
  twitter: 'https://twitter.com/zb_foryou',
  instagram: 'https://www.instagram.com/zb_foryou',
  linkedin: 'https://www.linkedin.com/company/zbforyou',
  youtube: 'https://www.youtube.com/@zb_foryou',
  tiktok: 'https://www.tiktok.com/@zbforyou',
  whatsapp: 'https://wa.me/+263772442685?text=Hello%20Zb'
};

const form = ref({
  firstName: '',
  lastName: '',
  email: '',
  phone: '',
  subject: '',
  message: '',
  subscribe: false
});

const subjectOptions = [
  'General Inquiry',
  'Membership Information',
  'Event Booking',
  'Partnership Opportunities',
  'Feedback',
  'Other'
];

const onSubmit = async () => {
  submitting.value = true;

  try {
    // Submit form data using centralized service
    const result = await contactService.submitContactForm({
      firstName: form.value.firstName,
      lastName: form.value.lastName,
      email: form.value.email,
      phone: form.value.phone,
      subject: form.value.subject,
      message: form.value.message,
      subscribe: form.value.subscribe
    });

    if (!result.success) {
      throw new Error(result.error || 'Failed to submit form');
    }

    // Show success notification
    $q.notify({
      color: 'positive',
      message: 'Your message has been sent successfully. We will get back to you soon.',
      icon: 'check_circle',
      position: 'top',
      timeout: 3000
    });

    // Reset form
    form.value = {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      subject: '',
      message: '',
      subscribe: false
    };
  } catch (error) {
    console.error('Error submitting contact form:', error);

    // Show error notification
    $q.notify({
      color: 'negative',
      message: 'There was an error sending your message. Please try again later.',
      icon: 'error',
      position: 'top',
      timeout: 3000
    });
  } finally {
    submitting.value = false;
  }
};

// Handle newsletter subscription
const subscribeToNewsletter = async () => {
  if (!newsletterEmail.value || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newsletterEmail.value)) {
    $q.notify({
      color: 'warning',
      message: 'Please enter a valid email address',
      icon: 'warning',
      position: 'top',
      timeout: 3000
    });
    return;
  }

  try {
    // Submit newsletter subscription using centralized service
    const result = await contactService.subscribeToNewsletter(
      newsletterEmail.value,
      'contact_page'
    );

    if (!result.success) {
      // If it's already subscribed, show a different message
      if (result.error?.includes('already subscribed')) {
        $q.notify({
          color: 'info',
          message: 'This email is already subscribed to our newsletter.',
          icon: 'info',
          position: 'top',
          timeout: 3000
        });
        newsletterEmail.value = '';
        return;
      }
      throw new Error(result.error || 'Failed to subscribe to newsletter');
    }

    // Show success notification
    $q.notify({
      color: 'positive',
      message: 'You have been subscribed to our newsletter!',
      icon: 'check_circle',
      position: 'top',
      timeout: 3000
    });

    // Reset email
    newsletterEmail.value = '';
  } catch (error) {
    console.error('Error subscribing to newsletter:', error);

    // Show error notification
    $q.notify({
      color: 'negative',
      message: 'There was an error subscribing to the newsletter. Please try again later.',
      icon: 'error',
      position: 'top',
      timeout: 3000
    });
  }
};
</script>

<style scoped>
.container {
  width: 100%;
  max-width: 1400px;
}

/* Hero Section */
.contact-hero {
  padding: 80px 0;
  background-color: #f5f5f5;
  position: relative;
  overflow: hidden;
}

/* Animated Circles */
.floating-circles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
  background-color: #0D8A3E;
}

.circle-1 {
  width: 150px;
  height: 150px;
  top: 10%;
  left: 10%;
  animation: float 8s infinite ease-in-out;
}

.circle-2 {
  width: 100px;
  height: 100px;
  top: 20%;
  right: 15%;
  animation: float 12s infinite ease-in-out reverse;
}

.circle-3 {
  width: 70px;
  height: 70px;
  bottom: 15%;
  left: 20%;
  animation: float 10s infinite ease-in-out 2s;
}

.circle-4 {
  width: 120px;
  height: 120px;
  bottom: 10%;
  right: 10%;
  animation: float 15s infinite ease-in-out 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(-15px) translateX(15px);
  }
  50% {
    transform: translateY(10px) translateX(-10px);
  }
  75% {
    transform: translateY(15px) translateX(5px);
  }
}

/* Contact Cards Section */
.contact-cards-section {
  padding: 60px 0;
  margin-top: -40px;
  position: relative;
  z-index: 1;
}

.contact-method-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 12px;
}

.contact-method-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 20px rgba(13, 138, 62, 0.1);
}

.contact-icon-wrapper {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: rgba(13, 138, 62, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

/* Main Contact Section */
.contact-section {
  padding: 60px 0;
}

.contact-form-card, .map-card, .hours-card, .social-card {
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-form-card:hover, .map-card:hover, .hours-card:hover, .social-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.map-container {
  position: relative;
}

.map-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 15px;
  background: linear-gradient(to top, rgba(13, 138, 62, 0.8), transparent);
}

/* FAQ Section */
.faq-section {
  padding: 60px 0;
  background-color: #f5f5f5;
}

/* Newsletter Section */
.newsletter-section {
  padding: 60px 0;
  background-color: #fff;
}

.newsletter-card {
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.newsletter-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* Social Media Styles (from footer) */
.social-links {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.social-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(13, 138, 62, 0.1);
  transition: all 0.3s ease;
}

.social-link:hover {
  background: rgba(13, 138, 62, 0.2);
  transform: translateY(-2px);
}

/* Common Styles */
.text-primary {
  color: #0D8A3E !important;
}

/* Responsive Adjustments */
@media (max-width: 767px) {
  .contact-cards-section {
    margin-top: 0;
  }

  .contact-method-card {
    margin-bottom: 20px;
  }

  .social-links {
    justify-content: center;
  }
}
</style>
