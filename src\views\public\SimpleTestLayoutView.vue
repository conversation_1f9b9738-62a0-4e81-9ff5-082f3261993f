<template>
  <q-layout view="lHh LpR lFf" class="test-layout">
    <!-- Fixed Header -->
    <q-header elevated class="glass-header">
      <q-toolbar>
        <q-btn flat round dense icon="menu" @click="toggleLeftDrawer" />
        <q-toolbar-title>New Virtual Community Layout Test</q-toolbar-title>
        <q-btn flat round dense icon="notifications" />
      </q-toolbar>
    </q-header>

    <!-- Left Drawer -->
    <q-drawer
      v-model="leftDrawerOpen"
      side="left"
      :width="280"
      :breakpoint="1024"
      bordered
      class="left-drawer"
    >
      <q-list>
        <q-item-label header>Navigation</q-item-label>
        <q-item 
          v-for="tab in tabs" 
          :key="tab.value"
          clickable 
          :active="activeTab === tab.value"
          @click="setActiveTab(tab.value)"
        >
          <q-item-section avatar>
            <q-icon :name="tab.icon" />
          </q-item-section>
          <q-item-section>{{ tab.label }}</q-item-section>
        </q-item>
      </q-list>
    </q-drawer>

    <!-- Right Drawer -->
    <q-drawer
      v-model="rightDrawerOpen"
      side="right"
      :width="320"
      :breakpoint="1200"
      bordered
      class="right-drawer"
    >
      <q-list>
        <q-item-label header>Community Info</q-item-label>
        <q-item>
          <q-item-section>
            <q-item-label>Members: 1,234</q-item-label>
            <q-item-label caption>Posts: 5,678</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </q-drawer>

    <!-- Page Container -->
    <q-page-container class="page-container">
      <q-page class="main-page">
        <div class="page-content">
          <!-- Featured Section -->
          <div class="featured-section">
            <q-card class="featured-card">
              <q-card-section>
                <h3>Featured Content</h3>
                <p>This is the featured content for the {{ activeTab }} tab</p>
              </q-card-section>
            </q-card>
          </div>

          <!-- Main Content -->
          <div class="main-content">
            <q-card>
              <q-card-section>
                <h2>{{ activeTab.charAt(0).toUpperCase() + activeTab.slice(1) }} Content</h2>
                <p>This is the main content area for the {{ activeTab }} tab.</p>
                <p><strong>🎉 The new layout is working!</strong></p>
                
                <div class="demo-grid">
                  <q-card v-for="i in 6" :key="i" flat bordered class="demo-card">
                    <q-card-section>
                      <h6>Sample {{ activeTab }} Item {{ i }}</h6>
                      <p>This is a placeholder for {{ activeTab }} content item {{ i }}.</p>
                    </q-card-section>
                  </q-card>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// Composables
const route = useRoute()
const router = useRouter()

// State
const leftDrawerOpen = ref(false)
const rightDrawerOpen = ref(true)

// Computed
const activeTab = computed(() => {
  return route.query.tab as string || 'feed'
})

// Data
const tabs = [
  { label: 'Feed', value: 'feed', icon: 'rss_feed' },
  { label: 'Profiles', value: 'profiles', icon: 'people' },
  { label: 'Events', value: 'events', icon: 'event' },
  { label: 'Blog', value: 'blog', icon: 'article' },
  { label: 'Marketplace', value: 'marketplace', icon: 'storefront' },
  { label: 'Groups', value: 'groups', icon: 'groups' }
]

// Methods
function toggleLeftDrawer() {
  leftDrawerOpen.value = !leftDrawerOpen.value
}

function setActiveTab(tab: string) {
  router.push({ 
    path: '/simple-test', 
    query: { ...route.query, tab } 
  })
  
  // Close mobile drawer
  if (window.innerWidth < 1024) {
    leftDrawerOpen.value = false
  }
}
</script>

<style scoped>
.test-layout {
  background: #f8fafc;
}

.glass-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.left-drawer,
.right-drawer {
  background: white;
}

.page-container {
  background: #f8fafc;
}

.main-page {
  min-height: calc(100vh - 64px);
}

.page-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px;
}

.featured-section {
  margin-bottom: 24px;
}

.featured-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-top: 24px;
}

.demo-card {
  height: fit-content;
}

/* Responsive adjustments */
@media (max-width: 1023px) {
  .page-content {
    padding: 8px;
  }
}

@media (max-width: 767px) {
  .page-content {
    padding: 4px;
  }
}
</style>
