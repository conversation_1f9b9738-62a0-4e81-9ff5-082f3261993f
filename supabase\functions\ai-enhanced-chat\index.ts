import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

// DeepSeek API Configuration
const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';
const DEEPSEEK_API_KEY = Deno.env.get('DEEPSEEK_API_KEY') || '***********************************';

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Initialize AI session for embeddings
const embeddingModel = new Supabase.ai.Session('gte-small');

// Type definitions
interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  metadata?: Record<string, any>;
}

interface UserContext {
  is_authenticated: boolean;
  user_id?: string;
  profile_type?: string;
  profile_completion?: number;
  current_page?: string;
  profile_data?: any;
}

interface AIChatRequest {
  message: string;
  conversation_id?: string;
  user_context: UserContext;
  include_context?: boolean;
  stream?: boolean;
}

interface AIAction {
  type: 'navigation' | 'action' | 'external';
  label: string;
  icon?: string;
  url?: string;
  action?: string;
  color?: string;
}

interface AIChatResponse {
  response: string;
  conversation_id: string;
  actions?: AIAction[];
  suggestions?: string[];
  metadata?: Record<string, any>;
}

// Generate embeddings using Supabase AI
async function generateEmbedding(text: string): Promise<number[]> {
  try {
    console.log('Generating embedding for text:', text.substring(0, 100) + '...');

    // Use Supabase AI for embedding generation
    const embedding = await embeddingModel.run(text.substring(0, 8000), {
      mean_pool: true,
      normalize: true,
    });

    console.log('Embedding generated successfully, dimensions:', embedding.length);
    return Array.from(embedding);
  } catch (error) {
    console.error('Error generating embedding:', error);
    // Return zero vector as fallback (384 dimensions for gte-small)
    return new Array(384).fill(0);
  }
}

// Store conversation and message in vector database
async function storeConversation(
  userId: string,
  conversationId: string,
  userMessage: string,
  assistantResponse: string,
  context: any,
  actions?: AIAction[],
  suggestions?: string[]
): Promise<void> {
  try {
    // Generate embeddings for both messages
    const userEmbedding = await generateEmbedding(userMessage);
    const assistantEmbedding = await generateEmbedding(assistantResponse);

    // Store user message
    await supabase.from('ai_messages').insert({
      conversation_id: conversationId,
      role: 'user',
      content: userMessage,
      content_embedding: userEmbedding,
      metadata: {
        context,
        timestamp: new Date().toISOString()
      }
    });

    // Store assistant response
    await supabase.from('ai_messages').insert({
      conversation_id: conversationId,
      role: 'assistant',
      content: assistantResponse,
      content_embedding: assistantEmbedding,
      metadata: {
        context,
        actions: actions || [],
        suggestions: suggestions || [],
        timestamp: new Date().toISOString()
      }
    });

    // Update conversation summary
    const conversationSummary = `User asked: ${userMessage.substring(0, 100)}... Assistant responded about: ${assistantResponse.substring(0, 100)}...`;
    const summaryEmbedding = await generateEmbedding(conversationSummary);

    await supabase.from('ai_conversations').upsert({
      id: conversationId,
      user_id: userId,
      title: userMessage.substring(0, 50) + '...',
      summary: conversationSummary,
      summary_embedding: summaryEmbedding,
      context_data: context,
      message_count: 2, // Will be updated by triggers
      last_message_at: new Date().toISOString()
    });

    console.log('Conversation stored successfully');
  } catch (error) {
    console.error('Failed to store conversation:', error);
  }
}

// Retrieve relevant conversation context using vector similarity
async function getRelevantContext(userId: string, query: string, limit: number = 5): Promise<any[]> {
  try {
    const queryEmbedding = await generateEmbedding(query);

    const { data, error } = await supabase.rpc('search_similar_messages', {
      query_embedding: queryEmbedding,
      match_threshold: 0.7,
      match_count: limit
    });

    if (error) {
      console.warn('Error retrieving relevant context:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.warn('Error in getRelevantContext:', error);
    return [];
  }
}

// System prompt builder for enhanced AI responses

const buildEnhancedSystemPrompt = (userContext: any): string => {
  let prompt = `You are ZbInnovation AI Assistant, an intelligent companion for Zimbabwe's premier digital innovation ecosystem.

PLATFORM CONTEXT:
ZbInnovation connects innovators, investors, mentors, professionals, academic institutions, industry experts, and organizations. The platform facilitates:
- Intelligent matchmaking between different user types
- Innovation project collaboration and funding opportunities
- Mentorship connections and knowledge sharing
- Community building and networking

YOUR ENHANCED CAPABILITIES:
- Provide deeply personalized guidance based on comprehensive user profile analysis
- Suggest specific platform actions with clickable buttons
- Offer contextual recommendations based on user insights and behavior
- Guide users through platform features and optimization strategies
- Provide data-driven insights about user's platform presence and growth
- Adapt responses based on authentication status and user journey stage
- Provide profile-type-specific guidance and recommendations

RESPONSE FORMAT:
- Always provide helpful, actionable advice tailored to the user's specific situation
- Include specific call-to-action buttons when relevant using [ACTION:type:label:icon:url/action:color] format
- Suggest next steps based on detailed user analysis and insights
- Be encouraging and supportive of innovation efforts
- Reference specific user data when providing recommendations
- Keep responses concise but comprehensive and personalized
- Adapt tone and content based on user's platform familiarity and journey stage

USER CONTEXT:`;

  if (!userContext.is_authenticated) {
    prompt += `
- User is NOT authenticated
- Journey Stage: new_visitor
- Platform Familiarity: new
- Encourage sign-up/login to access full platform features
- Provide general platform information and benefits
- Include login/signup action buttons in responses
- Focus on platform value proposition and community benefits
- Highlight success stories and community achievements
- Emphasize the value of joining the innovation ecosystem`;
  } else {
    prompt += `
- User is authenticated
- Profile Type: ${userContext.profile_type || 'Not specified'}
- Profile Completion: ${userContext.profile_completion || 0}%
- Profile State: ${userContext.profile_state || 'Unknown'}
- Journey Stage: ${userContext.journey_stage || 'unknown'}
- Platform Familiarity: ${userContext.platform_familiarity || 'new'}
- Has Multiple Profiles: ${userContext.has_multiple_profiles || false}
- Onboarding Completed: ${userContext.onboarding_completed || false}
- Current Page: ${userContext.current_page || 'Unknown'}`;

    // Add interests if available
    if (userContext.interests && userContext.interests.length > 0) {
      prompt += `
- User Interests: ${userContext.interests.slice(0, 5).join(', ')}`;
    }

    // Add recent activity context
    if (userContext.recent_activity && userContext.recent_activity.length > 0) {
      prompt += `
- Recent Activity: ${userContext.recent_activity.join(', ')}`;
    }

    // Add enhanced profile analysis if available
    if (userContext.profile_analysis || userContext.completion_stage) {
      prompt += `
- PROFILE ANALYSIS:`;

      if (userContext.completion_stage) {
        prompt += `
  * Completion Stage: ${userContext.completion_stage} (${userContext.profile_completion || 0}% complete)`;
      }

      if (userContext.missing_critical_fields && userContext.missing_critical_fields.length > 0) {
        prompt += `
  * Missing Critical Fields: ${userContext.missing_critical_fields.slice(0, 3).join(', ')}`;
      }

      if (userContext.profile_strengths && userContext.profile_strengths.length > 0) {
        prompt += `
  * Profile Strengths: ${userContext.profile_strengths.join(', ')}`;
      }

      if (userContext.next_steps && userContext.next_steps.length > 0) {
        prompt += `
  * AI Recommended Next Steps: ${userContext.next_steps.slice(0, 3).join(', ')}`;
      }

      if (userContext.milestone_achieved) {
        prompt += `
  * Recent Milestone: ${userContext.milestone_achieved} - Celebrate this achievement!`;
      }
    } else if (userContext.profile_completion !== undefined) {
      // Fallback to basic profile completion insights
      prompt += `
- PROFILE INSIGHTS:`;

      if (userContext.profile_completion < 30) {
        prompt += `
  * Profile Status: Just getting started (${userContext.profile_completion}% complete)
  * Priority: Help user complete basic profile information`;
      } else if (userContext.profile_completion < 70) {
        prompt += `
  * Profile Status: Building profile (${userContext.profile_completion}% complete)
  * Priority: Guide user to add more detailed information`;
      } else if (userContext.profile_completion < 100) {
        prompt += `
  * Profile Status: Almost complete (${userContext.profile_completion}% complete)
  * Priority: Help user finish final profile details`;
      } else {
        prompt += `
  * Profile Status: Complete and optimized (${userContext.profile_completion}% complete)
  * Priority: Focus on networking and platform engagement`;
      }
    }

    // Add detailed context if available
    if (userContext.detailed_context) {
      const stats = userContext.detailed_context.stats;
      const insights = userContext.detailed_context.insights;
      const profile = userContext.detailed_context.profile;

      if (stats) {
        prompt += `
- DETAILED STATS:
  * Posts Created: ${stats.posts_count}
  * Connections: ${stats.connections_count}
  * Likes Received: ${stats.likes_received}
  * Comments Received: ${stats.comments_received}
  * Platform Activity: ${stats.activity_count} actions`;

        if (stats.join_date) {
          const joinDate = new Date(stats.join_date);
          const monthsAgo = Math.floor((Date.now() - joinDate.getTime()) / (1000 * 60 * 60 * 24 * 30));
          prompt += `
  * Member Since: ${monthsAgo} months ago`;
        }
      }

      if (insights) {
        prompt += `
- USER INSIGHTS:
  * Profile Strength: ${insights.profile_strength}
  * Engagement Level: ${insights.engagement_level}
  * Network Size: ${insights.network_size}
  * Content Activity: ${insights.content_activity}`;

        if (insights.recommendations && insights.recommendations.length > 0) {
          prompt += `
  * AI Recommendations: ${insights.recommendations.join(', ')}`;
        }
      }

      if (profile?.first_name) {
        prompt += `
- User Name: ${profile.first_name}${profile.last_name ? ' ' + profile.last_name : ''}`;
      }
    }

    // Priority guidance based on profile completion and insights
    if (userContext.profile_completion < 50) {
      prompt += `
- PRIORITY: Encourage profile completion for better platform experience and visibility`;
    }

    if (userContext.detailed_context?.insights) {
      const insights = userContext.detailed_context.insights;

      if (insights.profile_strength === 'low') {
        prompt += `
- PRIORITY: Profile needs improvement - suggest specific profile enhancements`;
      }

      if (insights.content_activity === 'inactive') {
        prompt += `
- PRIORITY: User hasn't created content - encourage first post creation`;
      }

      if (insights.network_size === 'small') {
        prompt += `
- PRIORITY: Small network - suggest connection building strategies`;
      }
    }

    if (userContext.profile_type) {
      prompt += `
- Tailor responses for ${userContext.profile_type} specific needs and goals`;

      // Add profile type specific guidance
      switch (userContext.profile_type) {
        case 'innovator':
          prompt += `
- Focus on: Innovation sharing, investor connections, mentorship opportunities, idea validation`;
          break;
        case 'investor':
          prompt += `
- Focus on: Innovation discovery, due diligence, portfolio building, startup evaluation`;
          break;
        case 'mentor':
          prompt += `
- Focus on: Mentee connections, knowledge sharing, community leadership, expertise sharing`;
          break;
        case 'student':
          prompt += `
- Focus on: Learning opportunities, skill development, networking, career guidance`;
          break;
        case 'professional':
          prompt += `
- Focus on: Industry connections, collaboration opportunities, knowledge exchange`;
          break;
        case 'academic':
          prompt += `
- Focus on: Research collaboration, student mentoring, knowledge dissemination`;
          break;
        case 'government':
          prompt += `
- Focus on: Policy insights, public-private partnerships, innovation ecosystem development`;
          break;
        case 'organization':
          prompt += `
- Focus on: Partnership opportunities, talent acquisition, innovation scouting`;
          break;
      }
    }

    // Add journey stage specific guidance
    if (userContext.journey_stage) {
      switch (userContext.journey_stage) {
        case 'new_user':
        case 'just_signed_up':
          prompt += `
- JOURNEY GUIDANCE: Welcome new user, provide platform orientation, encourage profile setup`;
          break;
        case 'getting_started':
          prompt += `
- JOURNEY GUIDANCE: Guide through basic platform features, encourage profile completion`;
          break;
        case 'building_profile':
          prompt += `
- JOURNEY GUIDANCE: Focus on profile optimization, suggest missing sections`;
          break;
        case 'almost_complete':
          prompt += `
- JOURNEY GUIDANCE: Encourage final profile steps, prepare for active engagement`;
          break;
        case 'active_user':
          prompt += `
- JOURNEY GUIDANCE: Focus on advanced features, networking, and platform optimization`;
          break;
      }
    }

    // Add platform familiarity specific guidance
    if (userContext.platform_familiarity) {
      switch (userContext.platform_familiarity) {
        case 'new':
          prompt += `
- FAMILIARITY: Explain features clearly, provide step-by-step guidance, use simple language`;
          break;
        case 'intermediate':
          prompt += `
- FAMILIARITY: Provide moderate detail, suggest advanced features, assume basic knowledge`;
          break;
        case 'experienced':
          prompt += `
- FAMILIARITY: Focus on advanced features, optimization tips, assume platform knowledge`;
          break;
      }
    }

    // Add page-specific context
    switch (userContext.current_page) {
      case 'dashboard':
        prompt += `
- User is on dashboard - focus on profile management, recent activity, and next steps`;
        break;
      case 'community':
        prompt += `
- User is on community page - focus on content creation, connections, and engagement`;
        break;
      case 'profile':
        prompt += `
- User is on profile page - focus on profile optimization and visibility`;
        break;
      case 'landing':
        prompt += `
- User is on landing page - focus on platform exploration and getting started`;
        break;
    }
  }

  prompt += `

AVAILABLE ACTIONS (use these in your responses based on user authentication status and journey stage):

${userContext.is_authenticated ? `
AUTHENTICATED USER ACTIONS (prioritize based on journey stage and profile completion):

${(userContext.profile_completion || 0) < 50 ? `
HIGH PRIORITY (Profile Incomplete):
- [ACTION:navigation:Complete Profile:person:/dashboard/profile:primary]
- [ACTION:navigation:Profile Setup Guide:help:/dashboard/profile/${userContext.user_id || 'edit'}/edit:accent]
` : ''}

${userContext.journey_stage === 'new_user' || userContext.journey_stage === 'just_signed_up' ? `
NEW USER PRIORITY:
- [ACTION:navigation:Platform Tour:explore:/dashboard:primary]
- [ACTION:navigation:Getting Started:rocket_launch:/dashboard/help:secondary]
` : ''}

CORE ACTIONS:
- [ACTION:navigation:View Dashboard:dashboard:/dashboard:primary]
- [ACTION:navigation:Explore Community:groups:/virtual-community?tab=feed:secondary]
- [ACTION:navigation:View Profiles:people:/virtual-community?tab=profiles:info]

${(userContext.profile_completion || 0) >= 70 ? `
ADVANCED ACTIONS (Profile Complete):
- [ACTION:action:Create Post:edit:create-post:accent]
- [ACTION:action:Find Connections:people:find-connections:positive]
- [ACTION:action:Join Groups:group_add:join-groups:info]
` : ''}

PROFILE-SPECIFIC ACTIONS:
${userContext.profile_type === 'innovator' ? `
- [ACTION:action:Find Investors:attach_money:find-investors:positive]
- [ACTION:action:Find Mentors:school:find-mentors:secondary]
- [ACTION:action:Share Innovation:lightbulb:share-innovation:accent]
` : ''}
${userContext.profile_type === 'investor' ? `
- [ACTION:action:Discover Startups:rocket_launch:discover-startups:positive]
- [ACTION:action:View Opportunities:trending_up:view-opportunities:accent]
- [ACTION:action:Portfolio Management:business:portfolio:secondary]
` : ''}
${userContext.profile_type === 'mentor' ? `
- [ACTION:action:Find Mentees:school:find-mentees:positive]
- [ACTION:action:Share Expertise:psychology:share-expertise:accent]
- [ACTION:action:Join Mentor Network:people:mentor-network:secondary]
` : ''}

UTILITY ACTIONS:
- [ACTION:navigation:View Activity:timeline:/dashboard/activity:secondary]
- [ACTION:navigation:View Connections:people:/dashboard/connections:info]
- [ACTION:external:Platform Help:help:https://zbinnovation.com/help:grey]
` : `
UNAUTHENTICATED USER ACTIONS:
- [ACTION:action:Sign Up Now:person_add:signup:primary]
- [ACTION:action:Sign In:login:signin:secondary]
- [ACTION:action:Join Innovation Community:groups:join-community:positive]
- [ACTION:navigation:Explore Community:groups:/innovation-community?tab=feed:info]
- [ACTION:navigation:View Success Stories:star:/innovation-community?tab=blog:accent]
- [ACTION:navigation:View Profiles:people:/innovation-community?tab=profiles:info]
- [ACTION:external:Learn About Platform:info:https://zbinnovation.com:grey]
`}

IMPORTANT: Only suggest actions appropriate for the user's authentication status. Do NOT suggest authenticated-only actions (like Create Post, Find Mentors, Complete Profile) to unauthenticated users.

TONE: Professional, encouraging, knowledgeable about African innovation, and specifically familiar with Zimbabwe's business environment.

Always include relevant action buttons to help users take immediate next steps.`;

  return prompt;
};

Deno.serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  console.log('AI Enhanced Chat function called');

  try {
    const requestBody = await req.json();
    console.log('AI Chat request:', requestBody);

    const {
      message,
      conversation_id,
      user_context,
      include_context = true,
      stream = false
    }: AIChatRequest = requestBody;

    // Validate message
    if (!message || typeof message !== 'string') {
      console.error('Invalid message:', message);
      return new Response(
        JSON.stringify({ error: 'Message is required and must be a string' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Generate or use existing conversation ID
    const currentConversationId = conversation_id || crypto.randomUUID();

    // Get conversation context if conversation_id exists
    let conversationContext: any[] = [];
    if (conversation_id && user_context.is_authenticated) {
      try {
        const { data: contextData, error: contextError } = await supabase.rpc('get_ai_conversation_context', {
          conversation_id_param: conversation_id,
          message_limit: 10
        });

        if (!contextError && contextData && contextData.length > 0) {
          const context = contextData[0];
          conversationContext = context.messages || [];
          console.log('Retrieved conversation context:', conversationContext.length, 'messages');
        }
      } catch (error) {
        console.warn('Error retrieving conversation context:', error);
      }
    }

    // Get relevant context using vector similarity if authenticated
    let relevantContext: any[] = [];
    if (user_context.is_authenticated && user_context.user_id && include_context) {
      try {
        relevantContext = await getRelevantContext(user_context.user_id, message, 3);
        console.log('Retrieved relevant context:', relevantContext.length, 'items');
      } catch (error) {
        console.warn('Error retrieving relevant context:', error);
      }
    }

    // Build enhanced system prompt
    const systemPrompt = buildEnhancedSystemPrompt(user_context);
    console.log('Built system prompt for user:', {
      is_authenticated: user_context.is_authenticated,
      profile_type: user_context.profile_type,
      current_page: user_context.current_page
    });

    // Build conversation messages with context
    const messages: ChatMessage[] = [
      { role: 'system', content: systemPrompt }
    ];

    // Add relevant context if available
    if (relevantContext.length > 0) {
      const contextMessage = `Previous relevant conversations:\n${relevantContext.map(ctx =>
        `- ${ctx.content.substring(0, 100)}...`
      ).join('\n')}`;
      messages.push({ role: 'system', content: contextMessage });
    }

    // Add conversation history
    if (conversationContext.length > 0) {
      conversationContext.forEach(msg => {
        if (msg.role && msg.content) {
          messages.push({ role: msg.role, content: msg.content });
        }
      });
    }

    // Add current user message
    messages.push({ role: 'user', content: message });

    console.log('Calling DeepSeek API');

    // Validate API key
    if (!DEEPSEEK_API_KEY || DEEPSEEK_API_KEY === 'your-api-key-here') {
      throw new Error('DeepSeek API key not configured properly');
    }

    // Determine if streaming is requested
    const isStreaming = stream || req.headers.get('accept') === 'text/event-stream';

    console.log('DeepSeek API call details:', {
      url: DEEPSEEK_API_URL,
      model: 'deepseek-chat',
      messagesCount: messages.length,
      streaming: isStreaming,
      apiKeyPresent: !!DEEPSEEK_API_KEY
    });

    const apiRequestBody = {
      model: 'deepseek-chat',
      messages: messages,
      max_tokens: 1500,
      temperature: 0.7,
      stream: isStreaming
    };

    const response = await fetch(DEEPSEEK_API_URL, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiRequestBody)
    });

    console.log('DeepSeek API response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('DeepSeek API error:', {
        status: response.status,
        statusText: response.statusText,
        errorText: errorText
      });
      throw new Error(`DeepSeek API error: ${response.status} - ${errorText}`);
    }

    // Handle streaming response
    if (isStreaming) {
      return handleStreamingResponse(response, user_context, currentConversationId, message);
    }

    // Handle non-streaming response
    const data = await response.json();
    console.log('DeepSeek API response received');

    const aiResponse = data.choices?.[0]?.message?.content;

    if (!aiResponse) {
      console.error('No AI response in data:', data);
      throw new Error('No response from AI model');
    }

    // Parse action buttons from response
    const actions = parseActionButtons(aiResponse);
    const cleanResponse = aiResponse.replace(/\[ACTION:.*?\]/g, '').trim();

    // Generate contextual suggestions
    const suggestions = generateContextualSuggestions(user_context);

    const result: AIChatResponse = {
      response: cleanResponse,
      conversation_id: currentConversationId,
      actions,
      suggestions,
      metadata: {
        model: 'deepseek-chat',
        timestamp: new Date().toISOString()
      }
    };

    console.log('AI response prepared:', {
      responseLength: cleanResponse.length,
      actionsCount: actions.length,
      suggestionsCount: suggestions.length
    });

    // Store conversation in vector database
    if (user_context.is_authenticated && user_context.user_id) {
      try {
        await storeConversation(
          user_context.user_id,
          currentConversationId,
          message,
          cleanResponse,
          {
            profile_type: user_context.profile_type,
            current_page: user_context.current_page
          },
          actions,
          suggestions
        );
        console.log('Conversation stored successfully');
      } catch (error) {
        console.warn('Failed to store conversation:', error);
      }
    }

    return new Response(
      JSON.stringify(result),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error: any) {
    console.error('AI Chat error:', error);

    // Provide intelligent fallback based on user context
    const fallbackResponse = generateFallbackResponse(error.message, user_context);

    return new Response(
      JSON.stringify({
        response: fallbackResponse.response,
        actions: fallbackResponse.actions,
        suggestions: fallbackResponse.suggestions,
        error: 'AI service temporarily unavailable - system upgrade in progress',
        conversation_id: crypto.randomUUID()
      }),
      {
        status: 200, // Return 200 with fallback instead of error
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});

// Handle streaming response from DeepSeek API
async function handleStreamingResponse(
  response: Response,
  userContext: UserContext,
  conversationId: string,
  userMessage: string
): Promise<Response> {
  const encoder = new TextEncoder();
  const decoder = new TextDecoder();

  let fullResponse = '';

  const stream = new ReadableStream({
    async start(controller) {
      try {
        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error('No response body');
        }

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                // Send final message with actions and suggestions
                const actions = parseActionButtons(fullResponse);
                const cleanResponse = fullResponse.replace(/\[ACTION:.*?\]/g, '').trim();
                const suggestions = generateContextualSuggestions(userContext);

                // Store conversation
                if (userContext.is_authenticated && userContext.user_id) {
                  await storeConversation(
                    userContext.user_id,
                    conversationId,
                    userMessage,
                    cleanResponse,
                    { profile_type: userContext.profile_type, current_page: userContext.current_page },
                    actions,
                    suggestions
                  );
                }

                const finalData = JSON.stringify({
                  type: 'complete',
                  conversation_id: conversationId,
                  actions,
                  suggestions
                });
                controller.enqueue(encoder.encode(`data: ${finalData}\n\n`));
                controller.close();
                return;
              }

              try {
                const parsed = JSON.parse(data);
                const content = parsed.choices?.[0]?.delta?.content;
                if (content) {
                  fullResponse += content;
                  const streamData = JSON.stringify({
                    type: 'content',
                    content,
                    conversation_id: conversationId
                  });
                  controller.enqueue(encoder.encode(`data: ${streamData}\n\n`));
                }
              } catch (e) {
                // Skip invalid JSON
              }
            }
          }
        }
      } catch (error) {
        console.error('Streaming error:', error);
        const errorData = JSON.stringify({
          type: 'error',
          error: 'Streaming failed'
        });
        controller.enqueue(encoder.encode(`data: ${errorData}\n\n`));
        controller.close();
      }
    }
  });

  return new Response(stream, {
    headers: {
      ...corsHeaders,
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  });
}

function parseActionButtons(response: string): AIAction[] {
  const actionRegex = /\[ACTION:(.*?):(.*?):(.*?):(.*?):(.*?)\]/g;
  const actions: AIAction[] = [];
  let match;

  while ((match = actionRegex.exec(response)) !== null) {
    const [, type, label, icon, urlOrAction, color] = match;
    actions.push({
      type: type.trim() as AIAction['type'],
      label: label.trim(),
      icon: icon.trim(),
      url: type === 'navigation' || type === 'external' ? urlOrAction.trim() : undefined,
      action: type === 'action' ? urlOrAction.trim() : undefined,
      color: color.trim()
    });
  }

  return actions;
}

function generateContextualSuggestions(userContext: UserContext): string[] {
  const suggestions: string[] = [];

  if (!userContext.is_authenticated) {
    // Unauthenticated user suggestions
    suggestions.push(
      "How do I sign up for ZbInnovation?",
      "What are the benefits of joining the platform?",
      "Tell me about the innovation community",
      "How does the platform help innovators?",
      "Show me success stories from the platform"
    );
  } else {
    // Journey stage specific suggestions (highest priority)
    switch (userContext.journey_stage) {
      case 'new_user':
      case 'just_signed_up':
        suggestions.push(
          "How do I get started on the platform?",
          "What should I do first as a new member?",
          "Give me a platform tour"
        );
        break;
      case 'getting_started':
        suggestions.push(
          "How do I complete my profile effectively?",
          "What information should I include in my profile?",
          "How do I choose the right profile type?"
        );
        break;
      case 'building_profile':
        suggestions.push(
          "What should I add to improve my profile?",
          "How do I make my profile more attractive?",
          "What are the most important profile sections?"
        );
        break;
      case 'almost_complete':
        suggestions.push(
          "How do I finish my profile setup?",
          "What final touches will optimize my profile?",
          "How do I start networking effectively?"
        );
        break;
      case 'active_user':
        suggestions.push(
          "How can I grow my network on the platform?",
          "What advanced features should I explore?",
          "How do I maximize my platform presence?"
        );
        break;
    }

    // Profile completion specific suggestions
    if (userContext.missing_critical_fields && userContext.missing_critical_fields.length > 0) {
      const missingFields = userContext.missing_critical_fields.slice(0, 2).join(' and ')
      suggestions.push(`Help me add my ${missingFields} to my profile`)
    }

    if (userContext.milestone_achieved) {
      suggestions.push("What should I do now that I've reached this milestone?")
    }

    // Profile type specific suggestions
    switch (userContext.profile_type) {
      case 'innovator':
        if (userContext.profile_completion >= 70) {
          suggestions.push(
            "How do I find investors for my innovation?",
            "What makes a compelling innovation pitch?",
            "How do I connect with mentors in my field?"
          );
        } else {
          suggestions.push(
            "How do I showcase my innovation effectively?",
            "What should I include in my innovation description?"
          );
        }
        break;
      case 'investor':
        if (userContext.profile_completion >= 70) {
          suggestions.push(
            "How do I discover promising innovations?",
            "What should I look for in innovation projects?",
            "How do I connect with innovators?"
          );
        } else {
          suggestions.push(
            "How do I set up my investment criteria?",
            "What should I include in my investor profile?"
          );
        }
        break;
      case 'mentor':
        if (userContext.profile_completion >= 70) {
          suggestions.push(
            "How do I find mentees to guide?",
            "What are effective mentoring strategies?",
            "How do I share my expertise effectively?"
          );
        } else {
          suggestions.push(
            "How do I highlight my expertise areas?",
            "What should I include in my mentoring approach?"
          );
        }
        break;
      case 'student':
        suggestions.push(
          "How do I find mentors in my field?",
          "What learning opportunities are available?",
          "How do I build my professional network?"
        );
        break;
      case 'professional':
        suggestions.push(
          "How do I showcase my professional expertise?",
          "What collaboration opportunities exist?",
          "How do I connect with industry peers?"
        );
        break;
      default:
        suggestions.push(
          "How do I complete my profile?",
          "What opportunities are available for me?",
          "How do I get started on the platform?"
        );
    }

    // Page-specific suggestions (context-aware)
    switch (userContext.current_page) {
      case 'dashboard':
        if (userContext.profile_completion < 50) {
          suggestions.push("How do I complete my profile to unlock more features?");
        } else {
          suggestions.push(
            "How do I create my first post?",
            "What should I do next to grow my presence?",
            "How do I connect with other innovators?"
          );
        }
        break;
      case 'virtual-community':
        if (userContext.profile_completion >= 70) {
          suggestions.push(
            "Help me find relevant groups to join",
            "What events are coming up?",
            "How do I share my innovation story?",
            "How can I connect with mentors?"
          );
        } else {
          suggestions.push(
            "How do I prepare my profile for networking?",
            "What should I complete before joining groups?"
          );
        }
        break;
      case 'profile':
        if (userContext.completion_stage === 'empty' || userContext.completion_stage === 'basic') {
          suggestions.push(
            "What are the essential profile sections I need?",
            "How do I choose the right profile type?"
          );
        } else if (userContext.completion_stage === 'developing') {
          suggestions.push(
            "How can I make my profile more compelling?",
            "What additional information should I include?"
          );
        } else {
          suggestions.push(
            "How can I optimize my profile for maximum visibility?",
            "What advanced profile features should I use?"
          );
        }
        break;
      default:
        // Platform familiarity-based suggestions
        if (userContext.platform_familiarity === 'new') {
          suggestions.push(
            "What can I do on this platform?",
            "How do I get started?",
            "Give me a beginner's guide"
          );
        } else if (userContext.platform_familiarity === 'intermediate') {
          suggestions.push(
            "Show me advanced platform features",
            "How do I optimize my platform experience?",
            "What features am I not using yet?"
          );
        } else {
          suggestions.push(
            "What are the latest platform updates?",
            "How can I maximize my platform ROI?",
            "Show me power user tips"
          );
        }
    }

    // Interest-based suggestions
    if (userContext.interests && userContext.interests.length > 0) {
      const primaryInterest = userContext.interests[0]
      suggestions.push(`Help me find connections in ${primaryInterest}`)
    }

    // Recent activity-based suggestions
    if (userContext.recent_activity && userContext.recent_activity.includes('viewing_dashboard')) {
      suggestions.push("What should I focus on in my dashboard today?")
    }

    // Next steps from profile analysis
    if (userContext.next_steps && userContext.next_steps.length > 0) {
      const nextStep = userContext.next_steps[0]
      suggestions.push(`Help me: ${nextStep.toLowerCase()}`)
    }
  }

  // Remove duplicates and limit to 4 suggestions
  const uniqueSuggestions = [...new Set(suggestions)];
  return uniqueSuggestions.slice(0, 4);
}

function generateFallbackResponse(errorMessage: string, userContext: UserContext): AIChatResponse {
  const actions: AIAction[] = [];

  if (userContext.is_authenticated) {
    actions.push(
      { type: 'navigation', label: 'View Dashboard', icon: 'dashboard', url: '/dashboard', color: 'primary' },
      { type: 'navigation', label: 'Explore Community', icon: 'groups', url: '/virtual-community', color: 'secondary' }
    );
  } else {
    actions.push(
      { type: 'action', label: 'Sign Up', icon: 'person_add', action: 'signup', color: 'primary' },
      { type: 'navigation', label: 'Explore Community', icon: 'groups', url: '/virtual-community', color: 'secondary' }
    );
  }

  return {
    response: `I'm currently undergoing a system upgrade to serve you better. However, I'm here to help you navigate the ZbInnovation platform and connect with Zimbabwe's innovation community.

While I work on resolving this issue, you can explore the platform features using the buttons below, or try asking your question again in a moment.`,
    conversation_id: crypto.randomUUID(),
    actions,
    suggestions: [
      "What can I do on this platform?",
      "How do I get started?",
      "Tell me about the innovation community",
      "Help me navigate the platform"
    ],
    metadata: {
      fallback: true,
      error: errorMessage
    }
  };
}
