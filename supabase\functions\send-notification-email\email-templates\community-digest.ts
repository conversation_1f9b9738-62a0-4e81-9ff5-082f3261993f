/**
 * Community digest email template
 */

interface DigestData {
  newPosts: number
  newMembers: number
  topPosts: Array<{ title: string; url: string; likes: number }>
  featuredContent: Array<{ title: string; url: string; type: string }>
}

/**
 * Generates a community digest email
 * @param email The recipient's email
 * @param digestData The digest data including posts, members, etc.
 * @param firstName Optional first name of the recipient
 * @returns HTML and subject for the email
 */
export function generateCommunityDigestEmail(
  email: string,
  digestData: DigestData,
  firstName?: string
): { html: string; subject: string } {
  // Try to extract name from email if not provided
  const extractedName = !firstName ? extractNameFromEmail(email) : undefined;
  const greeting = firstName 
    ? `Hi ${firstName},` 
    : extractedName 
      ? `Hi ${extractedName},` 
      : 'Hi there,';

  const subject = 'Your Weekly ZB Innovation Hub Digest';

  // Generate top posts section
  const topPostsHtml = digestData.topPosts.length > 0 
    ? digestData.topPosts.map(post => `
        <div style="margin-bottom: 12px; padding: 12px; background-color: white; border-radius: 4px; border: 1px solid #e0e0e0;">
          <a href="${post.url}" style="color: #0D8A3E; text-decoration: none; font-weight: bold; font-size: 14px;">
            ${post.title.length > 80 ? post.title.substring(0, 80) + '...' : post.title}
          </a>
          <p style="margin: 4px 0 0 0; color: #666; font-size: 12px;">❤️ ${post.likes} likes</p>
        </div>
      `).join('')
    : '<p style="margin: 0; color: #666; font-style: italic;">No trending posts this week.</p>';

  // Generate featured content section
  const featuredContentHtml = digestData.featuredContent.length > 0
    ? digestData.featuredContent.map(content => `
        <div style="margin-bottom: 12px; padding: 12px; background-color: white; border-radius: 4px; border: 1px solid #e0e0e0;">
          <a href="${content.url}" style="color: #0D8A3E; text-decoration: none; font-weight: bold; font-size: 14px;">
            ${content.title.length > 80 ? content.title.substring(0, 80) + '...' : content.title}
          </a>
          <p style="margin: 4px 0 0 0; color: #666; font-size: 12px;">📝 ${content.type}</p>
        </div>
      `).join('')
    : '<p style="margin: 0; color: #666; font-style: italic;">No featured content this week.</p>';

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 24px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://zbinnovation.com/logo.png" alt="ZB Innovation Hub" style="max-width: 200px;">
      </div>

      <h1 style="color: #0D8A3E; margin-bottom: 16px;">${greeting}</h1>

      <p style="margin-bottom: 24px; line-height: 1.5;">
        Here's what's been happening in the ZB Innovation Hub community this week. Stay connected and discover new opportunities!
      </p>

      <!-- Community Stats -->
      <div style="background-color: #f0f8f4; padding: 20px; border-radius: 6px; margin: 24px 0;">
        <h2 style="color: #0D8A3E; margin-top: 0; margin-bottom: 16px; font-size: 18px;">📊 This Week's Activity</h2>
        <div style="display: flex; justify-content: space-around; text-align: center;">
          <div style="flex: 1;">
            <p style="margin: 0; font-size: 24px; font-weight: bold; color: #0D8A3E;">${digestData.newPosts}</p>
            <p style="margin: 4px 0 0 0; font-size: 14px; color: #666;">New Posts</p>
          </div>
          <div style="flex: 1;">
            <p style="margin: 0; font-size: 24px; font-weight: bold; color: #0D8A3E;">${digestData.newMembers}</p>
            <p style="margin: 4px 0 0 0; font-size: 14px; color: #666;">New Members</p>
          </div>
        </div>
      </div>

      <!-- Top Posts -->
      <div style="margin: 32px 0;">
        <h2 style="color: #0D8A3E; margin-bottom: 16px; font-size: 18px;">🔥 Trending Posts</h2>
        <div style="background-color: #f8f9fa; padding: 16px; border-radius: 6px;">
          ${topPostsHtml}
        </div>
      </div>

      <!-- Featured Content -->
      <div style="margin: 32px 0;">
        <h2 style="color: #0D8A3E; margin-bottom: 16px; font-size: 18px;">⭐ Featured Content</h2>
        <div style="background-color: #f8f9fa; padding: 16px; border-radius: 6px;">
          ${featuredContentHtml}
        </div>
      </div>

      <!-- Call to Action -->
      <div style="text-align: center; margin: 32px 0;">
        <a href="https://zbinnovation.com/innovation-community"
           style="background-color: #0D8A3E; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
          Explore Community
        </a>
      </div>

      <!-- Tips Section -->
      <div style="background-color: #f0f8f4; padding: 16px; border-radius: 6px; margin: 24px 0;">
        <p style="margin: 0; line-height: 1.5; color: #0D8A3E; font-weight: bold;">💡 Ways to Get More Involved:</p>
        <ul style="margin: 8px 0 0 0; padding-left: 20px; line-height: 1.5; color: #666; font-size: 14px;">
          <li>Share your latest projects or insights</li>
          <li>Comment on posts that interest you</li>
          <li>Connect with new community members</li>
          <li>Join discussions in your areas of expertise</li>
        </ul>
      </div>

      <div style="border-top: 1px solid #eee; padding-top: 24px; margin-top: 32px;">
        <p style="margin-bottom: 8px; line-height: 1.5;">
          Best regards,<br>
          The ZB Innovation Hub Team
        </p>
      </div>

      <div style="margin-top: 24px; padding: 16px; background-color: #f8f9fa; border-radius: 6px; font-size: 12px; color: #666;">
        <p style="margin: 0; text-align: center;">
          © ${new Date().getFullYear()} ZB Innovation Hub. All rights reserved.<br>
          This email was sent to ${email}
        </p>
        <p style="margin: 8px 0 0 0; text-align: center;">
          <a href="#" style="color: #0D8A3E; text-decoration: none;">Manage Email Preferences</a> | 
          <a href="#" style="color: #0D8A3E; text-decoration: none;">Unsubscribe</a>
        </p>
      </div>
    </div>
  `;

  return { html, subject };
}

/**
 * Helper function to extract name from email
 * @param email The email address
 * @returns Extracted name or null
 */
function extractNameFromEmail(email: string): string | null {
  const localPart = email.split('@')[0];
  
  // Handle common patterns like firstname.lastname or firstname_lastname
  if (localPart.includes('.') || localPart.includes('_')) {
    const parts = localPart.split(/[._]/);
    if (parts.length >= 2) {
      return parts[0].charAt(0).toUpperCase() + parts[0].slice(1);
    }
  }
  
  // Handle camelCase like firstnameLastname
  const camelCaseMatch = localPart.match(/^([a-z]+)([A-Z][a-z]+)/);
  if (camelCaseMatch) {
    return camelCaseMatch[1].charAt(0).toUpperCase() + camelCaseMatch[1].slice(1);
  }
  
  // If no pattern found, capitalize the first letter
  if (localPart.length > 0) {
    return localPart.charAt(0).toUpperCase() + localPart.slice(1);
  }
  
  return null;
}
