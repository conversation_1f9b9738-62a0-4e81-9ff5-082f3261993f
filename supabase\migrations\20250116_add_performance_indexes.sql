-- Performance Optimization Indexes
-- Created: 2025-01-16
-- Purpose: Add database indexes to improve query performance

-- ============================================================================
-- POSTS TABLE INDEXES
-- ============================================================================

-- Index for posts feed queries (most common query pattern)
-- Covers: ORDER BY created_at DESC with status filtering
CREATE INDEX IF NOT EXISTS idx_posts_created_at_status 
ON posts (created_at DESC, status) 
WHERE status = 'published';

-- Index for user posts queries
-- Covers: WHERE user_id = ? ORDER BY created_at DESC
CREATE INDEX IF NOT EXISTS idx_posts_user_created_at 
ON posts (user_id, created_at DESC);

-- Index for post type filtering
-- Covers: WHERE post_type = ? AND sub_type = ?
CREATE INDEX IF NOT EXISTS idx_posts_type_subtype 
ON posts (post_type, sub_type);

-- Index for slug lookups (single post views)
-- Covers: WHERE slug = ?
CREATE INDEX IF NOT EXISTS idx_posts_slug 
ON posts (slug) 
WHERE slug IS NOT NULL;

-- Index for tag searches (if using GIN for array operations)
-- Covers: WHERE tags @> ARRAY['tag']
CREATE INDEX IF NOT EXISTS idx_posts_tags_gin 
ON posts USING GIN (tags) 
WHERE tags IS NOT NULL;

-- ============================================================================
-- PERSONAL_DETAILS TABLE INDEXES
-- ============================================================================

-- Index for profile discovery queries
-- Covers: WHERE profile_visibility = 'public' ORDER BY profile_completion DESC
CREATE INDEX IF NOT EXISTS idx_personal_details_visibility_completion 
ON personal_details (profile_visibility, profile_completion DESC) 
WHERE profile_visibility = 'public';

-- Index for profile type filtering
-- Covers: WHERE profile_type = ? AND profile_visibility = 'public'
CREATE INDEX IF NOT EXISTS idx_personal_details_type_visibility 
ON personal_details (profile_type, profile_visibility) 
WHERE profile_visibility = 'public';

-- Index for profile search queries
-- Covers: Text search on names and bio
CREATE INDEX IF NOT EXISTS idx_personal_details_search_text 
ON personal_details USING GIN (
  to_tsvector('english', 
    COALESCE(first_name, '') || ' ' || 
    COALESCE(last_name, '') || ' ' || 
    COALESCE(bio, '')
  )
);

-- ============================================================================
-- COMMENTS TABLE INDEXES
-- ============================================================================

-- Index for post comments queries
-- Covers: WHERE post_id = ? ORDER BY created_at
CREATE INDEX IF NOT EXISTS idx_comments_post_created_at 
ON comments (post_id, created_at);

-- Index for user comments queries
-- Covers: WHERE user_id = ? ORDER BY created_at DESC
CREATE INDEX IF NOT EXISTS idx_comments_user_created_at 
ON comments (user_id, created_at DESC);

-- ============================================================================
-- LIKES TABLE INDEXES
-- ============================================================================

-- Index for like existence checks
-- Covers: WHERE post_id = ? AND user_id = ?
CREATE INDEX IF NOT EXISTS idx_likes_post_user 
ON likes (post_id, user_id);

-- Index for post like counts
-- Covers: WHERE post_id = ? (for counting)
CREATE INDEX IF NOT EXISTS idx_likes_post_id 
ON likes (post_id);

-- Index for user likes queries
-- Covers: WHERE user_id = ? ORDER BY created_at DESC
CREATE INDEX IF NOT EXISTS idx_likes_user_created_at 
ON likes (user_id, created_at DESC);

-- ============================================================================
-- USER_NOTIFICATIONS TABLE INDEXES
-- ============================================================================

-- Index for user notifications queries
-- Covers: WHERE user_id = ? AND is_read = ? ORDER BY created_at DESC
CREATE INDEX IF NOT EXISTS idx_user_notifications_user_read_created 
ON user_notifications (user_id, is_read, created_at DESC);

-- Index for unread count queries
-- Covers: WHERE user_id = ? AND is_read = false
CREATE INDEX IF NOT EXISTS idx_user_notifications_user_unread 
ON user_notifications (user_id) 
WHERE is_read = false;

-- ============================================================================
-- USER_MESSAGES TABLE INDEXES
-- ============================================================================

-- Index for conversation queries
-- Covers: WHERE (sender_id = ? AND recipient_id = ?) OR (sender_id = ? AND recipient_id = ?)
CREATE INDEX IF NOT EXISTS idx_user_messages_conversation 
ON user_messages (
  LEAST(sender_id, recipient_id), 
  GREATEST(sender_id, recipient_id), 
  created_at
);

-- Index for user message queries
-- Covers: WHERE sender_id = ? OR recipient_id = ?
CREATE INDEX IF NOT EXISTS idx_user_messages_sender 
ON user_messages (sender_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_user_messages_recipient 
ON user_messages (recipient_id, created_at DESC);

-- ============================================================================
-- USER_CONNECTIONS TABLE INDEXES
-- ============================================================================

-- Index for user connections queries
-- Covers: WHERE user_id = ? AND status = ?
CREATE INDEX IF NOT EXISTS idx_user_connections_user_status 
ON user_connections (user_id, status);

-- Index for connection lookups
-- Covers: WHERE connected_user_id = ? AND status = ?
CREATE INDEX IF NOT EXISTS idx_user_connections_connected_status 
ON user_connections (connected_user_id, status);

-- Composite index for bidirectional connection checks
-- Covers: Complex connection existence queries
CREATE INDEX IF NOT EXISTS idx_user_connections_bidirectional 
ON user_connections (
  LEAST(user_id, connected_user_id), 
  GREATEST(user_id, connected_user_id), 
  status
);

-- ============================================================================
-- SPECIALIZED PROFILE TABLES INDEXES
-- ============================================================================

-- Indexes for specialized profile tables (innovator, investor, etc.)
-- These support profile matching and discovery queries

-- Innovator profiles
CREATE INDEX IF NOT EXISTS idx_innovator_profiles_industry 
ON innovator_profiles USING GIN (industry) 
WHERE industry IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_innovator_profiles_stage 
ON innovator_profiles (innovation_stage);

-- Investor profiles  
CREATE INDEX IF NOT EXISTS idx_investor_profiles_focus 
ON investor_profiles USING GIN (investment_focus) 
WHERE investment_focus IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_investor_profiles_stage 
ON investor_profiles USING GIN (investment_stage) 
WHERE investment_stage IS NOT NULL;

-- Professional profiles
CREATE INDEX IF NOT EXISTS idx_professional_profiles_industry 
ON professional_profiles USING GIN (industry) 
WHERE industry IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_professional_profiles_skills 
ON professional_profiles USING GIN (skills) 
WHERE skills IS NOT NULL;

-- ============================================================================
-- PERFORMANCE MONITORING
-- ============================================================================

-- Add comments for documentation
COMMENT ON INDEX idx_posts_created_at_status IS 'Optimizes posts feed queries with status filtering';
COMMENT ON INDEX idx_posts_user_created_at IS 'Optimizes user posts queries';
COMMENT ON INDEX idx_personal_details_visibility_completion IS 'Optimizes profile discovery queries';
COMMENT ON INDEX idx_comments_post_created_at IS 'Optimizes post comments queries';
COMMENT ON INDEX idx_likes_post_user IS 'Optimizes like existence checks';
COMMENT ON INDEX idx_user_notifications_user_read_created IS 'Optimizes notification queries';
COMMENT ON INDEX idx_user_messages_conversation IS 'Optimizes conversation queries';
COMMENT ON INDEX idx_user_connections_user_status IS 'Optimizes connection queries';

-- Create a function to analyze index usage
CREATE OR REPLACE FUNCTION analyze_index_usage()
RETURNS TABLE (
  schemaname text,
  tablename text,
  indexname text,
  idx_scan bigint,
  idx_tup_read bigint,
  idx_tup_fetch bigint
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.schemaname::text,
    s.tablename::text,
    s.indexname::text,
    s.idx_scan,
    s.idx_tup_read,
    s.idx_tup_fetch
  FROM pg_stat_user_indexes s
  WHERE s.schemaname = 'public'
  ORDER BY s.idx_scan DESC;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION analyze_index_usage() IS 'Analyzes index usage statistics for performance monitoring';
