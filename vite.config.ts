import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { quasar, transformAssetUrls } from '@quasar/vite-plugin'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  // Base URL for Apache deployment
  base: '/',

  plugins: [
    vue({
      template: { transformAssetUrls }
    }),
    quasar({
      sassVariables: 'src/css/quasar.variables.scss'
    })
  ],

  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src')
    }
  },

  build: {
    // Output directory
    outDir: 'dist',

    // Optimize for Apache deployment
    assetsDir: 'assets',

    // Target modern browsers
    target: 'es2020',

    // Chunk size warning limit
    chunkSizeWarningLimit: 600,

    // Remove source maps for production
    sourcemap: false,

    // Minification settings
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug']
      },
      mangle: {
        safari10: true
      }
    },

    // Rollup options for code splitting
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunk for core dependencies
          vendor: ['vue', 'vue-router', 'pinia'],

          // UI framework chunk
          ui: ['quasar'],

          // Supabase chunk
          supabase: ['@supabase/supabase-js'],

          // Utilities chunk
          utils: ['dompurify', 'fuse.js']
        }
      }
    },

    // Asset inline limit (4KB)
    assetsInlineLimit: 4096
  },

  // Dependency optimization
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      'quasar',
      '@supabase/supabase-js'
    ]
  },

  // Development server optimization
  server: {
    hmr: {
      overlay: false
    }
  }
})
